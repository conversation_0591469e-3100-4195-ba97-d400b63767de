<script setup lang="ts">
import FormSwitch from "@/components/FormSwitch.vue";
import MyFlipper from "@/components/MyFlipper.vue";
import CmpButton from "@/components/CmpButton.vue";
import CheckDrawer from "@/view/task/components/CheckDrawer.vue";
import {
  MAX_PAGESIZE,
  taskCompleteType,
  taskCompleteTypeDict,
} from "@/utils/constant";
import { getMyTaskListApi } from "@/apis/path/task";
import type { params2GetTaskList } from "@/apis/path/task";
import { onMounted, ref } from "vue";

const drawerRef = ref(); // drawer
const currentPage = ref(1); // 当前页
const pageSize = MAX_PAGESIZE; // 页大小
const tableData = ref([]); // 表格数据
const total = ref(0); // 总数

// 获取我的任务列表
const getMyTaskList = (
  current: number,
  status?: number,
  domain?: string,
  task?: string
) => {
  let params: params2GetTaskList = {
    current: current ? current : currentPage.value,
    limit: pageSize,
  };
  if (status !== -1 && status) params.status = status;
  if (domain) params.areaTitle = domain;
  if (task) params.taskTitle = task;

  getMyTaskListApi(params).then((res) => {
    if (res.success) {
      tableData.value = res.data.list;
      total.value = res.data.total;
    }
  });
};
// 处理我的任务列表更新
const handleRefreshList = (form?: any) => {
  if (form) {
    getMyTaskList(
      currentPage.value,
      form.taskStatus,
      form.domainTitle,
      form.taskTitle
    );
  } else {
    getMyTaskList(currentPage.value);
  }
};
// 计算列表index
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize + index + 1;
};

// 处理换页
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  getMyTaskList(newPage);
};
// 处理查看
const handleCheck = (task: any, mode: number) => {
  // -1: 根据状态确定 || 0:taskList || 1: mytask || 2:conduct || 3: 已执行 || 4: 已完成 || 5:已退回
  drawerRef.value.showDrawer(task, mode);
};
onMounted(() => {
  getMyTaskList(currentPage.value);
});
</script>
<template>
  <div class="wrapper">
    <div class="header-wrapper">
      <form-switch
        :need-task-complete="true"
        :need-domain="true"
        :need-search="true"
        @task-list="handleRefreshList"
      ></form-switch>
      <div class="line"></div>
    </div>
    <div class="main-wrapper" style="margin-top: 50px">
      <div class="line"></div>
      <el-table
        class="table"
        :data="tableData"
        style="width: 100%"
        empty-text="暂无数据"
        :row-style="{ height: '50px', overflow: 'hidden' }"
      >
        <el-table-column
          type="index"
          :index="indexMethod"
          label="序号"
          width="60"
          align="center"
        />
        <el-table-column prop="klgName" label="任务名称" width="300">
          <template #default="scope">
            <span class="ck-content" v-html="scope.row.klgName"></span>
          </template>
        </el-table-column>
        <el-table-column
          prop="areaTitle"
          label="所属领域"
          width="180"
          align="center"
        />
        <el-table-column
          prop="createTime"
          label="提交时间"
          width="180"
          align="center"
        />
        <el-table-column
          prop="creatorName"
          label="提交人"
          width="180"
          align="center"
        />
        <el-table-column label="任务状态" width="150" align="center">
          <template #default="scope">
            {{
              Object.keys(taskCompleteTypeDict).find(
                (key) => taskCompleteTypeDict[key] === scope.row.taskStatus
              )
            }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="130" align="center">
          <template #default="scope">
            <span class="operation">
              <span
                class="op-btn"
                v-if="scope.row.taskStatus === taskCompleteType.assigned"
                @click="handleCheck(scope.row, 2)"
              >
                执行</span
              >
              <span class="op-btn" v-else @click="handleCheck(scope.row, -1)">
                查看
              </span>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <my-flipper
        @change-page="handleChangePage"
        :current="currentPage"
        :page-size="pageSize"
        :total="total"
      ></my-flipper>
    </div>
  </div>
  <!-- other -->
  <CheckDrawer ref="drawerRef" @refresh="handleRefreshList"></CheckDrawer>
</template>
<style scoped>
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
.wrapper {
  width: 1200px;
  min-height: 750px;
  background-color: white;
  font-family: var(--text-family);

  .header-wrapper {
    width: 100%;
  }
  .main-wrapper {
    padding: 0 10px;

    .table {
      --el-color-primary: var(--color-primary);

      .operation {
        padding: 0 10px;

        .op-btn {
          cursor: pointer;
          color: var(--color-primary);

          &:hover {
            font-weight: bold;
          }
        }
      }
    }
  }
}
</style>
