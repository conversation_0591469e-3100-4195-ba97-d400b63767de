import { createAxiosByinterceptors } from './request';
import type { AxiosInstance } from 'axios';

let http: AxiosInstance // 请求CRUD
let http_info: AxiosInstance // 用户获取用户信息
let http_auth: AxiosInstance
;(function () {
    http = createAxiosByinterceptors({
        baseURL: import.meta.env.VITE_APP_KSG_API_BASE
    })
    http_info = createAxiosByinterceptors({
        baseURL: import.meta.env.VITE_APP_INFO_API_BASE
    })
    http_auth = createAxiosByinterceptors({
        baseURL: import.meta.env.VITE_APP_AUTH_API_BASE
    })
})()

export { http, http_info, http_auth }