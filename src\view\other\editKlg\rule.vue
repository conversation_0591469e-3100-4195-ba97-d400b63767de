<script setup lang="ts"></script>
<template>
  <div class="wrapper">
    <div class="header-wrapper">
      <span class="title">无尽本源知识整理编者规范</span>
    </div>
    <div class="line"></div>
    <div class="form-container">
    <!-- TODO: 修改RULE -->
      <p>
        <p>为保证知识整理的规范性，各知识编辑者需要遵循以下规范。</p>
        <p style="padding-left: 20px;">1）所有的名词都有相对应的概念定义知识点，如果没有，需要在编者笔记中指明；</p>
        <p style="padding-left: 20px;">2）每个原理性的知识点都配有对应的逻辑推理，且逻辑推理没有错误；</p>
        <p style="padding-left: 20px;">3）每一步逻辑推理都有对应的基础原理知识点作为支撑，如果没有，需要在编者笔记中指明；</p>
        <p style="padding-left: 20px;">4）文本中不能存在多余的空格；</p>
        <p style="padding-left: 20px;">5）文本中不能存在英文的逗号，句号，分号等等；</p>
        <p style="padding-left: 20px;">6）每个知识点的编者笔记中都对该知识点做一些说明，包括前面提到的欠缺的前驱知识点，以及对该知识点的直观解释。</p>
        <p>另外关于概念的定义形式，给出如下的说明：</p>
        <p>概念指称事或者物，下面分别针对这两种情况做一下讨论：</p>
        <p style="padding-left: 20px;">1）如果这个概念性知识点指称的是一类事（现象），那么需要提前给出一个原理性的知识，该原理性知识指出在某种条件下，会发生该现象，然后在这个概念性知识点中，引用该原理性知识点，并且声明将该现象命名为某种名称；</p>
        <p style="padding-left: 20px;">2）如果这个概念性知识点指称的是一类物，定义方式通常为以下几种： </p>
        <p style="padding-left: 40px;">a）属 x 种差定义法：分三步进行。找出与被定义概念相邻近的属概念，再找出被定义概念与该邻近属概念下其他种概念之间的差别（种差），再将被定义项、种差和邻近的属概念用 “与关系，即同时满足”连接起来形成定义。“ 三角形 ”（概念A）与“等腰三角形
        ”（概念B）两个概念中，三角形是属，等腰三角形是种，而“有两条边相等”（概念B具有而概念A不具有）就是“三角形”与“等腰三角形”的种差。
        定义项 是由被定义概念的邻近的属和种差所组成的定义。
        一般地，属加种差的定义方式可以由公式表示：被定义项=种差 X（与）
        邻近的属。 例如：矩形的定义：有一个角是直角的 平行四边形 叫矩形。
        用公式表示为：矩形=有一个角是直角（种差）X（与）</p>
        平行四边形（属）。如果采用这种方式来定义，需要保证，邻近属的概念定义已经给出，种差中所用到的概念定义也都已经给出。比如像前面等腰三角形的定义，就需要保证三角形、边、相等这几个概念都已经提前给出。再比如矩形的定义，就需要保证角、直角、平行四边形这几个概念都已经提前给出。
        <p style="padding-left: 40px;">b）同义定义：这是通过表达同一个概念的不同词语来明确概念的定义方法。这种定义方式被我们用同义词功能来实现了；</p>
        <p style="padding-left: 40px;">c）枚举定义：把一个更大类的概念中所有的成分都列出来的方式来定义这个更大类的概念；比如本征电子和本征空穴统称为本征载流子；如果采用这种定义方式，就需要保证本征电子和本征空穴都已经提前给出定义；</p>
        <p style="padding-left: 40px;">d）递归定义：某个概念的外延当且仅当有限次使用下列两条规则产生。规则一：确定某些对象属于这个概念的外延。规则二：如果一些对象属于这个概念的外延，那么由这些对象按照固定的程序产生的新的对象也属于这个概念的外延。这种定义方式很少见，通常在语言类知识的语法定义中使用。通常可以在语言类知识的标准文档中直接找到，所以不太需要我们自己去整理。</p>
    </p>
    </div>
  </div>
</template>
<style scoped>
.wrapper {
  width: 1200px;
  background-color: white;
  padding-bottom: 30px;
  .header-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    .title {
        font-size: 28px;
        margin: 10px;
    }

  }
  .form-container {
    padding: 0 100px;
  }
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
  margin-bottom: 15px;
}

</style>
