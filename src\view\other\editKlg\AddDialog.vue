<script setup lang="ts">
import MyFlipper from '@/components/MyFlipper.vue';
import CmpButton from '@/components/CmpButton.vue';
import { nextTick, ref, watch } from 'vue';
import { getPubRefsApi } from '@/apis/path/document';
import { getFullTreeApi, getTreeApi } from '@/apis/path/domain';
import type { params2GetPubRef } from '@/apis/path/document';
import { MAX_PAGESIZE } from '@/utils/constant';
import { RefListItem, Tree } from '@/utils/type';
import { ElTable, ElTree } from 'element-plus';
import { transformToTreeNode } from '@/utils/func';

const dialogVisible = ref(false);
const currentPage = ref(1);
const pageSize = MAX_PAGESIZE;
const total = ref(0);
const tableRef = ref<InstanceType<typeof ElTable>>();
const treeRef = ref<InstanceType<typeof ElTree>>();
const curMode = ref(0); //  1: 所属领域 | 2: 参考文献
const titleText = ref(''); // dialog标题
const tableData = ref(); // 表格数据
const treeData = ref<Tree[]>([]); // 树数据
const searchKey = ref(''); // 关键词
const selectionRefList = ref<any[]>([]); // 选择的Ref列表
const defaultList = ref<any[]>([]); // 默认展开列表or默认选择列表
const emits = defineEmits(['editArea', 'editRef']);

// 展示dialog
const showDialog = (mode: number, list: any[]) => {
  resetData();
  curMode.value = mode;
  dialogVisible.value = true;
  defaultList.value = list;
  switch (mode) {
    case 1:
      titleText.value = '所属领域';
      getFullTree();
      break;
    case 2:
      titleText.value = '参考文献';
      getPubRefList();
      break;
  }
};
// 重置
const resetData = () => {
  searchKey.value = '';
  defaultList.value = [];
};
// 获取发布文献
const getPubRefList = () => {
  const params: params2GetPubRef = {
    current: currentPage.value,
    limit: pageSize,
    cntName: searchKey.value,
    refSort: '',
    status: 1
  };
  getPubRefsApi(params).then((res) => {
    if (res.success) {
      tableData.value = res.data.refVoList;
      total.value = res.data.total;
      nextTick(() => {
        setTableCheck();
      });
    }
  });
};
// 获取树
const getFullTree = () => {
  getTreeApi(1).then((res) => {
    if (res.data.tree) {
      treeData.value = [transformToTreeNode(res.data.tree[0])];
      setTreeCheck(treeData.value[0]);
    }
  });
  // getFullTreeApi().then((res) => {
  //   if (res.success) {
  //     treeData.value = [transformToTreeNode(res.data.tree[0])]
  //     setTreeCheck(treeData.value[0])
  //   }
  // })
};
// 设置表格默认勾选
const setTableCheck = () => {
  defaultList.value.forEach((item) => {
    tableData.value.forEach((row) => {
      if (row.id === item.refId) {
        tableRef.value?.toggleRowSelection(row, true);
      }
    });
  });
};
// 设置树默认勾选
const setTreeCheck = (data: Tree) => {
  if (defaultList.value.includes(data.areaCode)) {
    data.checked = true;
    disCheckBox(data);
  }
  if (data.children) {
    data.children.forEach((node) => {
      setTreeCheck(node);
    });
  }
};
// 获取树勾选
const getTreeCheck = (data: Tree) => {
  const checkList = ref<any[]>([]);
  const treeCheck = (data: Tree) => {
    if (data.checked) {
      checkList.value.push({
        areaCode: data.areaCode,
        label: data.label
      });
    }
    if (data.children) {
      data.children.forEach((item) => {
        treeCheck(item);
      });
    }
  };
  treeCheck(data);
  return checkList.value;
};

// 处理提交
const handleSubmit = () => {
  if (curMode.value === 1) {
    const list = getTreeCheck(treeData.value[0]);
    emits('editArea', list);
  } else if (curMode.value === 2) {
    emits('editRef', selectionRefList.value);
  }
  dialogVisible.value = false;
};
// 处理表格选择
const handleSelectionChange = (list: []) => {
  selectionRefList.value = [];
  list.forEach((item) => {
    const index = defaultList.value.findIndex((defaultItem) => defaultItem.refId === item.id);
    if (index !== -1) {
      selectionRefList.value.push(defaultList.value[index]);
    } else {
      selectionRefList.value.push({
        refId: item.id,
        cntName: item.cntName,
        indexPage: ''
      });
    }
  });
};
// 处理换页
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  getPubRefList();
};
// 计算列表index
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * MAX_PAGESIZE + index + 1;
};
// 过滤树
const filterTree = (value: string, data: Tree) => {
  if (!value) return true;
  return data.label.includes(value);
};
// 找到祖先
const findAncestors = (curNode: any): any[] => {
  const ancestors: any[] = [];
  const node = treeRef.value?.getNode(curNode.areaCode);
  const setAncestorsList = (node: any) => {
    if (node.parent) {
      ancestors.push(node.parent.data);
      setAncestorsList(node.parent);
    }
  };
  setAncestorsList(node);
  // console.log("node", node)
  // ancestors.push(treeData.value[0])
  // let findFlag = false
  // const setAncestorsList = (data: any) =>{
  //   let tempAncestor = data.children[0]
  //   if(data.children || data.children.length !== 0) {
  //     for(const node of data.children) {
  //       // console.log("node", node)
  //       if(findFlag) {
  //         ancestors.push(tempAncestor)
  //         break
  //       }
  //       if(node.areaCode === curNode.areaCode) {
  //         findFlag = true
  //         break
  //       } else {
  //         setAncestorsList(node)
  //       }
  //       tempAncestor = node
  //     }
  //   }

  // }
  // setAncestorsList(treeData.value[0])
  return ancestors;
};

// 设置checkbox不可选
const disCheckBox = (data: any) => {
  const setFather = (data: any, value: boolean) => {
    const ancestors = findAncestors(data);
    console.log('ances', ancestors);
    ancestors.forEach((item) => {
      item.showCheckBox = value;
    });
  };
  const setChilren = (data: any, value: boolean) => {
    data.children.forEach((item) => {
      item.showCheckBox = value;
      setChilren(item, value);
    });
  };
  setChilren(data, !data.checked);
  setFather(data, !data.checked);
};
watch(searchKey, (val) => {
  if (curMode.value === 1 && treeRef) {
    treeRef.value!.filter(val);
  }
});

defineExpose({
  showDialog
});
</script>
<template>
  <el-dialog v-model="dialogVisible" width="1000" style="height: 700px">
    <template #header> {{ titleText }} </template>
    <div class="main-container">
      <div class="tree-container" v-if="curMode === 1">
        <el-input
          v-model="searchKey"
          placeholder="请输入查询内容"
          style="margin-bottom: 5px"
          suffix-icon="Search"
        />
        <el-tree
          ref="treeRef"
          class="filter-tree"
          node-key="areaCode"
          :data="treeData"
          :filter-node-method="filterTree"
          :expand-on-click-node="false"
          :default-expanded-keys="defaultList"
        >
          <template #default="{ node, data }">
            <span class="tree-node-block">
              <span class="tree-node-label">{{ node.label }}</span>
              <span class="tree-node-checkbox"
                ><el-checkbox
                  v-if="data.showCheckBox"
                  :disabled="data.disable"
                  v-model="data.checked"
                  @change="disCheckBox(data)"
                ></el-checkbox
              ></span>
            </span>
          </template>
        </el-tree>
      </div>
      <div class="ref-container" v-else-if="curMode === 2">
        <div class="ref-header">
          <el-input
            v-model="searchKey"
            style="width: 300px"
            placeholder="请输入内容名称"
            @keydown.enter="getPubRefList"
          >
            <template #suffix>
              <el-icon @click="getPubRefList" style="cursor: pointer"><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <div class="line"></div>
        <el-table
          ref="tableRef"
          :data="tableData"
          style="width: 100%; min-height: 460px"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="40" />
          <el-table-column
            type="index"
            :index="indexMethod"
            label="序号"
            width="60"
            align="center"
          />
          <el-table-column label="内容名称" prop="cntName" width="368">
            <template #default="scope">
              <el-tooltip
                placement="top"
                :content="scope.row.cntName"
                :raw-content="true"
                :show-after="200"
                effect="customized"
              >
                <span class="ellipsis-text-inline" v-html="scope.row.cntName"></span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="文献类型" prop="refSort" width="150" align="center" />
          <el-table-column label="创建者" prop="creator" width="150" align="center" />
          <el-table-column label="创建时间" prop="createTime" width="200" align="center" />
        </el-table>
        <my-flipper
          @change-page="handleChangePage"
          :current="currentPage"
          :page-size="pageSize"
          :total="total"
        ></my-flipper>
      </div>
    </div>
    <template #footer>
      <div class="footer">
        <CmpButton type="info" @click="dialogVisible = false">关闭</CmpButton>
        <cmp-button type="primary" @click="handleSubmit"> 确定 </cmp-button>
      </div>
    </template>
  </el-dialog>
</template>
<style scoped>
.main-container {
  height: 560px;
  .tree-container {
    padding: 10px;
    background-color: var(--color-second);
    .filter-tree {
      height: 500px;
      background-color: var(--color-second);
      overflow: hidden;
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 3px;
      }
      &::-webkit-scrollbar-thumb {
        background-color: #888;
        border-radius: 6px;
        height: 50px;
      }
      &::-webkit-scrollbar-track {
        background-color: none;
      }
      .tree-node-block {
        display: flex;
        align-items: center;
        .tree-node-label {
          background-color: white;
          padding: 2px 10px;
        }
        .tree-node-checkbox {
          margin-left: 10px;
        }
      }
    }
  }
  .ref-container {
    .ref-header {
      display: flex;
      justify-content: flex-end;
    }
  }
}
.footer {
  display: flex;
  justify-content: center;
  gap: 40px;
}
.line {
  margin: 10px 0;
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
</style>
