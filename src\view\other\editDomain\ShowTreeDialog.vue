<script setup lang="ts">
import { ref } from "vue"
import { Tree, Role } from "@/utils/type"
const dialogVisible = ref(false)
const curRole = ref<Role>()
const curTree = ref<Tree>()
// 展示dialog
const showDialog = (role: Role, tree: Tree) => {
  dialogVisible.value = true
  curRole.value = role
  curTree.value = tree
}
defineExpose({
  showDialog,
})
</script>
<template>
  <el-dialog v-model="dialogVisible" title="角色领域配置预览" width="800">
    <el-form class="main-content">
      <el-form-item label="角色名称">
        <div class="role-title">{{ curRole?.title }}</div>
      </el-form-item>
      <el-form-item label="领域权限">
        <div class="domain-content">
          <div class="tree-content">
            <el-tree
              :height="500"
              class="tree-body"
              :data="curTree"
              node-key="areaCode"
              :expand-on-click-node="false"
              default-expand-all
            >
              <template #default="{ node, data }">
                <span class="tree-node">
                  <span class="tree-node-title tree-node-box">
                    <span>{{ node.label }}</span>
                  </span>
                  <span class="tree-node-box" v-if="data.isTag === 1">
                    <el-checkbox
                      v-model="data.isEditKlg"
                      :disabled="true"
                    ></el-checkbox>
                    <span style="margin-left: 10px">知识查看</span>
                  </span>
                  <span class="tree-node-box" v-if="data.isTag === 1">
                    <el-checkbox
                      v-model="data.isEditArea"
                      :disabled="true"
                    ></el-checkbox>
                    <span style="margin-left: 10px">领域编辑</span>
                  </span>
                </span>
              </template>
            </el-tree>
          </div>
        </div>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>
<style scoped>
.main-content {
  max-height: 600px;
  padding: 20px;

  &::-webkit-scrollbar {
    width: 3px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: #888;
    border-radius: 6px;
    height: 50px;
  }
  &::-webkit-scrollbar-track {
    background-color: #f1f1f1;
  }
  .role-title {
    width: 100%;
    padding: 4px 20px;
    margin-left: 20px;
    background-color: var(--color-light);
    border: 1px solid var(--color-boxborder);
  }
  .domain-content {
    width: 100%;
    .tree-content {
      margin-left: 20px;
      margin-top: 10px;
      width: 100%;
      /* height: 100%; */
      height: 500px;
      background-color: var(--color-light);
      overflow: auto;
      border: none;
      &::-webkit-scrollbar {
        width: 5px;
        height: 5px;
      }
      &::-webkit-scrollbar-thumb {
        background-color: #888;
        border-radius: 6px;
        height: 5px;
        width: 5px;
      }
      &::-webkit-scrollbar-track {
        background-color: #f1f1f1;
      }
      .tree-body {
        padding: 10px 0;
        height: 250px;
        border: none;
        display: inline-block;
        background-color: var(--color-light);
        .tree-node {
          display: flex;
          height: 100%;
          align-items: center;
          .tree-node-title {
            background-color: white;
            padding: 0 10px;
            height: 85%;
          }
          .tree-node-box {
            margin-left: 10px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
          }
        }
      }
    }
  }
}
</style>
