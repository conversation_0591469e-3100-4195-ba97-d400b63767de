<script lang="ts" setup>
import CmpButton from '@/components/CmpButton.vue';
import inlineCKEditor from '@/components/editors/VeditorInline.vue';
import { taskCompleteTypeDict, KlgType, KlgTypeDict, taskCompleteType } from '@/utils/constant';
import { ElMessage, FormInstance, FormRules } from 'element-plus';
import { executeTaskApi, getKlgsApi, returnTaskApi, releaseKlgApi } from '@/apis/path/task';
import type { params2ExecuteTask, params2ReturnTask } from '@/apis/path/task';
import { reactive, ref } from 'vue';
import { findKeyByValue } from '@/utils/func';

const addKlgFormRef = ref<FormInstance>();
const selectKlgFormRef = ref<FormInstance>();
const returnFormRef = ref<FormInstance>();
const drawerVisible = ref(false);

const data = ref();
const curKlg = ref({ typestr: '', type: '', name: '' }); // 当前新增知识点
const curSelectKlg = ref({ klgCode: '', klgTitle: '' }); // 当前选中知识点
const curReason = ref({ reason: '' }); // 当前理由
const klgList = ref();

const curMode = ref(0); // 0: tasklist || 1: checkmytask || 2: conductTask
const curRadio = ref('-1'); // '1': 执行任务 || '2': 退回任务
const curShowKlg = ref(-1); // -1: default || 0: add || 1: return

const loading = ref(false);
const emits = defineEmits(['refresh']);

// 展示抽屉
const showDrawer = (item: any, mode?: number) => {
  // 初始化
  curKlg.value.type = '';
  curKlg.value.name = '';
  curRadio.value = '-1';
  curShowKlg.value = -1;
  drawerVisible.value = true;
  if (mode) {
    curMode.value = mode;
    if (mode === 2) {
      curRadio.value = '1';
    } else if (mode === -1) {
      switch (item.taskStatus) {
        case taskCompleteType.executed:
          curMode.value = 3;
          break;
        case taskCompleteType.completed:
          curMode.value = 4;
          break;
        case taskCompleteType.returned:
          curMode.value = 1;
          break;
        case taskCompleteType.executing:
          curMode.value = 6;
          break;
      }
    }
  }
  data.value = item;
};
// 处理关闭
const handleClose = () => {
  curReason.value.reason = '';
  drawerVisible.value = false;
};
// 展示执行任务的新增/选择知识
const handleShowKlg = (mode: number) => {
  curShowKlg.value = mode;
};
// 处理提交新增知识
const handleSubmitAddKlg = () => {
  addKlgFormRef.value?.validate((valid) => {
    if (valid) {
      const params: params2ExecuteTask = {
        taskId: data.value.oid,
        typeId: parseInt(curKlg.value.type),
        klgType: findKeyByValue(parseInt(curKlg.value.type), KlgTypeDict),
        klgTitle: curKlg.value.name
      };
      executeTaskApi(params).then((res) => {
        if (res.success) {
          ElMessage.success('提交成功');
          drawerVisible.value = false;
          emits('refresh');
          handleClose();
        } else {
          ElMessage.success(res.message);
        }
      });
    }
  });
};
// 处理提交选择知识
const handleSubmitSelectKlg = () => {
  selectKlgFormRef.value?.validate((valid) => {
    if (valid) {
      const params: params2ExecuteTask = {
        taskId: data.value.oid,
        klgCode: curSelectKlg.value.klgCode
      };
      executeTaskApi(params).then((res) => {
        if (res.success) {
          ElMessage.success('提交成功');
          drawerVisible.value = false;
          emits('refresh');
          handleClose();
        }
      });
    }
  });
};
// 处理退回任务
const handleReturnTask = () => {
  returnFormRef.value?.validate((valid) => {
    if (valid) {
      const params: params2ReturnTask = {
        taskId: data.value.oid,
        feedback: curReason.value.reason
      };
      returnTaskApi(params).then((res) => {
        if (res.success) {
          ElMessage.success('提交成功');
          handleClose();
          emits('refresh');
        }
      });
    }
  });
};
// 筛选Klg
const remoteKlg = (key: string) => {
  if (key) {
    loading.value = true;
    getKlgsApi(key).then((res) => {
      if (res.success) {
        loading.value = false;
        klgList.value = res.data.list;
      }
    });
  }
};
// // 搜索klg
// const searchKlg = () => {
//   ElMessage.success("ssss")
//   loading.value = true
//   const key = ""
//   getKlgsApi(key).then((res) => {
//     if (res.success) {
//       loading.value = false
//       klgList.value = res.data.list
//     }
//   })
// }
// 处理选择知识
const handleSelectKlg = (item: any) => {
  curSelectKlg.value.klgCode = item.klgCode;
  curSelectKlg.value.klgTitle = item.title;
};
// 处理删除选择的知识
const handleDeleteSelectKlg = () => {
  curSelectKlg.value.klgCode = '';
  curSelectKlg.value.klgTitle = '';
  ElMessage.success('删除成功');
};
// 处理编辑知识点
const routerPush = (data: any) => {
  sessionStorage.setItem('step', '0');
  window.open(`/editKlg?klgCode=${data.klgCode}`, '_blank');
};
// 解绑知识点
const releaseKlg = () => {
  releaseKlgApi(data.value.oid).then((res) => {
    if (res.success) {
      curMode.value = 2;
      curRadio.value = '1';
      ElMessage.success('解绑成功');
      emits('refresh');
    }
  });
};
defineExpose({
  showDrawer
});
</script>
,
<template>
  <el-drawer
    class="check-drawer"
    v-model="drawerVisible"
    :show-close="true"
    :close-on-click-modal="false"
    direction="ltr"
    size="40%"
    :z-index="10"
    @close="handleClose"
  >
    <template #header>
      <div>
        <span>查看任务</span>
      </div>
    </template>
    <div class="line"></div>
    <el-form class="main-wrapper">
      <el-form-item>
        <div class="info-content">
          <span class="name">
            {{ data.klgName }}
          </span>
          <span class="domain">
            {{ data.areaTitle }}
          </span>
          <span class="foot-bar">
            <span class="creator">
              {{ data.creatorName }}
            </span>
            <span class="time">
              {{ data.createTime }}
            </span>
          </span>
        </div>
      </el-form-item>
      <el-form-item label="任务状态:">
        <div>
          {{
            Object.keys(taskCompleteTypeDict).find(
              (key) => taskCompleteTypeDict[key] === data.taskStatus
            )
          }}
        </div>
      </el-form-item>
      <el-form-item label="负责人:">
        <div>{{ data.handlerName ? data.handlerName : '暂无' }}</div>
      </el-form-item>
      <!-- mytask -->
      <!-- 写的时候写错了，我有罪，希望您能看懂我写的注释 -->
      <!-- checktask -->
      <el-form-item label="退回理由:" v-if="curMode === 1">
        <div class="result-content">
          {{ data.feedback ? data.feedback : '暂无' }}
        </div>
      </el-form-item>
      <!-- conducttask -->
      <el-form-item v-else-if="curMode === 2">
        <div class="line"></div>
        <div class="radio-group">
          <el-radio-group v-model="curRadio">
            <el-radio value="1" size="small">执行任务</el-radio>
            <el-radio value="2" size="small">退回任务</el-radio>
          </el-radio-group>
        </div>
      </el-form-item>
      <!-- conducttask => conduct -->
      <el-form-item v-if="curRadio === '1'">
        <div class="conduct-btn-group">
          <cmp-button
            class="btn"
            style="margin-right: 15px"
            type="primary"
            @click.prevent="handleShowKlg(1)"
            >+新增知识</cmp-button
          >
          <cmp-button class="btn" type="primary" @click.prevent="handleShowKlg(2)"
            >+选择知识</cmp-button
          >
        </div>
      </el-form-item>
      <!-- conducttask => return -->
      <el-form-item v-else-if="curRadio === '2'">
        <el-form ref="returnFormRef" :model="curReason" style="width: 100%; overflow-y: hidden">
          <el-form-item
            style="width: 100%; overflow-y: hidden"
            prop="reason"
            :rules="{
              required: true,
              message: '请输入退回理由',
              trigger: 'blur'
            }"
          >
            <el-input
              placeholder="请输入退回理由"
              style="
                --el-color-primary: var(--color-primary);
                width: 100%;
                height: 100%;
                margin-bottom: 10px;
                overflow-y: hidden;
              "
              maxlength="200"
              show-word-limit
              resize="none"
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 23 }"
              v-model="curReason.reason"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <div class="btn-group">
              <cmp-button type="primary" class="btn" @click.prevent="handleReturnTask"
                >提交</cmp-button
              >
            </div>
          </el-form-item>
        </el-form>
      </el-form-item>
      <!-- conducttask => conduct => addKlg -->
      <el-form-item v-if="curShowKlg === 1 && curRadio === '1'">
        <el-form ref="addKlgFormRef" :model="curKlg" class="add-klg-form">
          <el-form-item
            label="知识类型"
            prop="type"
            style="margin-top: 15px"
            :rules="{
              required: true,
              message: '请选择知识类型',
              trigger: 'blur'
            }"
          >
            <el-select v-model="curKlg.type" placeholder="请选择">
              <el-option
                v-for="(value, key) in KlgTypeDict"
                :key="key"
                :label="key"
                :value="value"
                :class="parseInt(curKlg.type) === value ? 'highlight' : ''"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="知识名称"
            prop="name"
            style="margin-top: 15px"
            :rules="{
              required: true,
              message: '请输入知识点名称',
              trigger: 'blur'
            }"
          >
            <!-- <el-input v-model="curKlg.name" placeholder="请输入"></el-input> -->
            <inlineCKEditor
              v-model="curKlg.name"
              placeholder="请输入"
              style="width: 100%"
              :height="35"
            ></inlineCKEditor>
          </el-form-item>
          <el-form-item>
            <div class="add-klg-btn-group">
              <cmp-button type="primary" class="btn" @click.prevent="handleSubmitAddKlg"
                >提交</cmp-button
              >
            </div>
          </el-form-item>
        </el-form>
      </el-form-item>
      <!-- conducttask => conduct => selectKlg -->
      <el-form-item v-else-if="curShowKlg === 2 && curRadio === '1'">
        <el-form ref="selectKlgFormRef" class="select-klg-form" :model="curSelectKlg">
          <el-form-item
            style="margin-top: 10px"
            prop="klgCode"
            :rules="{
              required: true,
              message: '请输入知识点名称',
              trigger: 'blur'
            }"
          >
            <el-select
              filterable
              remote
              placeholder="请输入知识名称"
              :remote-method="remoteKlg"
              :loading="loading"
              :fit-input-width="true"
              :remote-show-suffix="true"
              suffix-icon="Search"
            >
              <template #suffix>
                <el-icon @click.stop=""><Search /></el-icon>
              </template>
              <el-option
                v-for="item in klgList"
                :key="item.klgCode"
                :label="item.title"
                :value="item.klgCode"
                class="option ck-content"
                v-html="item.title"
                @click="handleSelectKlg(item)"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <span style="font-size: 12px"
              >说明: 请选择你要关联的知识,注意只能选择自己创建的未发布的知识点。</span
            >
          </el-form-item>
          <el-form-item class="select-block-item" label="选中的知识: ">
            <div class="select-block">
              <span class="ck-content" v-html="curSelectKlg.klgTitle"></span>
              <span
                style="color: var(--color-primary); font-weight: 400; cursor: pointer"
                @click="handleDeleteSelectKlg"
                >清除</span
              >
            </div>
          </el-form-item>
          <el-form-item>
            <div class="select-klg-btn-group">
              <cmp-button type="primary" class="btn" @click.prevent="handleSubmitSelectKlg"
                >提交</cmp-button
              >
            </div>
          </el-form-item>
        </el-form>
      </el-form-item>
      <el-form-item v-else-if="curMode === 3 || curMode === 6" label="对应知识: ">
        <div style="display: flex; width: 100%">
          <span class="klg-block ck-content" v-html="data.klgTitle"></span>
          <cmp-button
            class="btn"
            style="margin-left: 10px; width: 60px; height: 36px"
            type="primary"
            @click.prevent="routerPush(data)"
            v-if="curMode === 3"
            >编辑</cmp-button
          >
          <cmp-button
            class="btn normal"
            style="margin-left: 10px; width: 60px; height: 36px; background-color: white"
            @click.prevent="releaseKlg"
            >解绑</cmp-button
          >
        </div>
      </el-form-item>
      <el-form-item v-else-if="curMode === 4" label="对应知识: ">
        <span class="klg-block ck-content" v-html="data.klgTitle"></span>
      </el-form-item>
    </el-form>
  </el-drawer>
</template>
<style scoped>
.main-wrapper {
  width: 100%;
  font-family: var(--text-font-family);
  font-size: 14px;
  .info-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    background-color: var(--color-back-header);
    color: var(--color-black);
    padding: 10px;
    border-radius: 5px;
    .name {
      font-weight: 600;
    }
    .domain {
      font-weight: 400;
    }
    .foot-bar {
      color: var(--color-deep);
      .creator {
        margin-right: 20px;
      }
    }
  }
  .result-content {
    width: 100%;
    min-height: 150px;
    border: 1px solid var(--color-boxborder);
    border-radius: 4px;
    margin-top: 7px;
    padding: 2px 10px;
    color: var(--color-boxborder);
  }
  .radio-group {
    width: 100%;
    display: flex;
    justify-content: center;
    --el-color-primary: var(--color-primary);
  }
  .conduct-btn-group {
    display: flex;
    justify-content: center;
    padding: 20px;
    width: 100%;
    border: 1px dashed var(--color-primary);
  }
  .btn-group {
    display: flex;
    justify-content: center;
    width: 100%;
  }
  .add-klg-form {
    width: 100%;
    border: 1px solid var(--color-primary);
    border-radius: 2px;
    padding: 10px 40px;
    --el-color-primary: var(--color-primary);
    .add-klg-btn-group {
      width: 100%;
      display: flex;
      justify-content: center;
      margin-top: 40px;
    }
  }
  .select-klg-form {
    width: 100%;
    border: 1px solid var(--color-primary);
    border-radius: 2px;
    padding: 10px 20px;
    --el-color-primary: var(--color-primary);
    .select-block-item {
      margin-top: 10px;
      background-color: var(--color-back-header);
      padding: 0 10px;
      border-radius: 2px;
      .select-block {
        width: 100%;
        display: flex;
        justify-content: space-between;
        font-weight: 600;
        color: var(--color-black);
      }
    }

    .select-klg-btn-group {
      width: 100%;
      display: flex;
      justify-content: center;
      margin-top: 40px;
    }
  }
  .klg-block {
    width: 100%;
    border: 1px solid var(--color-primary);
    border-radius: 3px;
    padding: 0px 10px;
  }
  .normal {
    border: 1px solid var(--color-primary);
    color: var(--color-primary);
    &:hover {
      background-color: var(--color-second);
    }
  }
}
.btn {
  width: 120px;
  height: 30px;
  border-radius: 2px;
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
  margin-bottom: 10px;
}

.highlight {
  --el-color-primary: var(--color-primary);
}
:deep(p) {
  margin: 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
:deep(.el-select__caret.is-reverse) {
  transform: rotate(0deg);
}
/* 滚动条整体样式 */
:deep(.el-textarea__inner) ::-webkit-scrollbar {
  width: 8px; /* 垂直滚动条宽度 */
  height: 8px; /* 水平滚动条高度 */
}

/* 滚动条轨道样式 */
:deep(.el-textarea__inner) ::-webkit-scrollbar-track {
  background-color: black; /* 轨道颜色 */
  border-radius: 4px; /* 轨道圆角 */
}

/* 滚动条滑块样式 */
:deep(.el-textarea__inner) ::-webkit-scrollbar-thumb {
  background-color: black; /* 滑块颜色 */
  border-radius: 4px; /* 滑块圆角 */
  background-clip: padding-box; /* 防止滑块背景溢出 */
}

/* 滚动条滑块hover样式 */
:deep(.el-textarea__inner) ::-webkit-scrollbar-thumb:hover {
  background-color: black; /* 滑块hover颜色 */
}
</style>
