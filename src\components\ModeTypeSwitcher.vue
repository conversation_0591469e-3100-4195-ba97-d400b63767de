<script setup lang="ts">
import { ref, watch } from "vue"

const props = defineProps({
  mode: {
    type: Number,
    required: true,
  },
  modeValues: {
    type: Array as () => number[],
    required: false,
    default: () => [0, 1], // 超出默认要传这个参数
  },
  length: {
    type: Number,
    required: true,
    default: 1,
  },
  publishedEditable: {
    type: Boolean,
    required: false,
    default: true,
  },
})
const emit = defineEmits(["changeMode"])
const curMode = ref<number>()
const curModeValues = ref<number[]>()
watch(
  () => props,
  (newValue) => {
    curMode.value = newValue.mode
    curModeValues.value = newValue.modeValues
  },
  { deep: true, immediate: true }
)
const handleChangeMode = (newMode: number) => {
  if(!props.publishedEditable && newMode === 0) return
  curMode.value = newMode
  emit("changeMode", curMode.value)
}
</script>

<template>
  <div class="switcher-wrapper">
    <div
      v-for="index in Array(props.length).keys()"
      :key="index"
      @click="handleChangeMode(modeValues[index])"
      class="btn"
      :class="{
        isSelected: curMode == modeValues[index],
        disabled: !props.publishedEditable && modeValues[index] === 0
      }"
    >
      <slot :name="`mode${index}`"></slot>
    </div>
  </div>
</template>

<style scoped>
.switcher-wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-evenly;
  width: auto;
  height: 46px;
  padding: 0 3px;
  background-color: var(--color-group-background);
  .btn {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 42px;
    width: 137px;
    font-size: 14px;
    font-weight: 600;
    font-family: var(--font-family-info);
    &.isSelected,
    &:hover {
      cursor: pointer;
      background-color: white;
      color: var(--color-primary);
    }
  }
  .disabled {
    color: var(--color-grey);
    &:hover {
      background-color: var(--color-group-background) !important;
      cursor: text !important;
      color: var(--color-grey) !important;
    }
  }
}

</style>
