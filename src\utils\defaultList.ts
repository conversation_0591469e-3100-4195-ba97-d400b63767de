import { userInfoStore } from "@/stores/userInfo"
import { ref } from "vue"

const menuAccessMap: { [key: string]: boolean } = {}

export interface RoleBoxItem {
  name: string
  title: string
  check: boolean
}
export interface RolePermission {
  name: string
  title: string
  checkAll: boolean
  isIndeterminate: boolean
  checkedGroup: RoleBoxItem[]
  group: RoleBoxItem[]
}

// 左栏目录
export const menuList = [
  {
    name: "domain",
    title: "领域管理",
    path: "/domain",
    isActive: false,
    children: [
      {
        name: "areaManage",
        title: "领域标签维护",
        path: "/domain/tag",
      },
      {
        name: "",
        title: "领域维护",
        path: "/domain/maintain",
      },
    ],
  },
  {
    name: "task",
    title: "任务管理",
    path: "/task",
    isActive: false,
    children: [
      {
        name: "taskManage",
        title: "任务列表",
        path: "/task/list",
      },
      {
        name: "",
        title: "我的任务",
        path: "/task/mytask",
      },
    ],
  },
  {
    name: "",
    title: "知识管理",
    path: "/klg",
    isActive: false,
    children: [
      {
        name: "",
        title: "知识维护",
        path: "/klg/maintain",
      },
      {
        name: "",
        title: "回收站",
        path: "/klg/recycle",
      },
    ],
  },
  {
    name: "",
    title: "审核管理",
    path: "/audit",
    isActive: false,
    children: [
      {
        name: "",
        title: "知识待审核池",
        path: "/audit/pool",
      },
      {
        name: "",
        title: "我的知识审核",
        path: "/audit/myaudit",
      },
    ],
  },
  {
    name: "",
    title: "资料管理",
    path: "/document",
    isActive: false,
    children: [
      {
        name: "",
        title: "参考文献",
        path: "/document/reference",
      },
    ],
  },
  // {
  //   name: "permission",
  //   title: "权限管理",
  //   path: "/permission",
  //   isActive: false,
  //   children: [
  //     {
  //       name: "userManagement",
  //       title: "用户管理",
  //       path: "/permission/user",
  //     },
  //     {
  //       name: "roleManagement",
  //       title: "角色管理",
  //       path: "/permission/character",
  //     },
  //     {
  //       name: "approvalManagement",
  //       title: "审核管理",
  //       path: "/permission/audit",
  //     },
  //   ],
  // },
]

export const getLeftMenu = () => {
  const userinfo = userInfoStore()
  const permissions = userinfo.getPermission()
  const tempMenu = ref<any[]>([])
  const system = permissions.find(
    (serv) => serv.service === import.meta.env.VITE_APP_SERVICE
  )
  if (system && system.access) {
    system.menus.forEach((item) => {
      menuAccessMap[item.menu] = item.access
    })
    menuList.forEach((firstmenu) => {
      if (!firstmenu.name) {
        tempMenu.value.push(firstmenu)
      } else {
        const tempFstMenu = {
          ...firstmenu,
          children: [] as any[],
        }
        firstmenu.children.forEach((scdmenu) => {
          if (!scdmenu.name) {
            tempFstMenu.children.push(scdmenu)
          } else {
            if (menuAccessMap[scdmenu.name]) {
              tempFstMenu.children.push(scdmenu)
            }
          }
        })
        if (tempFstMenu.children.length !== 0) {
          tempMenu.value.push(tempFstMenu)
        }
      }
    })
  }
  return tempMenu.value
}
