<script setup lang="ts">
import Header from '@/layout/Header.vue';
import Left from '@/layout/Left.vue';
import LeftTree from '@/components/LeftTree.vue';
import { onBeforeRouteLeave, RouterView, useRoute } from 'vue-router';
import { ref, onMounted, computed, provide, watch } from 'vue';
import { getUserInfoApi } from '@/apis/path/user';
import { userInfoStore } from '@/stores/userInfo';
import { docCookies } from '@/utils';
import { ElMessageBox } from 'element-plus';
import router from '@/router';
import { jumpToLogin, routerPushSystem } from '@/utils/jump';
import { System } from '@/enums/system';

const route = useRoute();
const showTree = computed(
  () =>
    route.path === '/domain/maintain' ||
    route.path === '/klg/maintain' ||
    route.path === '/domain/tag' ||
    route.path === '/audit/pool'
);
const treeSelecterRef = ref();
const caninblackpearl = ref();
</script>
<template>
  <div class="app-container">
    <div class="header">
      <Header></Header>
    </div>
    <div class="content-container">
      <div class="left">
        <Left></Left>
      </div>
      <div class="middle">
        <LeftTree ref="treeSelecterRef" v-if="showTree"></LeftTree>
      </div>
      <div class="main">
        <div class="mainWrapper">
          <RouterView></RouterView>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
::v-deep(.el-table .el-table__cell) {
  padding: 8px 0;
}
* {
  --el-color-primary: var(--color-primary);
}
:deep(li::marker) {
  content: ''; /* 将内容设置为空字符串 */
  color: transparent; /* 将文本颜色设置为透明 */
}
.app-container {
  font-family: var(--text-family);
  background-color: rgba(242, 242, 242, 1);
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  word-break: break-all;
  --el-color-primary: var(--color-primary);
  :deep(pre) {
    width: max-content;
    min-width: 100%;
  }

  .header {
    display: flex;
    flex-direction: row;
    height: 60px;
    background-color: rgba(255, 255, 255, 0.996);
    box-shadow: rgba(149, 157, 165, 0.2) 0px 5px 24px;
    margin-bottom: 10px;
  }

  .content-container {
    height: calc(100vh - 60px);
    display: flex;
    flex-direction: row;
    overflow: hidden;

    .left {
      background-color: white;
      height: 100%;
      width: 200px;
    }
    .middle {
    }
    .main {
      width: 100%;
      display: flex;
      justify-content: center;
      overflow-y: auto;
      .mainWrapper {
        margin-top: 20px;
      }
      &::-webkit-scrollbar {
        width: 5px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #888;
        border-radius: 6px;
        height: 50px;
      }

      &::-webkit-scrollbar-track {
        background-color: #f1f1f1;
      }
    }
  }
}
</style>
