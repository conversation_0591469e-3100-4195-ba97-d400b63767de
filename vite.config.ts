import { fileURLToPath, URL } from 'node:url';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import mkcert from 'vite-plugin-mkcert';
import { codeInspectorPlugin } from 'code-inspector-plugin';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    codeInspectorPlugin({
      bundler: 'vite'
    }),
    AutoImport({
      imports: ['vue', 'vue-router'],
      resolvers: [ElementPlusResolver()]
    }),
    Components({
      resolvers: [ElementPlusResolver()]
    }),
    mkcert()
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    host: 'localhost',
    port: 5672, // 端口
    proxy: {
      '/blackpearl-service': {
        target: 'http://**************:8001',
        changeOrigin: true
        // secure: true,
      },
      '/auth-service': {
        target: 'http://**************:8001',
        changeOrigin: true
      }
    }
  },
  optimizeDeps: {
    // include: ['ckeditor5-custom-build', '@ckeditor/ckeditor5-vue']
  },
  build: {
    rollupOptions: {
      external: ['element-plus/es/locale']
    },
    commonjsOptions: {
      include: [/node_modules/]
    }
  }
});
