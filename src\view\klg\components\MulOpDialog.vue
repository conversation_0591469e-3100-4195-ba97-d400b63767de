<script setup lang="ts">
import { params2MulOpKlg } from "@/apis/path/klg"
import CmpButton from "@/components/CmpButton.vue"
import MulOpBlock from "@/view/other/linkKlg/MulOpBlock.vue"
import { ElMessage, UploadInstance, UploadUserFile } from "element-plus"
import { uploadFileApi } from "@/apis/path/klg"
import { ref, watch, nextTick } from "vue"

const emits = defineEmits(["op"])
const curMode = ref(-1)
const dialogVisible = ref(false)
const titleText = ref("")
const curData = ref()
const mulOpBlockRef = ref()
const uploadRef = ref<UploadInstance>()
const fileList = ref<UploadUserFile[]>([])
// 展示dialog
const showDialog = (mode: number, data?: any) => {
  dialogVisible.value = true
  curMode.value = mode
  switch (mode) {
    case 0:
      titleText.value = "恢复知识"
      curData.value =
        "您恢复的项目会出现在“知识维护”中并可以继续编辑，您确定要恢复吗？"
      break
    case 1:
      titleText.value = "销毁知识"
      curData.value = "您销毁的知识不能再恢复了，您确定要销毁吗？"
      break
    case 2:
      titleText.value = "批量上传"
      curData.value = data
      break
    case 3:
      titleText.value = "批量删除"
      curData.value = "您删除的知识会放到“回收站”中，您确定要删除吗？"
      break
    case 4:
      titleText.value = "批量预览"
      break
    case 5:
      titleText.value = "批量添入"
      nextTick(() => {
        mulOpBlockRef.value.resetParams()
        mulOpBlockRef.value.getTree()
        mulOpBlockRef.value.setTargetAreaCode(data)
      })
      break
    case 6:
      titleText.value = "批量移入"
      nextTick(() => {
        mulOpBlockRef.value.resetParams()
        mulOpBlockRef.value.getTree()
        mulOpBlockRef.value.setTargetAreaCode(data)
      })
      break
  }
}

// 下载模板
const downloadModel = () => {
  const fileName = "知识批量上传模板.xlsx"
  const fileUrl = `/static/${fileName}`
  const link = document.createElement("a")
  link.href = fileUrl
  link.setAttribute("download", fileName)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
// 上传文件
const uploadFile = (item: any) => {
  let FormDatas = new FormData()
  FormDatas.append("file", item.file)
  FormDatas.append("areaCode", curData.value.curAreaCode)
  uploadFileApi(FormDatas).then((res) => {
    if (res.success) {
      ElMessage.success("成功")
      uploadRef.value?.clearFiles()
      dialogVisible.value = false
    } else {
      ElMessage.success(res.message)
    }
  })
}
// 处理提交
const handleSubmit = () => {
  if (curMode.value === 5 || curMode.value === 6) {
    mulOpBlockRef.value.handleSubmit(curMode.value).then((result) => {
      if (result) {
        nextTick(() => {
          emits("op", curMode.value)
          dialogVisible.value = false
        })
      }
    })
  } else if (curMode.value === 2) {
    uploadRef.value?.submit()
    nextTick(() => {
      emits("op", curMode.value)
      dialogVisible.value = false
    })
  } else {
    console.log("op", curMode.value)
    nextTick(() => {
      emits("op", curMode.value)
      dialogVisible.value = false
    })
  }
}
defineExpose({
  showDialog,
})
</script>
<template>
  <el-dialog
    v-model="dialogVisible"
    :class="{
      wrapper1: curMode === 5 || curMode === 6,
      wrapper2: curMode !== 5 && curMode !== 6,
    }"
  >
    <template #header
      ><div class="header">{{ titleText }}</div></template
    >
    <div class="main-container">
      <div v-if="curMode === 0 || curMode === 1 || curMode === 3">
        {{ curData }}
      </div>
      <div class="upload-form-container" v-else-if="curMode === 2">
        <div class="instruction">
          说明：请先下载模板，并按照模板格式要求录入知识。录入完成后上传编辑好的知识内容。文件大小不能超过20M。
        </div>
        <div class="form-container">
          <el-form>
            <el-form-item label="当前领域">
              <span class="form-item">{{ curData.curTitle }}</span>
            </el-form-item>
            <el-form-item label="模板下载">
              <span
                class="form-item"
                style="cursor: pointer"
                @click="downloadModel"
                >知识批量上传模板.xslx
              </span>
            </el-form-item>
            <el-form-item label="选择文件">
              <el-upload
                ref="uploadRef"
                class="upload"
                :limit="1"
                :auto-upload="false"
                :file-list="fileList"
                accept=".xlsx,.xls"
                :http-request="uploadFile"
              >
                <template #trigger>
                  <CmpButton type="primary" @click.prevent>选择</CmpButton>
                </template>
              </el-upload>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div
        class="tree-table-container"
        v-else-if="curMode === 5 || curMode === 6"
      >
        <MulOpBlock ref="mulOpBlockRef"></MulOpBlock>
      </div>
    </div>
    <template #footer>
      <div class="footer">
        <CmpButton type="info" @click="dialogVisible = false">关闭</CmpButton>
        <CmpButton type="primary" @click="handleSubmit">确定</CmpButton>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.header {
  color: var(--color-black);
  font-weight: 600;
}

.main-container {
  color: var(--color-black);
  width: 100%;
  display: flex;
  .upload-form-container {
    .instruction {
      color: var(--color-grey);
    }
    .form-container {
      padding: 0 20px;
      .form-item {
        background-color: var(--color-light);
        margin-bottom: 5px;
        border: 1px solid var(--color-boxborder);
        padding: 0 10px;
      }
    }
    .tree-table-container {
    }
  }
}
.footer {
  display: flex;
  justify-content: center;
  gap: 40px;
}
:deep(.el-form-item) {
  margin: 0;
}
</style>
<style>
.wrapper1 {
  width: 900px;
  height: auto;
}
.wrapper2 {
  width: 600px;
}
</style>
