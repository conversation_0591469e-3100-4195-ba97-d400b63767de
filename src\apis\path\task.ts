import { http } from "@/apis"
import type { APIResponse } from "@/utils/type"

export interface params2GetTaskList {
  current: number
  limit: number
  status?: number
  areaTitle?: string
  taskTitle?: string
}

export interface params2AssignHandler {
  taskId: string
  userId: string
  answerId: string
}
export interface params2ExecuteTask {
    taskId: number,
    klgCode?: string,
    klgTitle?: string,
    klgType?: string,
    typeId?: number,
}
export interface params2ReturnTask {
  taskId: number,
  feedback: string,
}
// 获取任务列表
export function getTaskListApi(params: params2GetTaskList): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/task/ListTaskByPage`,
    data: params,
  })
}

// 获取筛选领域列表
export function getDomainListApi(keyWord: string): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/task/getArea?keyWord=${keyWord}`,
  })
}

// 获取负责人列表
export function getHandlerListApi(keyWord: string, areaCode: string): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/task/getHandler?name=${keyWord}&areaCode=${areaCode}`,
  })
}

// 分配任务
export function assignHandlerApi(params: params2AssignHandler): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/task/allotTask`,
    data: params,
  })
}

// 获取任务列表
export function getMyTaskListApi(params: params2GetTaskList): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/task/listTaskByUserId`,
    data: params,
  })
}

// 执行任务
export function executeTaskApi(params: params2ExecuteTask): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/task/executeTask`,
    data: params,
  })
}

// 获取知识列表
export function getKlgsApi(klgName: string): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/task/getKlgs?klgName=${klgName}`,
  })
}

// 退回任务
export function returnTaskApi(params: params2ReturnTask): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/task/returnTask`,
    data: params,
  })
}

// 解绑知识点
export function releaseKlgApi(taskId: number): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/task/unbindingKlg?taskId=${taskId}`,
  })
}