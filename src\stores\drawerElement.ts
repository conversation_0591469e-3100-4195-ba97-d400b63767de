import { defineStore } from 'pinia';
import { ref } from 'vue';

// 定义一个 Pinia Store 来管理当前选中的 DOM 元素
export const useElementStore = defineStore('elementStore', () => {
  // 存储当前选中的 DOM 元素，初始为 null
  const element = ref<HTMLElement | null>();

  // 设置当前选中的元素
  const setElement = (el: HTMLElement | null) => {
    element.value = el;
  };

  // 将当前选中的元素设置为 null
  const setNull = () => {
    element.value = null;
  };

  // 暴露状态和方法
  return { element, setNull, setElement };
});
