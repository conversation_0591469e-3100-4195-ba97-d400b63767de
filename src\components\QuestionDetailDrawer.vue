<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';

// 组件状态
const drawerVisible = ref(false);
const questionList = ref<any[]>([]);

// 显示抽屉
const showDrawer = (questions: any[]) => {
  questionList.value = questions || [];
  drawerVisible.value = true;
};

// 关闭抽屉
const closeDrawer = () => {
  drawerVisible.value = false;
  questionList.value = [];
};

// 跳转到知识详情页
const toKlgDetail = (item: any) => {
  if (item.klgCode) {
    window.open(`/klgdetail?klgCode=${item.klgCode}`, '_blank');
  }
};

// 处理显示问题详情的事件
const handleShowQuestionDetail = (event: CustomEvent) => {
  const { questions } = event.detail;
  showDrawer(questions);
};

// 暴露方法给父组件
defineExpose({
  showDrawer,
  closeDrawer
});

onMounted(() => {
  // 监听显示问题详情的事件
  window.addEventListener('showQuestionDetail', handleShowQuestionDetail as EventListener);
});

onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('showQuestionDetail', handleShowQuestionDetail as EventListener);
});
</script>

<template>
  <el-drawer
    v-model="drawerVisible"
    direction="ltr"
    size="596px"
    :z-index="2000"
    class="question-detail-drawer"
    @close="closeDrawer"
  >
    <template #header>
      <div class="drawer-header">
        <span class="header-title">显示问题 </span>
      </div>
    </template>

    <div class="drawer-content">
      <div v-if="questionList.length === 0" class="empty-container">
        <el-empty description="暂无问题" />
      </div>

      <div v-else class="main-container">
        <div v-for="question in questionList" :key="question.questionId" class="question-block">
          <!-- 问题内容区域 -->
          <div class="question-content">
            <!-- 文本关联内容 -->
            <div class="associated-words">
              <span
                class="words-text ck-content"
                v-html="question.associatedWords || question.keyword"
              ></span>
            </div>

            <!-- 问题关键词 -->
            <div class="question-keyword">
              【<span class="keyword-text ck-content" v-html="question.keyword"></span>】
              <span v-if="question.questionType !== '开放性问题'" class="question-type-text">{{
                question.questionType
              }}</span>
              <span
                v-else
                class="question-description-text"
                v-html="question.questionDescription"
              ></span>
            </div>
          </div>

          <div class="line"></div>

          <!-- 关联结果区域 -->
          <div class="section-title">关联结果</div>
          <div class="link-result">
            <div v-if="!question.answers || question.answers.length === 0" class="no-result">
              暂无关联结果
            </div>
            <div v-else class="result-list">
              <div
                v-for="answer in question.answers"
                :key="answer.klgCode"
                class="result-item"
                @click="toKlgDetail(answer)"
              >
                <span class="result-title ck-content" v-html="answer.title"></span>
                <span class="result-action">领域</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<style scoped>
.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .header-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.drawer-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.main-container {
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 20px;
  color: var(--color-black);
}

.question-block {
  margin-bottom: 30px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-black);
  margin-bottom: 10px;
}

.question-content {
  margin-bottom: 15px;
}

.associated-words {
  display: flex;
  flex-direction: row;
  background-color: var(--color-light);
  padding: 10px;
  border-radius: 3px;
  margin-bottom: 10px;

  .label {
    margin-right: 10px;
    white-space: nowrap;
    font-size: 14px;
  }

  .words-text {
    font-weight: 600;
    word-break: break-all;
    font-size: 14px;
  }
}

.question-keyword {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-black);
  margin-bottom: 10px;

  .question-type-text {
    margin-left: 10px;
    font-size: 14px;
    color: var(--color-grey);
    font-weight: normal;
  }

  .question-description-text {
    margin-left: 10px;
    font-size: 14px;
    color: var(--color-black);
    font-weight: normal;
  }
}

.line {
  margin: 15px 0;
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}

.link-result {
  width: 100%;
  background-color: var(--color-light);
  border-radius: 3px;
  min-height: 120px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  margin-bottom: 20px;
}

.no-result {
  color: var(--color-grey);
  font-size: 14px;
  text-align: center;
  padding: 20px;
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.result-item {
  width: 100%;
  background-color: white;
  border-radius: 3px;
  border: 1px solid var(--color-boxborder);
  padding: 8px 10px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--color-primary);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .result-title {
    flex: 1;
    font-size: 14px;
    color: var(--color-black);
    word-break: break-all;
  }

  .result-action {
    font-size: 12px;
    color: var(--color-primary);
    background-color: var(--color-light);
    padding: 2px 8px;
    border-radius: 3px;
    margin-left: 10px;
    white-space: nowrap;
  }
}
</style>
