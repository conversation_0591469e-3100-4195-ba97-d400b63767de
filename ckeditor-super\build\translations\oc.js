!function(e){const t=e.oc=e.oc||{};t.dictionary=Object.assign(t.dictionary||{},{"%0 of %1":"",Accept:"",Bold:"Gras","Bold text":"",Cancel:"Anullar",Clear:"","Close contextual balloons, dropdowns, and dialogs":"",Code:"","Content editing keystrokes":"","Execute the currently focused button. Executing buttons that interact with the editor content moves the focus back to the content.":"",Italic:"Italica","Italic text":"","Move focus between form fields (inputs, buttons, etc.)":"","Move focus to the menu bar, navigate between menu bars":"","Move focus to the toolbar, navigate between toolbars":"","Move out of an inline code style":"","Navigate through the toolbar or menu bar":"","Open the accessibility help dialog":"","Remove color":"","Restore default":"","Rich Text Editor. Editing area: %0":"",Save:"Enregistrar","Show more items":"",Strikethrough:"","Strikethrough text":"",Subscript:"",Superscript:"","These keyboard shortcuts allow for quick access to content editing features.":"","Toggle caption off":"","Toggle caption on":"",Underline:"","Underline text":"","Use the following keystrokes for more efficient navigation in the CKEditor 5 user interface.":"","User interface and content navigation keystrokes":""}),t.getPluralForm=function(e){return e>1}}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));