import { computed } from 'vue';
import { useRoute } from 'vue-router';

/**
 * 根据当前路由动态返回 drawer 的方向
 * linkklg 路由: ltr (左侧)
 * klgdetail 路由: rtl (右侧)
 */
export function useDrawerDirection() {
  const route = useRoute();
  
  const drawerDirection = computed(() => {
    const routeName = route.name as string;
    
    switch (routeName) {
      case 'linkklg':
        return 'ltr'; // 左侧显示
      case 'klgdetail':
        return 'rtl'; // 右侧显示
      default:
        return 'rtl'; // 默认右侧显示
    }
  });
  
  return {
    drawerDirection
  };
}
