<script setup lang="ts">
import CmpButton from "@/components/CmpButton.vue"
import {
  getTreeApi,
  getReferNodeApi,
  editReferNodeApi,
orderDomainApi,
params2OrderDomain,
} from "@/apis/path/domain"
import type { params2EditReferNode } from "@/apis/path/domain"
import { Domain, Tree } from "@/utils/type"
import { transformToTreeNode } from "@/utils/func"
import { nextTick, onMounted, ref, watch } from "vue"
import { ElMessage, ElTable, ElTree } from "element-plus"
import { TreeType } from "@/utils/constant"
import { emitter } from "@/utils/emitter"
import { Event } from "@/types/event"

const tableRef = ref<InstanceType<typeof ElTable>>()
const treeRef = ref<InstanceType<typeof ElTree>>()
const dialogVisible = ref(false)
const treeData = ref<Tree[]>([])
const tableData = ref()
const curData = ref<any>()
const curMode = ref(0) // 0: 选择领域 || 1: 查看子领域关系 || 2: 添加依赖
const curTitleText = ref("")
const filterText = ref("")
const curSelectPNode = ref() // 当前父节点
const parentNodeEditFlag = ref(true) // 父节点是否有编辑权限
const defaultExpandIds = ref<string[]>([]) // 这里存放要默认展开的节点 id
const emits = defineEmits(["edit-p-node"])
const draggingItemOid = ref(null)
const tempAreaList = ref<any[]>([])
let draggingItemIndex = -1

// 展示Dialog
const showDialog = (mode: number, data: any) => {
  parentNodeEditFlag.value = true
  dialogVisible.value = true
  curMode.value = mode
  curData.value = data
  console.log("curData", curData.value)
  switch (mode) {
    case 0:
      curTitleText.value = "选择领域"
      getTree()
      nextTick(() => {
        defaultExpandIds.value.push(curData.value.areaCode)
      })
      break
    case 1:
      curTitleText.value = "查看子领域关系"
      break
    case 2:
      curTitleText.value = "添加依赖"
      tableRef.value?.clearSelection()
      getReferNode()
      break
    case 3:
      curTitleText.value = "子领域排序"
      Object.assign(tempAreaList.value, curData.value.angle.list)
    default:
      break
  }
}

// 获取树
const getTree = () => {
  getTreeApi(TreeType.edit).then((res) => {
    treeData.value = [transformToTreeNode(res.data.tree[0])]
    initNode(
      treeData.value[0],
      curData.value.parentAreaCode,
      curData.value.areaCode,
      true,
      false
    )
  })
}

// 初始化节点
const initNode = (
  node: Tree,
  targetCode: string,
  currentCode: string,
  isChecked: boolean,
  targetFound: boolean
) => {
  /* 1. showCheckBox初始化已经在transformToTreeNode做了
   *     showCheckBox赋值isEditArea的值，isEditArea为true的节点是 所有isTag为1的节点且其子节点的isTag不为1 的集合
   */
  // 2. 如果找到了目标，设置flag找到，让之后的节点showCheckBox都为false
  if (node.areaCode === currentCode) {
    targetFound = true
    node.showCheckBox = false
    if (!node.isEditArea) {
      parentNodeEditFlag.value = false
    }
  } else if (node.areaCode === targetCode) {
    node.checked = isChecked
  } else {
    // 非当前节点且找到当前节点则将true改为false
    if (targetFound) {
      node.showCheckBox = !targetFound
    }
  }
  // 如果有子节点
  if (node.children) {
    node.children.forEach((item) => {
      initNode(item, targetCode, currentCode, isChecked, targetFound)
    })
  }
  targetFound = false
}

// 筛选树
watch(filterText, (val) => {
  treeRef.value!.filter(val)
})

// 筛选方法
const filterNode = (value: string, data: Tree) => {
  if (!value) return true
  return data.label.includes(value)
}

// 获取同级依赖节点
const getReferNode = () => {
  getReferNodeApi(curData.value.areaCode).then((res) => {
    if (res.success) {
      tableData.value = res.data.list
      nextTick(() => {
        tableData.value.forEach((row) => {
          if (row.isOk) {
            tableRef.value?.toggleRowSelection(row, true)
          }
        })
      })
    }
  })
}

// 处理选择父节点
const handleSelectParentNode = (data: any) => {
  // 确保其他节点的 checked 状态为 false
  cleanSelectParentNode(treeData.value[0])
  curSelectPNode.value = {
    areaCode: data.areaCode,
    parentId: data.areaId,
  }
}

// 清除checked
const cleanSelectParentNode = (node: Tree) => {
  if (node.checked) {
    node.checked = false
    return
  }
  if (node.children) {
    node.children.forEach((item) => {
      cleanSelectParentNode(item)
    })
  }
}

// 处理拖拽开始
const handleDragStart = (index: number) => {
  draggingItemIndex = index
  draggingItemOid.value = tempAreaList.value[index].oid
}
// 处理拖拽结束
const handleDragOver = (e: any) => {
  e.preventDefault()
  e.dataTransfer.dropEffect = "move"
}
// 处理拖拽
const handleDrop = (targetIndex: number) => {
  if (draggingItemIndex === targetIndex) return
  const item = tempAreaList.value[draggingItemIndex]
  tempAreaList.value.splice(draggingItemIndex, 1)
  tempAreaList.value.splice(targetIndex, 0, item)

  draggingItemIndex = -1
  draggingItemOid.value = null
}
// 处理拖拽事件
const handleDragEnd = () => {
    draggingItemOid.value = null;
    console.log('Drag operation ended');
}
// 提交
const handleSubmit = () => {
  if (curMode.value === 0) {
    handleClose()
    emits("edit-p-node", [
      curSelectPNode.value.areaCode,
      curSelectPNode.value.parentId,
    ])
  } else if (curMode.value === 1) {
  } else if (curMode.value === 2) {
    const params: params2EditReferNode = {
      areaCode: curData.value.areaCode,
      editAreaCond: tableRef.value?.getSelectionRows().map((item) => {
        return {
          areaTargetCode: item.areaCode,
          description: item.description,
        }
      }),
    }
    editReferNodeApi(params).then((res) => {
      if (res.success) {
        ElMessage.success("添加成功")
        handleClose()
      } else {
        ElMessage.error(res.message)
      }
    })
  } else if (curMode.value === 3) {
    const params: params2OrderDomain = {
      areaCode: curData.value.domain.areaCode,
      angleId: curData.value.angle.oid,
      sortAreaList: tempAreaList.value.map((item, index)=>{
        return {
          areaCode: item.areaCode,
          sort: index + 1,
        }
      })
    }
    console.log("params", params)
    orderDomainApi(params).then(res=>{
      if(res.success) {
        ElMessage.success("修改成功")
        Object.assign(curData.value.angle.list, tempAreaList.value)
        emitter.emit(Event.TREE_ORDER, params)
        handleClose()
      }else{
        ElMessage.warning(res.message)
      }
    })
  }
}
// 关闭
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  showDialog,
})
</script>
<template>
  <el-dialog v-model="dialogVisible" style="height: 70vh">
    <template #header> {{ curTitleText }} <el-divider v-if="curMode === 3"/></template>
    <div class="main-container">
      <div v-if="curMode === 0" class="tree-content">
        <div class="input">
          <el-input
            v-model="filterText"
            placeholder="请输入查询内容"
            suffix-icon="Search"
          />
        </div>
        <div class="block">
          <el-tree
            ref="treeRef"
            class="tree"
            :data="treeData"
            node-key="areaCode"
            :current-node-key="curData.areaCode"
            :filter-node-method="filterNode"
            :default-expanded-keys="defaultExpandIds"
          >
            <template #default="{ node, data }">
              <span class="tree-node">
                <span class="tree-node-label">
                  {{ node.label }}{{ node.areaCode }}</span
                >
                <el-checkbox
                  v-if="data.showCheckBox"
                  v-model="data.checked"
                  style="margin-left: 10px"
                  @click.stop="handleSelectParentNode(data)"
                ></el-checkbox>
                <span
                  v-if="data.areaCode === curData.areaCode"
                  class="current-area-block"
                  >当前领域</span
                >
              </span>
            </template>
          </el-tree>
        </div>
      </div>
      <div v-else-if="curMode === 1"></div>
      <div v-else-if="curMode === 2" class="refer-content">
        <el-form style="padding: 0 50px">
          <el-form-item label="当前领域">
            <div class="area-name">{{ curData.title }}</div>
          </el-form-item>
          <el-form-item>
            <div class="tip-text">请在下方勾选当前领域所依赖的领域。</div>
          </el-form-item>
          <el-form-item>
            <el-table ref="tableRef" :data="tableData" height="400">
              <el-table-column type="selection" width="55" />
              <el-table-column
                prop="title"
                label="同级领域名称"
                align="center"
              />
              <el-table-column label="依赖描述" align="center">
                <template #default="scoped">
                  <el-form>
                    <el-form-item>
                      <el-input
                        placeholder="请输入依赖描述"
                        v-model="scoped.row.description"
                      ></el-input>
                    </el-form-item>
                  </el-form>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
      </div>
      <div v-else-if="curMode === 3" class="sort-content">
        <div class="sort-container">
          <div class="sort-title">
            <span>{{ curData.domain.title }}</span>
            <span class="connect-line"></span>
            <span>{{ curData.angle.angleTitle }}</span>
          </div>
          <ul id="sortable-list" class="sortable-list">
            <li
              v-for="(item, index) in tempAreaList"
              :key="item.oid"
              class="draggable-item"
              draggable="true"
              :class="{ dragging: item.oid === draggingItemOid }"
              @dragstart="handleDragStart(index)"
              @dragover="handleDragOver"
              @drop="handleDrop(index)"
              @dragend="handleDragEnd"
            >
              {{ item.title }}
            </li>
          </ul>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="footer-content">
        <div class="btn-group">
          <CmpButton
            type="info"
            style="margin-right: 10px"
            @click.prevent="handleClose"
            >取消</CmpButton
          >

          <CmpButton
            type="primary"
            :disabled="parentNodeEditFlag === false"
            @click.prevent="handleSubmit"
            >确定</CmpButton
          >
        </div>
        <span v-if="parentNodeEditFlag === false" class="warning-text"
          >没有该领域所属父领域的编辑权限，无法编辑</span
        >
      </div>
    </template>
  </el-dialog>
</template>
<style scoped>
.main-container {
  height: max-content;
  .tree-content {
    .input {
      background-color: var(--color-second);
      padding: 5px;
    }
    .block {
      height: 50vh;
      overflow: auto;
      border: none;
      background-color: var(--color-second);

      &::-webkit-scrollbar {
        width: 4px;
        height: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #888;
        border-radius: 4px;
        height: 4px;
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background-color: var(--color-theme-hover);
      }
      .tree {
        border: none;
        display: inline-block;
        background-color: var(--color-theme-hover);
        .tree-node {
          display: flex;
          align-items: center;
          .tree-node-label {
            padding: 1px 7px;
            background-color: white;
          }
          .current-area-block {
            padding: 1px;
            border: 1px solid var(--color-primary);
            background-color: var(--color-primary);
            color: white;
            border-radius: 3px;
            font-size: 10px;
            margin-left: 5px;
          }
        }
      }
    }
  }

  .refer-content {
    .area-name {
      background-color: var(--color-light);
      padding: 0 15px;
      width: 100%;
      border-radius: 2px;
    }
    .tip-text {
      color: var(--color-deep);
      font-size: 12px;
    }
    .domain-table {
      width: 100%;
      max-height: 400px;
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 5px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #888;
        border-radius: 6px;
        height: 2px;
      }

      &::-webkit-scrollbar-track {
        background-color: var(--color-theme-hover);
      }
    }
  }
  .sort-content {
    height: 430px;
    overflow: auto;
    .sort-container {
      padding: 0 100px;
      .sort-title {
        display: flex;
        flex-direction: row;
        align-items: center;
        .connect-line {
          display: flex;
          content: "";
          width: 50px; /* 视口宽度的 20% */
          height: 1px;
          margin: 0 10px;
          background-color: var(--color-boxborder);
        }
      }
      .sortable-list {
        margin-top: 20px;
        padding: 0 50px;
        list-style: none;
        /* padding: 0; */
      }

      .draggable-item {
        padding: 5px 10px;
        background-color: #fafafa;
        margin: 5px 0;
        cursor: grab;
      }

      .dragging {
        cursor: grabbing;
        opacity: 0.5;
      }
    }
  }
}
.footer-content {
  display: flex;
  flex-direction: column;
  justify-content: center;

  .btn-group {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  .warning-text {
    color: #f56c6c;
    font-size: 10px;
    display: flex;
    justify-content: center;
  }
}
</style>
<style>
.el-dialog__header {
  padding: 0;
}
.el-dialog__header.show-close {
  padding: 0;
}
</style>
