/**
 * @license Copyright (c) 2014-2024, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
 */
import { ClassicEditor as ClassicEditorBase } from "@ckeditor/ckeditor5-editor-classic";
import { InlineEditor as InlineEditorBase } from "@ckeditor/ckeditor5-editor-inline";
import { Autoformat } from "@ckeditor/ckeditor5-autoformat";
import { Bold, Italic } from "@ckeditor/ckeditor5-basic-styles";
import { BlockQuote } from "@ckeditor/ckeditor5-block-quote";
import { CloudServices } from "@ckeditor/ckeditor5-cloud-services";
import { Essentials } from "@ckeditor/ckeditor5-essentials";
import { Heading } from "@ckeditor/ckeditor5-heading";
import { Image, ImageCaption, ImageResize, ImageStyle, ImageToolbar, ImageUpload } from "@ckeditor/ckeditor5-image";
import { Indent } from "@ckeditor/ckeditor5-indent";
import { List } from "@ckeditor/ckeditor5-list";
import { Paragraph } from "@ckeditor/ckeditor5-paragraph";
import { PasteFromOffice } from "@ckeditor/ckeditor5-paste-from-office";
import { Table, TableToolbar } from "@ckeditor/ckeditor5-table";
import { TextTransformation } from "@ckeditor/ckeditor5-typing";
import { Undo } from "@ckeditor/ckeditor5-undo";
import Mathematics from "@isaul32/ckeditor5-math/src/math";
import AutoformatMath from "@isaul32/ckeditor5-math/src/autoformatmath";
declare class ClassicEditorCustom extends ClassicEditorBase {
    static builtinPlugins: (typeof Autoformat | typeof BlockQuote | typeof Bold | typeof CloudServices | typeof Essentials | typeof Heading | typeof Image | typeof ImageCaption | typeof ImageResize | typeof ImageStyle | typeof ImageToolbar | typeof ImageUpload | typeof Indent | typeof Italic | typeof List | typeof Paragraph | typeof PasteFromOffice | typeof Table | typeof TableToolbar | typeof TextTransformation | typeof Undo | typeof Mathematics | typeof AutoformatMath)[];
    static defaultConfig: {
        toolbar: {
            items: string[];
        };
        language: string;
        image: {
            toolbar: string[];
        };
        table: {
            contentToolbar: string[];
        };
        math: {
            engine: string;
            lazyLoad: undefined;
            outputType: string;
            forceOutputType: boolean;
            enablePreview: boolean;
            previewClassName: never[];
            popupClassName: never[];
        };
    };
}
declare class InlineEditorCustom extends InlineEditorBase {
    static builtinPlugins: (typeof Autoformat | typeof BlockQuote | typeof Bold | typeof CloudServices | typeof Essentials | typeof Heading | typeof Image | typeof ImageCaption | typeof ImageResize | typeof ImageStyle | typeof ImageToolbar | typeof ImageUpload | typeof Indent | typeof Italic | typeof List | typeof Paragraph | typeof PasteFromOffice | typeof Table | typeof TableToolbar | typeof TextTransformation | typeof Undo | typeof Mathematics | typeof AutoformatMath)[];
    static defaultConfig: {
        toolbar: {
            items: string[];
        };
        language: string;
        image: {
            toolbar: string[];
        };
        table: {
            contentToolbar: string[];
        };
        math: {
            engine: string;
            lazyLoad: undefined;
            outputType: string;
            forceOutputType: boolean;
            enablePreview: boolean;
            previewClassName: never[];
            popupClassName: never[];
        };
    };
}
declare class InlineEditTest extends InlineEditorBase {
    static builtinPlugins: (typeof Autoformat | typeof Bold | typeof Essentials | typeof Heading | typeof Italic | typeof TextTransformation | typeof Undo | typeof Mathematics | typeof AutoformatMath)[];
    static defaultConfig: {
        toolbar: {
            items: string[];
        };
        language: string;
        math: {
            engine: string;
            lazyLoad: undefined;
            outputType: string;
            forceOutputType: boolean;
            enablePreview: boolean;
            previewClassName: never[];
            popupClassName: never[];
        };
    };
}
declare class InlineEditorOnlyMath extends InlineEditorBase {
    static builtinPlugins: (typeof Autoformat | typeof Bold | typeof Essentials | typeof Heading | typeof Italic | typeof TextTransformation | typeof Undo | typeof Mathematics | typeof AutoformatMath)[];
    static defaultConfig: {
        toolbar: {
            items: string[];
        };
        language: string;
        math: {
            engine: string;
            lazyLoad: undefined;
            outputType: string;
            forceOutputType: boolean;
            enablePreview: boolean;
            previewClassName: never[];
            popupClassName: never[];
        };
    };
}
declare const _default: {
    InlineEditTest: typeof InlineEditTest;
    ClassicEditorCustom: typeof ClassicEditorCustom;
    InlineEditorCustom: typeof InlineEditorCustom;
    InlineEditorOnlyMath: typeof InlineEditorOnlyMath;
};
export default _default;
