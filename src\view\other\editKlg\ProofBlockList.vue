<script setup lang="ts">
import inlineCKEditor from '@/components/editors/VeditorInline.vue';
import CondDialog from '@/view/other/editKlg/CondDialog.vue';

import { klgProofCondSortDict } from '@/utils/constant';
import { ProofCondItem, ProofListItem } from '@/utils/type';
import { FormInstance } from 'element-plus';
import { ref } from 'vue';
const editorCondition = ref();
const editorResult = ref();
const proofBlockRef = ref<FormInstance>();
const dialogRef = ref();

const emits = defineEmits(['save:draft']);
const props = defineProps({
  proofList: {
    type: Array as () => ProofListItem[],
    required: true
  }
});

// 处理添加论证块
const handleAddProofBlock = (blockIndex: number) => {
  if (!proofBlockRef.value) return;
  proofBlockRef.value.validateField([`proofList.${blockIndex}.conclusion`], (valid) => {
    if (valid && props.proofList) {
      const blankProofBlock: ProofListItem = {
        klgProofBlockId: null,
        klgProofBlockOrderNum: blockIndex + 1,
        conclusion: '',
        klgProofCondList: [
          {
            klgProofCondId: null,
            sort: 2,
            refId: '',
            cnt: '',
            klgProofCondOrderNum: 0
          }
        ],
        isDel: true
      };
      props.proofList.splice(blockIndex + 1, 0, blankProofBlock);
    }
  });
};
// 处理删除论证块
const handleDelProofBlock = (blockIndex: number) => {
  if (props.proofList) {
    props.proofList.splice(blockIndex, 1);
  }
};
// 处理添加条件
const handleAddProofCond = (block: ProofListItem, condIndex: number) => {
  const blankProofCond: ProofCondItem = {
    klgProofCondId: null,
    sort: 2,
    refId: '',
    cnt: '',
    klgProofCondOrderNum: condIndex
  };
  block.klgProofCondList.splice(condIndex + 1, 0, blankProofCond);
};
// 处理删除条件
const handleDelProofCond = (block: ProofListItem, condIndex: number) => {
  props.proofList[Number(block.klgProofCondList[condIndex].refId)].isDel = true;
  block.klgProofCondList.splice(condIndex, 1);
};
// 筛选内部条件
const filterInnerProofCondList = (index: number) => {
  return props.proofList
    ?.filter((item, i) => i < index && item.conclusion !== '')
    .map((item) => {
      return {
        klgCode: item.klgProofBlockOrderNum,
        title: item.conclusion
      };
    });
};

// 处理提交
const handleSubmit = async (): Promise<Boolean> => {
  try {
    if (!proofBlockRef.value) return false;
    const valid = await proofBlockRef.value.validate();
    return valid;
  } catch (error) {
    return false;
  }
};

// 处理dialog
const handleDialog = (mode: number, blockIndex: number, condIndex: number, data?: any) => {
  // 0: 内部条件 | 1: 内部条件
  if (mode === 0) {
    dialogRef.value.showDialog(mode, blockIndex, condIndex, data);
  } else {
    dialogRef.value.showDialog(mode, blockIndex, condIndex);
  }
};
// 处理选择
const handleSelectCond = (data: any) => {
  props.proofList[data.blockIndex].klgProofCondList[data.condIndex].refId = data.code;
  props.proofList[data.blockIndex].klgProofCondList[data.condIndex].cnt = data.title;
};

const handleEmitToSaveDraft = () => {
  emits('save:draft');
};
defineExpose({
  handleSubmit
});
</script>
<template>
  <div class="wrapper">
    <el-form :model="props" ref="proofBlockRef">
      <div v-for="(block, blockIndex) in props.proofList" :key="blockIndex" class="proof-block">
        <div class="proof-block-title">论证块{{ blockIndex + 1 }}</div>
        <div
          style="width: 100%; margin-bottom: 7px"
          v-for="(cond, condIndex) in block.klgProofCondList"
          :key="condIndex"
        >
          <div class="proof-block-premise">
            <span style="width: 10%; margin-right: 10px">
              <el-select
                v-model="cond.sort"
                placeholder="请选择"
                style="width: 100px"
                @change="cond.cnt = ''"
              >
                <el-option
                  v-for="(key, value) in klgProofCondSortDict"
                  :key="value"
                  :label="value"
                  :value="key"
                  class="primary"
                />
              </el-select>
            </span>
            <el-form-item
              style="width: 100%"
              :prop="`proofList.${blockIndex}.klgProofCondList.${condIndex}.cnt`"
              :rules="{
                required: true,
                message: '请输入条件',
                trigger: 'blur'
              }"
            >
              <span style="width: 100%">
                <div v-if="cond.sort === 0" style="display: flex; flex-direction: row">
                  <span
                    class="show-outer-block ck-content"
                    v-if="cond.cnt !== ''"
                    v-html="cond.cnt"
                  >
                  </span>
                  <span
                    :class="cond.cnt === '' ? 'outer-btn1' : 'outer-btn2'"
                    class="outer-btn"
                    @click="
                      handleDialog(0, blockIndex, condIndex, filterInnerProofCondList(blockIndex))
                    "
                  >
                    请点击选择内部条件
                  </span>
                </div>
                <div v-else-if="cond.sort === 1" style="display: flex; flex-direction: row">
                  <span
                    class="show-outer-block ck-content"
                    v-if="cond.cnt !== ''"
                    v-html="cond.cnt"
                  >
                  </span>
                  <span
                    :class="cond.cnt === '' ? 'outer-btn1' : 'outer-btn2'"
                    class="outer-btn"
                    @click="handleDialog(1, blockIndex, condIndex)"
                  >
                    请点击选择外部条件
                  </span>
                </div>
                <inlineCKEditor
                  v-else
                  v-model="cond.cnt"
                  placeholder="请输入条件"
                  :height="35"
                  ref="editorCondition"
                ></inlineCKEditor>
              </span>
            </el-form-item>
            <span class="op-btn-group">
              <span class="op-btn">
                <img
                  src="@/assets/image/klg/u3993.svg"
                  @click="handleAddProofCond(block, condIndex)"
                />
              </span>
              <span v-if="block.klgProofCondList.length > 1" class="op-btn">
                <img
                  src="@/assets/image/klg/u3994.svg"
                  @click="handleDelProofCond(block, condIndex)"
                />
              </span>
              <span v-else class="op-btn" style="cursor: not-allowed">
                <img src="@/assets/image/klg/u3999.svg" />
              </span>
            </span>
          </div>
        </div>
        <div class="proof-block-conclusion">
          <span class="proof-conclusion-text"> 论证结论 </span>
          <el-form-item
            style="width: 100%"
            :prop="`proofList.${blockIndex}.conclusion`"
            :rules="{
              required: true,
              message: '请输入结论',
              trigger: 'blur'
            }"
          >
            <span style="width: 100%">
              <inlineCKEditor
                v-model="block.conclusion"
                placeholder="请输入结论"
                :height="35"
                ref="editorResult"
              ></inlineCKEditor>
            </span>
          </el-form-item>
          <span class="op-btn-group">
            <span class="op-btn">
              <img src="@/assets/image/klg/u3993.svg" @click="handleAddProofBlock(blockIndex)" />
            </span>
            <span v-if="props.proofList?.length > 1 && block.isDel" class="op-btn">
              <img src="@/assets/image/klg/u3994.svg" @click="handleDelProofBlock(blockIndex)" />
            </span>
            <span v-else class="op-btn" style="cursor: not-allowed">
              <img src="@/assets/image/klg/u3999.svg" />
            </span>
          </span>
        </div>
      </div>
    </el-form>
  </div>
  <!-- other -->
  <cond-dialog ref="dialogRef" @select="handleSelectCond"></cond-dialog>
</template>
<style scoped>
.op-btn-group {
  display: flex;
  flex-direction: row;
  align-items: center;
  .op-btn {
    cursor: pointer;
    display: flex;
    align-items: center;
    margin-left: 5px;
    user-select: none;
  }
}
.wrapper {
  width: 100%;
  :deep(.el-form-item) {
    margin-bottom: 7px;
  }
  .proof-block {
    width: 100%;
    margin-bottom: 7px;
    .proof-block-title {
      width: 98.2%;
      padding: 7px 10px;
      background-color: var(--color-primary);
      color: white;
      font-size: 12px;
      font-weight: 600;
      margin-bottom: 14px;
    }
    .proof-block-premise {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-top: 7px;
      .show-outer-block {
        width: 100%;
        border-radius: 5px;
        border: 1px solid var(--color-boxborder);
        padding: 0 7px;
      }
      .outer-btn {
        border: 1px solid var(--color-boxborder);
        border-radius: 5px;
        padding: 0 10px;
        cursor: pointer;
        font-size: 12px;
        font-weight: 600;
        background-color: var(--color-light);
        margin-left: 1px;
      }
      .outer-btn1 {
        width: 100%;
      }
      .outer-btn2 {
        margin-left: 10px;
        white-space: nowrap;
      }
    }
    .proof-block-conclusion {
      width: 100%;
      display: flex;
      flex-direction: row;
      .proof-conclusion-text {
        background-color: var(--color-light);
        border: 1px solid var(--color-boxborder);
        border-radius: 3px;
        padding: 0 12.5px;
        font-size: 14px;
        display: flex;
        align-items: center;
        width: 112px;
        height: 30px;
        margin-right: 10px;
      }
    }
  }
}
:deep(.vditor-reset) {
  overflow-y: hidden;
}
</style>
