<script setup lang="ts">
import MyFlipper from '@/components/MyFlipper.vue';

import { onMounted, onUnmounted, ref, watch } from 'vue';
import {
  invalidQuestionApi,
  getPreKlgQuesListApi,
  autoRelationApi,
  deleteQuesApi,
  publishQuesApi,
  rebackQuesApi
} from '@/apis/path/klg';
import type { params2GetPreKlgList, params2Ques, params2Invalid } from '@/apis/path/klg';
import { QuestionTypeDict, MAX_PAGESIZE, klgQuestionAble, klgAuditType } from '@/utils/constant';
import { ElMessage, ElTable, ElMessageBox } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { findKeyByValue } from '@/utils/func';
import { emitter } from '@/utils/emitter';
import { Event } from '@/types/event';
import { useRoute } from 'vue-router';

const tableRef = ref<InstanceType<typeof ElTable>>();
const curMode = ref(-1);
const currentPage = ref(1);
const pageSize = MAX_PAGESIZE;
const total = ref(0);
const route = useRoute();
const curHeaderMode = ref(1); // 未发布：1 | 已发布： 0 但是 传给后端的是相反的，这块我好难受啊啊啊啊啊
const curKlgCode = ref('');
const tableData = ref<any[]>([]); // 表格数据
const selectionKlgList = ref([]); // 选择的KlgList

// 筛选相关变量
const searchKeyword = ref(''); // 搜索关键字
const selectedQuestionType = ref(''); // 选择的问题类型
const questionTypeOptions = ref([
  { label: '全部问题类型', value: '' },
  { label: '是什么', value: '是什么' },
  { label: '为什么', value: '为什么' }
]);

// 计算序号的方法
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize + index + 1;
};
// 初始化数据
const initData = () => {
  curKlgCode.value = route.query.klgCode!.toString();
  getPreKlgQuesList();
};

// 设置模式
const setMode = (mode: number) => {
  curKlgCode.value = route.query.klgCode!.toString();
  if (mode === klgAuditType.published) {
    curHeaderMode.value = 0;
    curMode.value = 1;
  } else {
    curMode.value = 0;
  }
  getPreKlgQuesList();
};
// 处理表格选择
const handleSelectionChange = (list: []) => {
  selectionKlgList.value = list;
};
// 处理换页
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  getPreKlgQuesList();
};
// 自动关联
const autoRelation = () => {
  autoRelationApi(curKlgCode.value).then((res) => {
    if (res.success) {
      ElMessage.success('匹配成功!');
      getPreKlgQuesList();
    } else {
      ElMessage.error('匹配失败!');
    }
  });
};
// 移除 handleChangeHeader 函数，因为不再需要模式切换
// 筛选功能
const handleSearch = () => {
  currentPage.value = 1; // 重置到第一页
  getPreKlgQuesList();
};

const handleQuestionTypeChange = () => {
  currentPage.value = 1; // 重置到第一页
  getPreKlgQuesList();
};

// 获取前驱知识的问题列表
const getPreKlgQuesList = () => {
  const params: params2GetPreKlgList = {
    current: currentPage.value,
    limit: pageSize,
    klgCode: curKlgCode.value,
    questionType: selectedQuestionType.value,
    keyword: searchKeyword.value
  };
  getPreKlgQuesListApi(params).then((res) => {
    if (res.success) {
      // 直接使用后端返回的筛选结果
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    }
  });
};
// 处理显示问题
const handleShowQuestion = (data: any) => {
  emitter.emit(Event.SHOW_DRAWER, data);
};
// 处理批量发布和撤回
const handleMul = (mode: number) => {
  const selectedRows = tableRef.value?.getSelectionRows() || [];
  if (selectedRows.length === 0) {
    ElMessage.warning('请至少选择一个问题！');
    return;
  }

  const list = selectedRows.map((item: any) => item.questionId);

  // 根据模式显示不同的确认对话框
  let title = '';
  let message = '';

  if (mode === 2) {
    // 批量发布
    title = '批量发布确认';
    message = `确定要发布选中的 ${selectedRows.length} 个问题吗？`;
  } else if (mode === 3) {
    // 批量撤回
    title = '批量撤回确认';
    message = `确定要撤回选中的 ${selectedRows.length} 个问题吗？`;
  }

  ElMessageBox.confirm(message, title, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      if (mode === 2) {
        // 执行批量发布
        handleSend(list);
      } else if (mode === 3) {
        // 执行批量撤回
        handleSendBack(list);
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};
// 处理发布
const handleSend = (list: any[]) => {
  const params: params2Ques = {
    questionList: list
  };
  publishQuesApi(params).then((res) => {
    if (res.success) {
      if (list.length === 1) {
        ElMessage.success('发布成功');
      } else {
        ElMessage.success(`成功发布 ${list.length} 个问题`);
        // 只有批量操作才清空选择
        tableRef.value?.clearSelection();
      }
      getPreKlgQuesList();
    } else {
      ElMessage.error(res.message);
    }
  });
};
// 处理批量生效失效
// const handleMulState = () => {
//   const list = tableRef.value?.getSelectionRows().map((item: any) => item.questionId) || [];
//   if (list.length > 0) {
//     handleState(list);
//   } else {
//     ElMessage.warning('请至少选择一个问题！');
//   }
// };
// // 处理生效失效
// const handleState = (list: any[]) => {
//   const params: params2Invalid = {
//     questionIdList: list
//   };
//   invalidQuestionApi(params).then((res) => {
//     if (res.success) {
//       ElMessage.success('修改成功');
//       getPreKlgQuesList();
//     } else {
//       ElMessage.error(res.message);
//     }
//   });
// };
// 处理取消发布
const handleSendBack = (list: any[]) => {
  const params: params2Ques = {
    questionList: list
  };
  rebackQuesApi(params).then((res) => {
    if (res.success) {
      if (list.length === 1) {
        ElMessage.success('撤回成功');
      } else {
        ElMessage.success(`成功撤回 ${list.length} 个问题`);
        // 只有批量操作才清空选择
        tableRef.value?.clearSelection();
      }
      getPreKlgQuesList();
    } else {
      ElMessage.error(res.message);
    }
  });
};
// 处理编辑 - 参考 KlgInfo.vue 的 handleAddQuestion 实现
const handleEdit = (data: any) => {
  // 构造编辑问题的数据，参考 KlgInfo.vue 中 handleAddQuestion 的数据结构
  const editData = {
    mode: 1, // 编辑/增加问题模式
    questionId: data.questionId, // 编辑时需要问题ID
    associatedWords: data.keyword || data.associatedWords, // 关联词
    keyword: data.keyword, // 问题关键词
    klgCode: curKlgCode.value, // 当前知识编码
    answers: data.answers || [] // 答案数组，避免undefined错误
  };

  // 使用 emitter 发送事件，触发显示问题编辑对话框
  emitter.emit(Event.SHOW_QUESTION_DIALOG, editData);
};
// 处理批量删除
const handleBatchDelete = () => {
  const selectedRows = tableRef.value?.getSelectionRows() || [];
  if (selectedRows.length === 0) {
    ElMessage.warning('请至少选择一个问题！');
    return;
  }

  ElMessageBox.confirm(`确定要删除选中的 ${selectedRows.length} 个问题吗？`, '批量删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // 批量删除逻辑 - 收集所有问题ID，发送一次请求
      const questionIdList = selectedRows.map((row: any) => row.questionId);
      console.log('questionIdList', questionIdList);
      const params: any = {
        questionIds: questionIdList
      };

      deleteQuesApi(params).then((res) => {
        if (res.success) {
          ElMessage.success(`成功删除 ${selectedRows.length} 个问题`);
        } else {
          ElMessage.error(res.message || '删除失败');
        }

        getPreKlgQuesList();
        // 清空选择
        tableRef.value?.clearSelection();
      });
    })
    .catch(() => {
      // 用户取消删除
    });
};

// 处理删除
const handleDel = (data: any) => {
  const params: any = {
    questionIds: [data]
  };
  deleteQuesApi(params).then((res) => {
    if (res.success) {
      ElMessage.success('删除成功');
      getPreKlgQuesList();
      emitter.emit(Event.REMOVE_QUESTION, data);
    } else {
      ElMessage.error(res.message);
    }
  });
};
// // 判断表格是否可以选择
// const isSelectable = (row: any) => {
//   // 调试信息
//   console.log('isSelectable调试:', {
//     questionId: row.questionId,
//     questionStatus: row.questionStatus,
//     questionAble: row.questionAble,
//     canPublish: row.canPublish,
//     curMode: curMode.value,
//     curHeaderMode: curHeaderMode.value
//   });

//   // 基础选择条件
//   let baseSelectable = false;

//   // 判断curMode是否为0
//   if (curMode.value !== 1) {
//     baseSelectable = row.questionAble !== klgQuestionAble.disable;
//   } else {
//     if (curHeaderMode.value === 1) {
//       baseSelectable = row.questionAble === klgQuestionAble.disable;
//     } else {
//       baseSelectable = true;
//     }
//   }

//   console.log('基础选择条件结果:', baseSelectable);

//   // 如果基础条件不满足，直接返回false
//   if (!baseSelectable) {
//     console.log('基础条件不满足，返回false');
//     return false;
//   }

//   // 对于待发布状态的问题，还需要检查canPublish字段
//   if (row.questionStatus === 0) {
//     const canSelect = row.canPublish === true;
//     console.log('待发布状态检查:', { canPublish: row.canPublish, canSelect });
//     return canSelect;
//   }

//   // 其他状态的问题按原逻辑处理
//   console.log('其他状态，返回基础条件结果:', baseSelectable);
//   return baseSelectable;
// };

onMounted(() => {
  emitter.on(Event.SET_STATUS, setMode);
  emitter.on(Event.REFRESH_QUESTION, getPreKlgQuesList);
  // 初始化数据
  initData();
});
onUnmounted(() => {
  emitter.off(Event.REFRESH_QUESTION, getPreKlgQuesList);
  emitter.off(Event.SET_STATUS, setMode);
});
</script>
<template>
  <div class="listWrapper textfont">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-row">
        <el-select
          v-model="selectedQuestionType"
          placeholder="全部问题类型"
          @change="handleQuestionTypeChange"
          style="width: 180px; height: 35px; margin-right: 10px"
        >
          <el-option
            v-for="option in questionTypeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>

        <el-input
          v-model="searchKeyword"
          placeholder="请输入关键字"
          @keydown.enter="handleSearch"
          @clear="handleSearch"
          clearable
          style="width: 260px; height: 33px; margin-right: 10px"
        >
          <template #suffix>
            <el-icon @click="handleSearch" style="cursor: pointer">
              <Search />
            </el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 批量操作按钮区域 -->
    <div class="header">
      <div class="header-left">
        <span class="batch-btn" @click="handleBatchDelete">
          <span class="batch-btn-text">批量删除</span>
        </span>

        <span class="batch-btn" @click="handleMul(2)" style="margin-left: 10px">
          <span class="batch-btn-text">批量发布</span>
        </span>

        <span class="batch-btn" @click="handleMul(3)" style="margin-left: 10px">
          <span class="batch-btn-text">批量撤回</span>
        </span>
      </div>
    </div>
    <div class="tableContainer">
      <div class="line"></div>
      <!-- 统一的问题列表表格 -->
      <el-table
        :data="tableData"
        ref="tableRef"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        :row-style="{ height: '46px' }"
        :cell-style="{ height: '46px', width: '100%' }"
        empty-text="暂无数据"
      >
        <el-table-column type="selection" width="30" />
        <el-table-column type="index" :index="indexMethod" label="序号" width="60" align="center" />
        <el-table-column label="关键字" width="300">
          <template #default="scope">
            <span class="questionList ellipsis-text" v-html="scope.row.keyword"> </span>
          </template>
        </el-table-column>
        <el-table-column label="问题类型" width="80" align="center">
          <template #default="scope">{{
            findKeyByValue(scope.row.sort, QuestionTypeDict)
          }}</template>
        </el-table-column>
        <el-table-column label="提问人" width="90" align="center">
          <template #default="scope">
            {{ scope.row.creatorName || '乔治' }}
          </template>
        </el-table-column>
        <el-table-column label="提问时间" width="165" align="center">
          <template #default="scope">
            <span class="textfont"> {{ scope.row.createTime || '2023-02-28 12:09:15' }} </span>
          </template>
        </el-table-column>
        <el-table-column label="知识数" width="70" align="center">
          <template #default="scope">
            {{ scope.row.klgCount || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="70" align="center">
          <template #default="scope">
            <span>
              {{ scope.row.questionStatus === 0 ? '待发布' : '已发布' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="203" align="center">
          <template #default="scope">
            <span style="display: flex; justify-content: center; gap: 5px">
              <el-button
                link
                class="op-btn"
                @click="handleShowQuestion(scope.row)"
                v-if="scope.row.questionAble === klgQuestionAble.disable || scope.row.flag === 0"
              >
                查看
              </el-button>

              <el-button
                link
                class="op-btn"
                @click="handleEdit(scope.row)"
                v-if="scope.row.flag === 0"
                >编辑
              </el-button>
              <el-button
                link
                class="op-btn"
                :class="{ 'op-btn-disabled': !scope.row.canPublish }"
                @click="scope.row.canPublish ? handleSend([scope.row.questionId]) : null"
                v-if="scope.row.questionStatus === 0"
                :disabled="!scope.row.canPublish"
              >
                发布
              </el-button>
              <el-button
                v-if="scope.row.questionStatus === 1"
                link
                class="op-btn"
                @click="handleSendBack([scope.row.questionId])"
                >撤回
              </el-button>
              <!-- 删除-->
              <el-button
                link
                class="op-btn"
                @click="handleDel(scope.row.questionId)"
                v-if="scope.row.flag === 0"
              >
                删除
              </el-button>
              <!-- 已发布问题的操作 -->
            </span>
          </template>
        </el-table-column>
      </el-table>
      <my-flipper
        @change-page="handleChangePage"
        :current="currentPage"
        :page-size="pageSize"
        :total="total"
      ></my-flipper>
    </div>
  </div>
</template>
<style scoped>
:deep(p) {
  margin: 0;
}

.listWrapper {
  width: 100%;
  background-color: white;
  display: flex;
  flex-direction: column !important;

  .filter-section {
    padding: 16px 0;
    position: relative;
    .filter-row {
      display: flex;
      align-items: center;
      gap: 10px;
      position: absolute;
      right: 0;
    }
  }

  .header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 16px 0;

    .header-left {
      display: flex;
      align-items: center;

      .batch-btn {
        display: flex;
        align-items: center;
        justify-items: center;
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.3s;
        width: 120px;
        height: 35px;
        background-color: rgba(22, 97, 171, 0.996);

        box-sizing: border-box;

        &:hover {
          /* border: 1px solid var(--color-primary); */
          background-color: #c5d5ea;

          .batch-btn-text {
            color: var(--color-primary);
          }

          img {
            filter: brightness(0) invert(1);
          }
        }

        .batch-btn-text {
          margin: 0 auto;
          color: white;
          transition: color 0.3s;
        }
      }
    }

    .header-right {
      display: flex;
      flex-direction: row;

      .header-btn {
        display: flex;
        align-items: center;
        cursor: pointer;

        .header-btn-text {
          margin-left: 2px;
          font-size: 12px;
          color: var(--color-primary);
        }
      }
    }
  }
}

.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}

.tableContainer {
  width: 100%;
}
:deep(.op-btn) {
  color: var(--color-primary);
  /* color: var(--color-boxborder); */
  &:hover {
    color: var(--color-primary);
    font-weight: bold !important;
  }
}

:deep(.op-btn-disabled) {
  color: var(--color-boxborder) !important;
  cursor: default !important;
  pointer-events: none;

  &:hover {
    color: var(--color-boxborder) !important;
    font-weight: normal !important;
    cursor: default !important;
  }
}
</style>
