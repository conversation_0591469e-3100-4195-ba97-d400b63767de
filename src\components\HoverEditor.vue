<script setup lang="ts">
defineProps<{
  label?: string
}>()
const emit = defineEmits(["edit"])
const handleEmit = () => {
  emit("edit")
}
</script>
<template>
  <div class="hover-container">
    <span class="hover-content">
      <slot></slot>
    </span>
    <span class="hover-block" @click="handleEmit">
      <el-icon class="hover-icon" color="var(--color-primary)"
        ><Edit
      /></el-icon>
      <span class="hover-text">{{ label }}</span>
    </span>
  </div>
</template>
<style scoped lang="less">
.hover-container {
  display: flex;
}

/* 鼠标悬停时，显示 .hover-block */
.hover-container:hover .hover-block {
  display: flex;
}

.hover-content {
}

.hover-block {
  display: none; /* 默认不显示 */
  cursor: pointer;
  margin-left: 10px;
}

.hover-icon {
  display: flex;
  align-items: center;
  margin-right: 5px;
  margin-top: 10px;
}

.hover-text {
  color: var(--color-primary);
  font-size: 10px;
  text-wrap: nowrap;
}
</style>
