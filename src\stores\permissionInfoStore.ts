import { defineStore } from "pinia"
import { ref } from "vue"

export const permissionInfoStore = defineStore("permissionInfo", () => {
  const permissionInfo = ref({
    klgShow: false,
    klgEdit: false,
  })

  const setKlgShow = (state: boolean) => {
    permissionInfo.value.klgShow = state
  }
  const setKlgEdit = (state: boolean) => {
    permissionInfo.value.klgEdit = state
  }
  const getPerInfo = () => permissionInfo.value

  return {
    setKlgShow,
    setKlgEdit,
    getPerInfo,
  }
})
