import type { ChildN<PERSON>, Element, Document } from 'domhandler';
import { ElementType, parseDocument } from 'htmlparser2';

export function getVisibleLatex(htmlString: string) {
  let str = '';
  const dfs = (node: ChildNode) => {
    if (node.type == ElementType.Text) {
      str += node.data;
    } else if (node.type == ElementType.Tag) {
      for (const child of (node as Element).children) {
        dfs(child);
      }
    } else {
      for (const child of (node as Document).children) {
        dfs(child);
      }
    }
  };
  dfs(
    parseDocument(htmlString, {
      decodeEntities: true
    })
  );
  return str;
}
