<script setup lang="ts">
import { ref } from "vue"
import { AuditItem } from "@/utils/type"
import { findKeyByValue } from "@/utils/func"
import { auditTypeDict } from "@/utils/constant"

const drawerVisible = ref(false)
const curMode = ref(-1) // 0: 显示问题 | 1: 审核意见
const titleText = ref("")
const auditForm = ref<AuditItem>({
  id: 0,
  auditOpinion: "",
  auditResult: "",
  createTime: "",
  processorName: "",
  actionName: "",
})
const curData = ref<any>()
// 展示drawer
const showDrawer = (mode: number, data: any) => {
  drawerVisible.value = true
  curMode.value = mode
  switch (mode) {
    case 0:
      titleText.value = "显示问题"
      curData.value = data
      // console.log("data", data)
      break
    case 1:
      titleText.value = "审核意见"
      auditForm.value = data
      break
  }
}
// 处理点击klg
const handleClickKlg = (item: any) => {
  window.open(`/klgdetail?klgCode=${item.klgCode}`, `_self`)
}
defineExpose({
  showDrawer,
})
</script>
<template>
  <el-drawer v-model="drawerVisible" direction="rtl">
    <template #header>
      <div class="header-text">{{ titleText }}</div>
    </template>
    <div class="wrapper">
      <div class="question-container" v-if="curMode === 0">
        <div class="words-line image-fix">
          <span style="font-weight: 600; white-space: nowrap"
            >文本关联内容:
          </span>
          <span
            class="ck-content"
            style="word-break: break-all"
            v-html="(curData.associatedWords)"
          ></span>
        </div>
        <div class="keyword-line image-fix">
          【<span class="keyword">
            <span
              class="ck-content"
              style="word-break: break-all"
              v-html="(curData.keyword)"
            ></span>
          </span>】
          <span class="type">{{ curData.questionType }}</span>
        </div>
        <div>关联结果</div>
        <div class="relation-list">
          <div v-if="curData.answers.length === 0">暂无关联结果</div>
          <div
            v-else
            class="relation-line"
            v-for="(item, index) in curData.answers"
            :key="index"
            @click="handleClickKlg(item)"
          >
            <span class="klgtitle" v-html="item.title"></span>
            <img
              src="@/assets/image/klg/u1705.svg"
              height="12"
              style="margin-right: 12px"
            />
          </div>
        </div>
      </div>
      <div class="audit-container" v-else-if="curMode === 1">
        <el-form>
          <el-form-item label="审核时间:">
            {{ auditForm.createTime }}
          </el-form-item>
          <el-form-item label="审核环节:">
            {{ auditForm.actionName }}
          </el-form-item>
          <el-form-item label="审核人:">
            {{ auditForm.processorName ? auditForm.processorName : "-" }}
          </el-form-item>
          <el-form-item label="审核结果:">
            {{
              findKeyByValue(auditForm.auditResult as string, auditTypeDict)
                ? findKeyByValue(auditForm.auditResult as string, auditTypeDict)
                : "-"
            }}
          </el-form-item>
          <el-form-item label="审核意见:">
            <div
              v-if="
                !(
                  auditForm.auditOpinion !== null &&
                  auditForm.auditOpinion !== ''
                )
              "
            >
              -
            </div>
          </el-form-item>
          <el-form-item class="review-block-item">
            <div
              class="review-block"
              v-if="
                auditForm.auditOpinion !== null && auditForm.auditOpinion !== ''
              "
            >
              <span class="ck-content" v-html="auditForm.auditOpinion"></span>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </el-drawer>
</template>
<style scoped>
.image-fix {
  :deep(img) {
    max-width: 80px;
    height: auto;
  }
}

.header-text {
  color: var(--color-black);
  font-weight: 600;
}
.wrapper {
  font-size: 14px;
  padding: 20px 10px;
  color: var(--color-black);
  .question-container {
    .words-line {
      background-color: var(--color-light);
      padding: 5px 10px;
      width: 100%;
      display: inline-flex;
      :deep(p) {
        margin: 0;
      }
    }
    .keyword-line {
      margin: 15px 0;
      display: inline-flex;
      .keyword {
        font-weight: 600;
      }
      .type {
        display: flex;
        align-items: center;
        margin-left: 5px;
        min-width: 42px;
      }
      :deep(p) {
        margin: 0;
      }
    }
    .relation-list {
      margin-top: 5px;
      background-color: var(--color-light);
      padding: 10px;
      width: 100%;
      display: inline-flex;
      :deep(p) {
        margin: 0;
      }
      .relation-line {
        width: 100%;
        background-color: white;
        border: 1px solid var(--color-boxborder);
        padding: 5px 10px;
        border-radius: 3px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        .klgtitle {
        }
        .klgarea {
          margin-left: 10px;
        }
      }
    }
  }
  .audit-container {
    width: 100%;
    :deep(.el-form-item__content) {
      width: 100%;
    }
    .review-block-item {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      .review-block {
        width: 100%;
        padding: 0 10px;
        background-color: var(--color-light);
        border: 1px solid var(--color-boxborder);
      }
    }
  }
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
</style>
<style>
.el-drawer__header {
  margin: 0;
}
</style>