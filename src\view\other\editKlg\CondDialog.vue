<script setup lang="ts">
import MyFlipper from '@/components/MyFlipper.vue';

import { computed, onMounted, ref } from 'vue';
import { getOuterKlgListApi, params2GetOuterList } from '@/apis/path/klg';
import { MAX_PAGESIZE } from '@/utils/constant';
import { processAllLatexEquations } from '@/utils/latexUtils';

const dialogVisible = ref(false);
const tableData = ref<any[]>([]);
const AllInnerData = ref(); // 搜索备份
const searchKey = ref('');
const currentPage = ref(1);
const pageSize = MAX_PAGESIZE;
const total = ref(0);

const curBlockIndex = ref(); // 当前论证块index
const curCondIndex = ref(); // 当前条件index
const curMode = ref(-1); // 0: 内部条件 | 1: 外部条件
const titleText = ref('');

const emits = defineEmits(['select']);

// 展示对话框
const showDialog = (mode: number, blockIndex: number, condIndex: number, data?: any) => {
  curMode.value = mode;
  dialogVisible.value = true;
  curBlockIndex.value = blockIndex;
  curCondIndex.value = condIndex;
  switch (mode) {
    case 0:
      titleText.value = '内部条件选择';
      tableData.value = data;
      AllInnerData.value = data;
      break;
    case 1:
      titleText.value = '外部条件选择';
      getOuterList(currentPage.value);
      break;
  }
};
// 获取外部条件列表
const getOuterList = (page?: number) => {
  const params: params2GetOuterList = {
    current: page ? page : currentPage.value,
    limit: pageSize,
    keyword: searchKey.value
  };
  getOuterKlgListApi(params).then((res) => {
    if (res.success) {
      total.value = res.data.total;
      tableData.value = res.data.list;
    }
  });
};
// 处理搜索
const handleSearch = () => {
  if (curMode.value === 0) {
    if (searchKey.value !== '') {
      tableData.value = AllInnerData.value.filter((item) => item.title.includes(searchKey.value));
    } else {
      tableData.value = AllInnerData.value;
    }
  } else if (curMode.value === 1) {
    getOuterList();
  }
};
// 处理换页
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  getOuterList(newPage);
};
// 计算列表index
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize + index + 1;
};

// 处理选择
const handleSelect = (item: any) => {
  const data = {
    mode: curMode.value,
    code: item.klgCode,
    title: item.title,
    blockIndex: curBlockIndex.value,
    condIndex: curCondIndex.value
  };
  emits('select', data);
  dialogVisible.value = false;
};
defineExpose({
  showDialog
});
</script>
<template>
  <el-dialog v-model="dialogVisible" width="800" style="max-height: 700px">
    <template #header>
      <span>{{ titleText }}</span>
    </template>
    <div class="toolbar">
      <span style="white-space: nowrap; margin-right: 10px">知识名称</span>
      <el-input
        v-model="searchKey"
        placeholder="请输入知识名称"
        @keydown.enter="handleSearch"
        style="margin-right: 10px"
        clearable
      >
        <template #suffix>
          <el-icon class="btn el-input__icon"><Search /></el-icon>
        </template>
      </el-input>
      <el-button type="primary" @click="handleSearch">搜索</el-button>
    </div>
    <el-table :data="tableData">
      <el-table-column type="index" :index="indexMethod" label="序号" width="60" align="center" />
      <el-table-column label="知识名称" width="600">
        <template #default="scope">
          <span class="ck-content" v-html="processAllLatexEquations(scope.row.title)"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button type="primary" @click="handleSelect(scope.row)">选择</el-button>
        </template>
      </el-table-column>
    </el-table>
    <my-flipper
      v-if="curMode === 1"
      @change-page="handleChangePage"
      :current="currentPage"
      :page-size="pageSize"
      :total="total"
    ></my-flipper>
  </el-dialog>
</template>
<style scoped>
.toolbar {
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>
