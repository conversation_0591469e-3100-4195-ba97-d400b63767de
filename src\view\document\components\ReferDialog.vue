<script setup lang="ts">
import CmpButton from "@/components/CmpButton.vue"
import Uploader from "@/components/uploader.vue"

import { ElMessage, FormInstance } from "element-plus"
import {
  addRefApi,
  editRefApi,
  getEditRefApi,
  params2EditRef,
} from "@/apis/path/document"
import type { params2AddRef } from "@/apis/path/document"
import { ref, defineEmits, nextTick } from "vue"
import { genFileId } from "element-plus"
import type { UploadInstance, UploadProps, UploadRawFile } from "element-plus"
import { uploadFileApi } from "@/apis/path/document"

const uploaderRef = ref()
const curRefId = ref()
const emit = defineEmits(["publish", "refresh"])
const dialogVisible = ref(false)
const literaryFormRef = ref<FormInstance>()
const webFormRef = ref<FormInstance>()
const otherFormRef = ref<FormInstance>()
const fileList = ref<any[]>([])
const formTitle = ref()
const btnList = ["著作资料", "网络资料", "其他资料"]
const curMode = ref(0) // 0: 新增文献 || 1: 批量发布 || 2: 编辑文献
const curReferMode = ref(0) // 0: 著作资料 || 1: 网络资料 || 2: 其他资料
const initLiteraryForm = {
  id: -1,
  author: "",
  bookName: "",
  pubPlace: "",
  pubHouse: "",
  pubYear: "",
  fileName: "",
} // 著作表单
const initWebForm = {
  id: -1,
  url: "",
  articleName: "",
  networkName: "",
  pubDate: "",
} // 网络表单
const initOtherForm = {
  id: -1,
  refDesc: "",
  fileName: "",
} // 其他表单
const literaryForm = ref(initLiteraryForm)
const webForm = ref(initWebForm)
const otherForm = ref(initOtherForm)

// 处理展示dialog
const showDialog = (mode: number, data?: any) => {
  dialogVisible.value = true
  curMode.value = mode
  switch (mode) {
    case 0:
      resetForm()
      curReferMode.value = 0
      formTitle.value = "新增文献参考"
      break
    case 1:
      formTitle.value = "发布文献"
      break
    case 2:
      formTitle.value = "编辑文献参考"
      setForm(data.id)
      break
  }
}
// 设置form
const setForm = (id: number) => {
  curRefId.value = id
  getEditRefApi(id).then((res) => {
    if (res.success) {
      curReferMode.value = res.data.refSortVo.flag
      if (curReferMode.value === 0) {
        literaryForm.value = res.data.refSortVo.authorInfo
        nextTick(() => {
          if (res.data.refSortVo.authorInfo.fileName) {
            uploaderRef.value.setFileNameList([
              res.data.refSortVo.authorInfo.fileName,
            ])
          }
        })
      } else if (curReferMode.value === 1) {
        webForm.value = res.data.refSortVo.networkInfo
      } else if (curReferMode.value === 2) {
        otherForm.value = res.data.refSortVo.otherInfo
        nextTick(() => {
          if (res.data.refSortVo.otherInfo.fileName) {
            uploaderRef.value.setFileNameList([
              res.data.refSortVo.otherInfo.fileName,
            ])
          }
        })

        // fileList.value.push(otherForm.value.fileName)
      }
    }
  })
}
// 重置form
const resetForm = () => {
  literaryForm.value = { ...initLiteraryForm }
  webForm.value = { ...initWebForm }
  otherForm.value = { ...initOtherForm }
}
// 处理切换表单
const handleChangeHeaderForm = (index: number) => {
  if (curReferMode.value !== index) {
    curReferMode.value = index
    resetForm()
  }
}

// 处理校验表单
const handleAddValidate = () => {
  if (curReferMode.value === 0) {
    literaryFormRef.value?.validate((valid) => {
      if (valid) {
        handleAddReference()
      }
    })
  } else if (curReferMode.value === 1) {
    webFormRef.value?.validate((valid) => {
      if (valid) {
        handleAddReference()
      }
    })
  } else if (curReferMode.value === 2) {
    otherFormRef.value?.validate((valid) => {
      if (valid) {
        handleAddReference()
      }
    })
  }
}

// 处理提交ref
const handleAddReference = () => {
  if (curMode.value === 0) {
    if (curReferMode.value !== 1) {
      uploaderRef.value.submitUpload().then((reskeys) => {
        if (reskeys.length > 1) return
        if (curReferMode.value === 0) {
          literaryForm.value.fileName = reskeys[0] ? reskeys[0] : ""
        } else {
          otherForm.value.fileName = reskeys[0] ? reskeys[0] : ""
        }
        const params: params2AddRef = {
          author: literaryForm.value.author,
          bookName: literaryForm.value.bookName,
          pubPlace: literaryForm.value.pubPlace, // 出版地
          pubHouse: literaryForm.value.pubHouse, // 出版社
          pubYear: getDateToYear(literaryForm.value.pubYear), // 出版年
          fileName:
            curReferMode.value === 0
              ? literaryForm.value.fileName
              : otherForm.value.fileName,
          url: webForm.value.url,
          articleName: webForm.value.articleName,
          networkName: webForm.value.networkName,
          pubDate: webForm.value.pubDate.toString(), // 出版日期
          refDesc: otherForm.value.refDesc,
          flag: curReferMode.value, // 资料类型 0: 著作|| 1: 网络|| 2: 其他
        }
        addRefApi(params).then((res) => {
          if (res.success) {
            curRefId.value = res.data.ref

            ElMessage.success("添加成功")
            emit("refresh")
            handleClose()
          } else {
            ElMessage.error(res.message)
          }
        })
      })
    } else {
      const params: params2AddRef = {
        author: literaryForm.value.author,
        bookName: literaryForm.value.bookName,
        pubPlace: literaryForm.value.pubPlace, // 出版地
        pubHouse: literaryForm.value.pubHouse, // 出版社
        pubYear: getDateToYear(literaryForm.value.pubYear), // 出版年
        fileName: "",
        url: webForm.value.url,
        articleName: webForm.value.articleName,
        networkName: webForm.value.networkName,
        pubDate: webForm.value.pubDate.toString(), // 出版日期
        refDesc: otherForm.value.refDesc,
        flag: curReferMode.value, // 资料类型 0: 著作|| 1: 网络|| 2: 其他
      }
      addRefApi(params).then((res) => {
        if (res.success) {
          curRefId.value = res.data.ref

          ElMessage.success("添加成功")
          emit("refresh")
          handleClose()
        } else {
          ElMessage.error(res.message)
        }
      })
    }
  } else if (curMode.value === 2) {
    uploaderRef.value.submitUpload().then((reskeys) => {
      if (reskeys.length > 1) return
      if (curReferMode.value === 0) {
        literaryForm.value.fileName = reskeys[0] ? reskeys[0] : ""
      } else {
        otherForm.value.fileName = reskeys[0] ? reskeys[0] : ""
      }
      const params: params2EditRef = {
        refId: curRefId.value,
        author: literaryForm.value.author,
        bookName: literaryForm.value.bookName,
        pubPlace: literaryForm.value.pubPlace, // 出版地
        pubHouse: literaryForm.value.pubHouse, // 出版社
        pubYear: getDateToYear(literaryForm.value.pubYear), // 出版年
        fileName:
          curReferMode.value === 0
            ? literaryForm.value.fileName
            : otherForm.value.fileName,
        url: webForm.value.url,
        articleName: webForm.value.articleName,
        networkName: webForm.value.networkName,
        pubDate: webForm.value.pubDate, // 出版日期
        refDesc: otherForm.value.refDesc,
        flag: curReferMode.value, // 资料类型 0: 著作|| 1: 网络|| 2: 其他
      }
      editRefApi(params).then((res) => {
        if (res.success) {
          curRefId.value = res.data.ref
          uploaderRef.value?.submitUpload()
          ElMessage.success("修改成功")
          emit("refresh")
          handleClose()
        } else {
          ElMessage.error(res.message)
        }
      })
    })
  }
}
// 批量发布文献
const handleAddMulRef = () => {
  emit("publish")
  handleClose()
}

// 拿到year
const getDateToYear = (date: string) => {
  const parts = date.toString().split(" ")
  return parseInt(parts[3])
}
// 处理关闭dialog
const handleClose = () => {
  dialogVisible.value = false
}
// 上传文件
const uploadFile = (item: any) => {
  let FormDatas = new FormData()
  FormDatas.append("file", item.file)
  FormDatas.append("refId", curRefId.value)
  uploadFileApi(FormDatas).then((res) => {
    if (res.success) {
      // ElMessage.success("成功")
      // uploadRef.value?.clearFiles()
    } else {
      ElMessage.success(res.message)
    }
  })
}
defineExpose({
  showDialog,
})
</script>
<template>
  <el-dialog v-model="dialogVisible" :title="formTitle" width="500">
    <span v-if="curMode === 1">
      <span>您确认要发布文献吗？</span>
    </span>
    <span class="form-container" v-else-if="curMode === 0 || curMode === 2">
      <span class="btn-group">
        <cmp-button
          :disabled="index !== curReferMode && curMode === 2"
          v-for="(item, index) in btnList"
          type="primary"
          class="btn-group-item"
          :class="index !== curReferMode ? 'not-active' : 'active'"
          @click="handleChangeHeaderForm(index)"
        >
          {{ item }}
        </cmp-button>
      </span>
      <!-- 著作 -->
      <el-form
        ref="literaryFormRef"
        :model="literaryForm"
        v-if="curReferMode === 0"
        label-position="right"
        label-width="auto"
      >
        <el-form-item
          label="作者"
          required
          prop="author"
          :rules="{
            required: true,
            message: '请输入作者',
            trigger: 'blur',
          }"
        >
          <el-input
            v-model="literaryForm.author"
            placeholder="请输入作者"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="著作名"
          required
          prop="bookName"
          :rules="{
            required: true,
            message: '请输入著作名',
            trigger: 'blur',
          }"
        >
          <el-input
            v-model="literaryForm.bookName"
            placeholder="请输入著作名"
          ></el-input>
        </el-form-item>
        <el-form-item label="出版地">
          <el-input
            v-model="literaryForm.pubPlace"
            placeholder="请输入出版地"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="出版社"
          required
          prop="pubHouse"
          :rules="{
            required: true,
            message: '请输入出版社',
            trigger: 'blur',
          }"
        >
          <el-input
            v-model="literaryForm.pubHouse"
            placeholder="请输入出版社"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="出版年份"
          required
          prop="pubYear"
          :rules="{
            required: true,
            message: '请选择年份',
            trigger: 'blur',
          }"
        >
          <el-date-picker
            style="width: 100%"
            v-model="literaryForm.pubYear"
            type="year"
            placeholder="请选择年份"
            popper-class="year-picker"
          />
        </el-form-item>
        <el-form-item label="原文附件">
          <uploader ref="uploaderRef"></uploader>
          <!-- <el-upload
            ref="uploadRef"
            class="upload"
            :limit="1"
            :auto-upload="false"
            :file-list="fileList"
            :http-request="uploadFile"
            accept=".pdf"
          >
            <template #trigger>
              <cmp-button type="primary" @click.prevent>选择附件</cmp-button>
            </template>
          </el-upload>
          <span>{{ literaryForm.fileName }}</span> -->
        </el-form-item>
      </el-form>
      <!-- 网络 -->
      <el-form
        ref="webFormRef"
        :model="webForm"
        v-if="curReferMode === 1"
        label-position="right"
        label-width="auto"
      >
        <el-form-item
          label="网址"
          required
          prop="url"
          :rules="{
            required: true,
            message: '请输入网址',
            trigger: 'blur',
          }"
        >
          <el-input v-model="webForm.url" placeholder="请输入网址"></el-input>
        </el-form-item>
        <el-form-item
          label="文章名字"
          required
          prop="articleName"
          :rules="{
            required: true,
            message: '请输入文章名字',
            trigger: 'blur',
          }"
        >
          <el-input
            v-model="webForm.articleName"
            placeholder="请输入文章名字"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="网络名称"
          required
          prop="networkName"
          :rules="{
            required: true,
            message: '请输入网络名称',
            trigger: 'blur',
          }"
        >
          <el-input
            v-model="webForm.networkName"
            placeholder="请输入出版社"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="发表日期"
          required
          prop="pubDate"
          :rules="{
            required: true,
            message: '请选择时间',
            trigger: 'blur',
          }"
        >
          <el-date-picker
            style="width: 100%"
            format="YYYY/MM/DD"
            value-format="YYYY-MM-DD"
            v-model="webForm.pubDate"
            placeholder="请选择时间"
            popper-class="year-picker"
          />
        </el-form-item>
      </el-form>
      <!-- other -->
      <el-form
        ref="otherFormRef"
        :model="otherForm"
        v-if="curReferMode === 2"
        label-position="top"
        label-width="auto"
      >
        <el-form-item
          prop="refDesc"
          label="参考资料说明"
          :rules="{
            required: true,
            message: '请输入参考资料说明',
            trigger: 'blur',
          }"
          style="display: initial"
          required
        >
          <span style="color: var(--color-grey); font-size: 12px"
            >填写格式请参考GB/T 7714-2015 《信息与文献 参考文献著录规则》</span
          >
          <el-input
            v-model="otherForm.refDesc"
            resize="none"
            type="textarea"
            :autosize="{ minRows: 4, maxRows: 23 }"
            placeholder="请输入参考资料说明"
          ></el-input>
        </el-form-item>
        <el-form-item style="margin-top: 17px">
          <uploader ref="uploaderRef"></uploader>
        </el-form-item>
      </el-form>
    </span>
    <template #footer>
      <div class="dialog-footer">
        <cmp-button type="info" @click="handleClose">关闭</cmp-button>
        <cmp-button
          type="primary"
          v-if="curMode === 1"
          @click="handleAddMulRef"
        >
          确定
        </cmp-button>
        <cmp-button type="primary" v-else @click="handleAddValidate">
          确定
        </cmp-button>
      </div>
    </template>
  </el-dialog>
</template>
<style scoped>
.btn-group {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  .btn-group-item {
    width: 100px;
  }
  .active {
    &:hover {
      color: white;
      background-color: var(--color-primary);
    }
  }
  .not-active {
    background-color: white;
    color: var(--color-primary);
    &:hover {
      background-color: var(--color-second);
    }
  }
}
.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 40px;
}
.upload {
  width: 100%;
}
</style>
<style>
.year-picker {
  --el-color-primary: var(--color-primary);
}
</style>
