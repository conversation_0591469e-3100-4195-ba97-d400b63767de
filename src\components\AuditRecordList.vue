<script setup lang="ts">
import { onMounted, ref, watch } from "vue"
import {
  getAuditRecordListApi,
  params2GetAuditRecordList,
} from "@/apis/path/audit"
import { auditTypeDict, MAX_PAGESIZE } from "@/utils/constant"
import { findKeyByValue } from "@/utils/func";

const props = defineProps({
  klgCode: String,
})
const emits = defineEmits(["select"])
const currentPage = ref(1)
const pageSize = MAX_PAGESIZE
const total = ref(0)
const curKlgCode = ref("")
const listData = ref<any[]>([]) // 表格数据

// 获取审核记录列表
const getAuditRecordList = (page?: number) => {
  const params: params2GetAuditRecordList = {
    current: page ? page : currentPage.value,
    limit: pageSize,
    klgCode: curKlgCode.value,
  }
  getAuditRecordListApi(params).then((res) => {
    if (res.success) {
      listData.value = res.data.list
      total.value = res.data.total
    }
  })
}
// 处理换页
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage
  getAuditRecordList(newPage)
}
// 处理选择
const handleSelect = (data: any) => {
  emits("select", data)
}
watch(
  () => props,
  () => {
    if (props.klgCode && props.klgCode !== "" && props.klgCode !== null) {
      curKlgCode.value = props.klgCode
      getAuditRecordList()
    }
  },
  { deep: true, immediate: true }
)
</script>
<template>
  <div class="wrapper">
    <div class="header">
      <span class="header-title">审核记录</span>
    </div>
    <div class="main-container" v-if="total === 0">暂无记录</div>
    <div class="main-container" v-else>
      <div class="list-total">
        <span>共{{ total }}个</span>
      </div>
      <div class="record-list">
        <div
          class="record-item"
          v-for="item in listData"
          :key="item.id"
          @click="handleSelect(item)"
        >
          <span style="width: 120px; font-size: 12px; white-space: nowrap">
            {{ item.createTime }}
          </span>
          <span style="width: 60px; white-space: nowrap">
            {{ item.processorName ? item.processorName : "-" }}
          </span>
          <span style="width: 60px; white-space: nowrap">
            {{ item.actionName }}
          </span>
          <span class="record-right">
            <span class="record-type">{{
              item.auditResult !== null ? findKeyByValue(item.auditResult, auditTypeDict) : "-"
            }}</span>
            <img src="@/assets/image/klg/u1705.svg" />
          </span>
        </div>
      </div>
      <div class="footer">
        <el-pagination
          :v-model:current-page="currentPage"
          :page-size="pageSize"
          :pager-count="7"
          layout="prev, pager, next"
          :total="total"
          @change="handleChangePage"
        />
      </div>
    </div>
  </div>
</template>
<style scoped>
.wrapper {
  width: 390px;
  background-color: white;
  padding: 10px;
  display: flex;
  flex-direction: column !important;
  font-size: 14px;
  .header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    .header-title {
      font-size: 14px;
      font-weight: 600;
    }
  }
  .main-container {
    display: flex;
    flex-direction: column;
    margin-top: 10px;
    .list-total {
      display: flex;
      justify-content: flex-end;
      font-size: 12px;
      font-weight: 600;
      margin-bottom: 5px;
    }
    .record-list {
      background-color: var(--color-light);
      padding: 10px 10px 0 10px;
      .record-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: white;
        padding: 5px 10px;
        margin-bottom: 10px;
        border: 1px solid var(--color-boxborder);
        border-radius: 3px;
        cursor: pointer;
        &:hover {
          background-color: var(--color-light);
        }
        .record-left {
        }
        .record-right {
          white-space: nowrap;
          display: flex;
          align-items: center;
          .record-type {
            margin-right: 5px;
            width: 25px;
          }
        }
      }
    }
    .footer {
      display: flex;
      justify-content: center;
    }
  }
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
</style>
