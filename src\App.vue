<script setup lang="ts">
import { RouterView, useRouter } from 'vue-router';
import { ref, onMounted, onUnmounted } from 'vue';
import ThumbNail from '@/components/ThumbNail.vue';
import { provide } from 'vue';
import PicIcon from '@/assets/image/common/pic_icon.jpg';
import TableIcon from '@/assets/image/common/table.png';
import CodeIcon from '@/assets/image/common/terminal.png';

import { userInfoStore } from './stores/userInfo';

const userinfo = userInfoStore();

// 窗口多开处理逻辑
document.addEventListener('visibilitychange', () => {
  if (!document['hidden']) {
    userinfo.getUserInfo();
    console.log('出现');
  } else {
    console.log('隐藏');
  }
});
const imgs: Array<HTMLElement> = [];
const thumbNailElement = ref<HTMLElement | null>(null);
const observer = new MutationObserver((mutationsList) => {
  let scripts = Array.prototype.slice.call(document.body.getElementsByTagName('script'));
  scripts.forEach(function (script) {
    if (!script.type || !script.type.match(/math\/tex/i)) {
      return -1;
    }
    const display = script.type.match(/mode\s*=\s*display(;|\s|\n|$)/) != null;

    const katexElement = document.createElement(display ? 'div' : 'span');
    katexElement.setAttribute('class', display ? 'equation' : 'inline-equation');
    katexElement.setAttribute('latexCode', script.text);
    try {
      katex!.render(script.text.replace(/\s+/g, ' '), katexElement, {
        displayMode: display
      });
    } catch (err) {
      katexElement.textContent = script.text;
    }
    script.parentNode.replaceChild(katexElement, script);
  });

  const images = document.querySelectorAll('img');

  images.forEach((img) => {
    if (imgs.includes(img)) {
      return;
    }
    img.addEventListener(
      'dblclick',
      function () {
        const range = document.createRange();
        range.selectNode(this);

        const selection = window.getSelection();
        selection!.removeAllRanges();
        selection!.addRange(range);
      },
      true
    );
    imgs.push(img);
    const questionLists = document.querySelectorAll('.questionList, .questionPop');
    questionLists.forEach((questionList) => {
      const figureElements = questionList.querySelectorAll('figure:not([thumbnail])');
      figureElements?.forEach((figure) => {
        const imgElements = figure.querySelectorAll('img:not([thumbnail])');
        imgElements?.forEach((item) => {
          const newImg = document.createElement('img');
          newImg.src = PicIcon;
          newImg.style.height = '14px';
          newImg.style.minWidth = '16px';
          // console.log("figure.outerHTML", figure)
          // @ts-ignore
          item.style.width = '155px';
          // @ts-ignore
          item.style.height = 'auto';
          newImg.setAttribute('thumbnail', item.outerHTML);
          newImg.addEventListener('mouseover', (event) => {
            thumbNailElement.value = event.target as HTMLElement;
          });
          figure.replaceWith(newImg);
        });
        const tableElements = figure.querySelectorAll('table:not([thumbnail])');
        // @ts-ignore
        tableElements?.forEach((item) => {
          const newImg = document.createElement('img');
          newImg.src = TableIcon;
          newImg.style.height = '14px';
          newImg.style.minWidth = '16px';
          newImg.setAttribute('thumbnail', figure.outerHTML);
          newImg.addEventListener('mouseover', (event) => {
            thumbNailElement.value = event.target as HTMLElement;
          });
          figure.replaceWith(newImg);
        });
      });
      const imgElements = questionList.querySelectorAll('img:not([thumbnail])');
      imgElements?.forEach((item) => {
        const newImg = document.createElement('img');
        newImg.src = PicIcon;
        newImg.style.height = '14px';
        newImg.style.minWidth = '16px';
        for (let i = item.attributes.length - 1; i >= 0; i--) {
          const attribute = item.attributes[i];
          if (attribute.name !== 'src') {
            item.removeAttribute(attribute.name);
          }
        }
        newImg.setAttribute('thumbnail', item.outerHTML);
        // console.log("item.outer", item.outerHTM L)
        newImg.addEventListener('mouseover', (event) => {
          thumbNailElement.value = event.target as HTMLElement;
        });
        item.replaceWith(newImg);
      });
      const codeElements = questionList.querySelectorAll('pre:not([thumbnail])');
      codeElements?.forEach((item) => {
        const newImg = document.createElement('img');
        newImg.src = CodeIcon;
        newImg.style.minWidth = '16px';
        newImg.style.height = '14px';
        newImg.setAttribute('thumbnail', item.outerHTML);
        newImg.addEventListener('mouseover', (event) => {
          thumbNailElement.value = event.target as HTMLElement;
        });
        item.replaceWith(newImg);
      });
    });
  });
});
const scale = window.devicePixelRatio || 1;
if (scale >= 1.25) {
  document.body.classList.add('zoomed-in');
} else {
  document.body.classList.remove('zoomed-in');
}

const mediaQuery = window.matchMedia('(min-width: 1000px)');
mediaQuery.addEventListener('change', (e) => {
  if (e.matches) {
    console.log('屏幕宽度大于或等于 1000px');
  } else {
    console.log('屏幕宽度小于 1000px');
  }
});

onMounted(() => {
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
});

onUnmounted(() => {
  observer.disconnect();
  window.handleWord = null;
});
</script>
<template>
  <router-view></router-view>
  <ThumbNail v-model="thumbNailElement"></ThumbNail>
</template>
<style scoped>

</style>
