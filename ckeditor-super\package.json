{"name": "ckeditor5-custom-build", "author": "CKSource", "description": "A custom CKEditor 5 build made by the CKEditor 5 online builder.", "version": "0.0.1", "license": "SEE LICENSE IN LICENSE.md", "private": true, "main": "./build/ckeditor.js", "types": "./build/ckeditor.d.ts", "dependencies": {"@ckeditor/ckeditor5-autoformat": "41.4.2", "@ckeditor/ckeditor5-basic-styles": "41.4.2", "@ckeditor/ckeditor5-block-quote": "41.4.2", "@ckeditor/ckeditor5-cloud-services": "41.4.2", "@ckeditor/ckeditor5-code-block": "^41.4.2", "@ckeditor/ckeditor5-editor-classic": "41.4.2", "@ckeditor/ckeditor5-editor-inline": "41.4.2", "@ckeditor/ckeditor5-essentials": "41.4.2", "@ckeditor/ckeditor5-heading": "41.4.2", "@ckeditor/ckeditor5-image": "41.4.2", "@ckeditor/ckeditor5-indent": "41.4.2", "@ckeditor/ckeditor5-link": "41.4.2", "@ckeditor/ckeditor5-list": "41.4.2", "@ckeditor/ckeditor5-media-embed": "41.4.2", "@ckeditor/ckeditor5-paragraph": "41.4.2", "@ckeditor/ckeditor5-paste-from-office": "41.4.2", "@ckeditor/ckeditor5-source-editing": "41.4.2", "@ckeditor/ckeditor5-table": "41.4.2", "@ckeditor/ckeditor5-typing": "41.4.2", "@ckeditor/ckeditor5-undo": "41.4.2"}, "devDependencies": {"@ckeditor/ckeditor5-core": "41.4.2", "@ckeditor/ckeditor5-dev-translations": "^32.1.2", "@ckeditor/ckeditor5-dev-utils": "^32.1.2", "@ckeditor/ckeditor5-theme-lark": "41.4.2", "@isaul32/ckeditor5-math": "^41.4.2", "css-loader": "^5.2.7", "postcss": "^8.4.36", "postcss-loader": "^4.3.0", "raw-loader": "^4.0.2", "style-loader": "^2.0.0", "terser-webpack-plugin": "^4.2.3", "ts-loader": "^9.5.1", "typescript": "5.0.4", "webpack": "^5.90.3", "webpack-cli": "^4.10.0"}, "scripts": {"build": "webpack --mode production", "postbuild": "tsc --declaration --declarationDir build --stripInternal --emitDeclarationOnly"}}