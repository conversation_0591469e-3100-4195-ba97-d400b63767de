<script setup lang="ts">
import FormSwitch from '@/components/FormSwitch.vue';
import MyFlipper from '@/components/MyFlipper.vue';
import CmpButton from '@/components/CmpButton.vue';
import MulOpDialog from '@/view/klg/components/MulOpDialog.vue';

import { KlgType, KlgTypeDict, KlgTypeDictPlus, MAX_PAGESIZE } from '@/utils/constant';
import { findKeyByValue } from '@/utils/func';
import { getRecycleKlgListApi, rebackKlgApi, delKlgApi } from '@/apis/path/klg';
import type { params2GetRecycleKlgList } from '@/apis/path/klg';
import { onMounted, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

const currentPage = ref(1); // 当前页
const pageSize = MAX_PAGESIZE; // 页大小
const total = ref(0); // 总数

const dialogRef = ref(); // dialog
const tableData = ref([]); // 表格数据
const selectionKlgList = ref<string[]>([]); // 选择任务列表

// 获取文献列表
const getRefList = (current: number, type?: string, keyword?: string) => {
  const params: params2GetRecycleKlgList = {
    current: current ? current : currentPage.value,
    limit: pageSize,
    type: type ? type : '',
    keyword: keyword ? keyword : ''
  };
  getRecycleKlgListApi(params).then((res) => {
    if (res.success) {
      tableData.value = res.data.list;
      total.value = res.data.total;
    }
  });
};
// 处理文献列表更新
const handleRefreshList = (form?: any) => {
  if (form) {
    getRefList(
      currentPage.value,
      form.klgType === KlgType.All ? '' : findKeyByValue(form.klgType, KlgTypeDictPlus),
      form.keyword
    );
  } else {
    getRefList(currentPage.value);
  }
};

// 计算列表index
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize + index + 1;
};
// 处理选择Klg
const handleSelectionChange = (list: []) => {
  selectionKlgList.value = [];
  list.forEach((item) => {
    selectionKlgList.value.push(item.klgCode);
  });
};
// 处理换页
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  getRefList(newPage);
};
// 处理恢复知识
const handleRebackKlg = (list: string[]) => {
  rebackKlgApi(list).then((res) => {
    if (res.success) {
      ElMessage.success('恢复成功');
      handleRefreshList();
    }
  });
};
// 处理销毁知识
const handleDeleteKlg = (list: string[]) => {
  delKlgApi(list).then((res) => {
    if (res.success) {
      ElMessage.success('销毁成功');
      handleRefreshList();
    }
  });
};
// 处理dialog
const handleDialog = (mode: number) => {
  if (selectionKlgList.value.length === 0) return;
  dialogRef.value.showDialog(mode);
};
// 处理批量
const handleMulOp = (mode: number) => {
  if (mode === 0) {
    rebackKlgApi(selectionKlgList.value).then((res) => {
      if (res.success) {
        ElMessage.success('恢复成功');
        handleRefreshList();
      }
    });
  } else if (mode === 1) {
    delKlgApi(selectionKlgList.value).then((res) => {
      if (res.success) {
        ElMessage.success('销毁成功');
        handleRefreshList();
      }
    });
  }
  // 为了和别的统一就不用ElmessageBox了
  //   const titleTextList = ["恢复知识", "销毁知识"]
  //   const contentTextList = [
  //     "您恢复的项目会出现在“知识维护”中并可以继续编辑，您确定要恢复吗？",
  //     "您销毁的知识不能再恢复了，您确定要销毁吗？",
  //   ]
  //   ElMessageBox.confirm(contentTextList[mode], titleTextList[mode], {
  //     confirmButtonText: "确定",
  //     cancelButtonText: "关闭",
  //     customClass: 'message-box-block'
  //   }).then(() => {
  //   })
};
onMounted(() => {
  handleRefreshList();
});
</script>
<template>
  <div class="wrapper">
    <div class="header-wrapper">
      <form-switch
        :need-klg-type="true"
        :need-search-klg="true"
        @recycle="handleRefreshList"
      ></form-switch>
      <div class="line"></div>
      <div class="toolbar">
        <CmpButton type="primary" @click="handleDialog(0)">批量恢复</CmpButton>
        <CmpButton type="primary" @click="handleDialog(1)" style="margin-left: 20px"
          >批量销毁</CmpButton
        >
      </div>
    </div>
    <div class="main-wrapper">
      <div class="line"></div>
      <el-table
        class="table"
        :data="tableData"
        style="width: 100%"
        empty-text="暂无数据"
        :row-style="{ overflow: 'hidden' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="30" />
        <el-table-column type="index" :index="indexMethod" label="序号" width="60" align="center" />
        <el-table-column label="知识名称" width="490" height="55">
          <template #default="scope">
            <el-tooltip
              placement="top"
              :content="(scope.row.title)"
              :raw-content="true"
              :show-after="200"
              effect="customized"
            >
              <div
                class="ck-content ellipsis-text-inline"
                v-html="(scope.row.title)"
                style="max-width: 100%;"
              ></div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="知识类型" width="150" align="center">
          <template #default="scoped">
            {{ findKeyByValue(scoped.row.type, KlgTypeDict) }}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="whoName" label="作者" width="140" align="center" /> -->
        <el-table-column prop="dieTime" label="销毁时间" width="200" align="center" />
        <el-table-column label="操作" width="250" align="center">
          <template #default="scope">
            <span class="operation">
              <el-button
                type="primary"
                class="operationBtn"
                @click="handleRebackKlg([scope.row.klgCode])"
                text
              >
                恢复
              </el-button>
              <el-button
                type="primary"
                class="operationBtn"
                @click="handleDeleteKlg([scope.row.klgCode])"
                text
              >
                销毁
              </el-button>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <my-flipper
        @change-page="handleChangePage"
        :current="currentPage"
        :page-size="pageSize"
        :total="total"
      ></my-flipper>
    </div>
  </div>
  <!-- other -->
  <MulOpDialog ref="dialogRef" @op="handleMulOp"></MulOpDialog>
</template>
<style scoped>
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
.wrapper {
  width: 1200px;
  min-height: 750px;
  background-color: white;
  font-family: var(--text-family);

  .header-wrapper {
    width: 100%;
    .toolbar {
      margin: 7px 0;
      padding: 0 20px;
    }
  }
  .main-wrapper {
    padding: 0 10px;

    .table {
      --el-color-primary: var(--color-primary);
      ::v-deep(p) {
        margin: 0;
      }
      .operation {
        padding: 0 10px;
        display: flex;
        justify-content: center;
        .operationBtn {
          font-family: var(--text-family) !important;
          font-weight: 400;
          &:hover {
            font-weight: bold;
          }
        }
      }
    }
  }
}
:deep(.el-button--primary.is-text.is-disabled) {
  color: var(--color-boxborder);
}
</style>
