/**
 * @license Copyright (c) 2014-2024, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
 */

//import { ClassicEditor } from '@ckeditor/ckeditor5-editor-classic';
import { ClassicEditor as ClassicEditorBase } from "@ckeditor/ckeditor5-editor-classic"
import { InlineEditor as InlineEditorBase } from "@ckeditor/ckeditor5-editor-inline"

import { Autoformat } from "@ckeditor/ckeditor5-autoformat"
import { Bold, Italic } from "@ckeditor/ckeditor5-basic-styles"
import { BlockQuote } from "@ckeditor/ckeditor5-block-quote"
import { CloudServices } from "@ckeditor/ckeditor5-cloud-services"
import type { EditorConfig } from "@ckeditor/ckeditor5-core"
import { Essentials } from "@ckeditor/ckeditor5-essentials"
import { Heading } from "@ckeditor/ckeditor5-heading"
import {
  Image,
  ImageCaption,
  ImageResize,
  ImageStyle,
  ImageToolbar,
  ImageUpload,
} from "@ckeditor/ckeditor5-image"
import { Indent } from "@ckeditor/ckeditor5-indent"
import { Link } from "@ckeditor/ckeditor5-link"
import { List } from "@ckeditor/ckeditor5-list"
import { MediaEmbed } from "@ckeditor/ckeditor5-media-embed"
import { Paragraph } from "@ckeditor/ckeditor5-paragraph"
import { PasteFromOffice } from "@ckeditor/ckeditor5-paste-from-office"
import { Table, TableToolbar } from "@ckeditor/ckeditor5-table"
import { TextTransformation } from "@ckeditor/ckeditor5-typing"
import { Undo } from "@ckeditor/ckeditor5-undo"
import Mathematics from "@isaul32/ckeditor5-math/src/math"
import AutoformatMath from "@isaul32/ckeditor5-math/src/autoformatmath"
import SourceEditing from '@ckeditor/ckeditor5-source-editing/src/sourceediting';
import { CodeBlock } from "@ckeditor/ckeditor5-code-block"
// You can read more about extending the build with additional plugins in the "Installing plugins" guide.
// See https://ckeditor.com/docs/ckeditor5/latest/installation/plugins/installing-plugins.html for details.

//class Editor extends ClassicEditor {
class ClassicEditorCustom extends ClassicEditorBase {
  public static override builtinPlugins = [
    Autoformat,
    BlockQuote,
    Bold,
    CloudServices,
    Essentials,
    Heading,
    Image,
    ImageCaption,
    ImageResize,
    ImageStyle,
    ImageToolbar,
    ImageUpload,
    Indent,
    Italic,
    // Link,
    List,
    // MediaEmbed,
    Paragraph,
    PasteFromOffice,
    Table,
    TableToolbar,
    TextTransformation,
    Undo,
    Mathematics,
    AutoformatMath,
    CodeBlock,
    SourceEditing,
  ]

  public static override defaultConfig = {
    toolbar: {
      items: [
        "heading",
        "|",
        "undo",
        "redo",
        "|",
        "bold",
        "italic",
        // 'link',
        "bulletedList",
        "numberedList",
        "|",
        "outdent",
        "indent",
        "|",
        "imageUpload",
        "blockQuote",
        "insertTable",
        // 'mediaEmbed',
        "math",
        "codeblock",
        "sourceEditing",
      ],
    },
    language: "zh-cn",
    image: {
      toolbar: [
        "imageTextAlternative",
        "toggleImageCaption",
        "imageStyle:inline",
        "imageStyle:block",
        "imageStyle:side",
      ],
    },
    table: {
      contentToolbar: ["tableColumn", "tableRow", "mergeTableCells"],
    },
    math: {
      engine: "katex", // or katex or function. E.g. (equation, element, display) => { ... }
      lazyLoad: undefined, // async () => { ... }, called once before rendering first equation if engine doesn't exist. After resolving promise, plugin renders equations.
      outputType: "script", // or span
      forceOutputType: true, // forces output to use outputType
      enablePreview: true, // Enable preview view
      previewClassName: [], // Class names to add to previews
      popupClassName: [], // Class names to add to math popup balloon
    },
  }
}

class InlineEditorCustom extends InlineEditorBase {
  public static override builtinPlugins = [
    Autoformat,
    BlockQuote,
    Bold,
    CloudServices,
    Essentials,
    Heading,
    Image,
    ImageCaption,
    ImageResize,
    ImageStyle,
    ImageToolbar,
    ImageUpload,
    Indent,
    Italic,
    // Link,
    List,
    // MediaEmbed,
    Paragraph,
    PasteFromOffice,
    Table,
    TableToolbar,
    TextTransformation,
    Undo,
    Mathematics,
    AutoformatMath,
    CodeBlock,
  ]

  public static override defaultConfig = {
    toolbar: {
      items: [
        "heading",
        "|",
        "undo",
        "redo",
        "|",
        "bold",
        "italic",
        // 'link',
        "bulletedList",
        "numberedList",
        "|",
        "outdent",
        "indent",
        "|",
        "imageUpload",
        "blockQuote",
        "insertTable",
        // 'mediaEmbed',
        "math",
        "codeblock",
      ],
    },
    language: "zh-cn",
    image: {
      toolbar: [
        "imageTextAlternative",
        "toggleImageCaption",
        "imageStyle:inline",
        "imageStyle:block",
        "imageStyle:side",
      ],
    },
    table: {
      contentToolbar: ["tableColumn", "tableRow", "mergeTableCells"],
    },
    math: {
      engine: "katex", // or katex or function. E.g. (equation, element, display) => { ... }
      lazyLoad: undefined, // async () => { ... }, called once before rendering first equation if engine doesn't exist. After resolving promise, plugin renders equations.
      outputType: "script", // or span
      forceOutputType: true, // forces output to use outputType
      enablePreview: true, // Enable preview view
      previewClassName: [], // Class names to add to previews
      popupClassName: [], // Class names to add to math popup balloon
    },
  }
}

class InlineEditorOnlyMath extends InlineEditorBase {
  public static override builtinPlugins = [
    Autoformat,
    Bold,
    Essentials,
    Heading,
    Italic,
    // Link,
    // MediaEmbed,
    TextTransformation,
    Undo,
    Mathematics,
    AutoformatMath,
  ]

  public static override defaultConfig = {
    toolbar: {
      items: ["undo", "redo", "|", "math"],
    },
    language: "zh-cn",
    math: {
      engine: "katex", // or katex or function. E.g. (equation, element, display) => { ... }
      lazyLoad: undefined, // async () => { ... }, called once before rendering first equation if engine doesn't exist. After resolving promise, plugin renders equations.
      outputType: "script", // or span
      forceOutputType: true, // forces output to use outputType
      enablePreview: true, // Enable preview view
      previewClassName: [], // Class names to add to previews
      popupClassName: [], // Class names to add to math popup balloon
    },
  }
}

//export default Editor;
export default {
  ClassicEditorCustom,
  InlineEditorCustom,
  InlineEditorOnlyMath,
}
