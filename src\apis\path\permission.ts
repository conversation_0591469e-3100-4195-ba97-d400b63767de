import { http } from "@/apis"
import type { APIResponse } from "@/utils/type"

export interface params2GetRoleList {
  current: number
  limit: number
}
export interface params2AddRole {
  title: string,
}

export interface params2EditRole extends params2AddRole{
  id: number,
  rightSet?: string,
}

export interface params2GetUserList extends params2GetRoleList {
  keyword: string,
}

export interface params2EditAdmin {
  adminname: string,
  approvalAuthority: string,
  expireTime: string,
  gid: number,
  gname: string,
  id: number,
  name: string,
  pwd: string,
}
export interface params2SaveTree {
  areaDataS: string,
  areaEditS: string,
  id: number,
}

// 获取角色列表
export function getRoleListApi(params: params2GetRoleList): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/gteam/getAllList`,
    data: params,
  })
}

// 添加角色
export function addRoleApi(params: params2AddRole): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/gteam/save`,
    data: params,
  })
}

// 修改角色
export function editRoleApi(params: params2EditRole): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/gteam/edit`,
    data: params,
  })
}

// 修改角色
export function deleteRoleApi(id: number): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/gteam/remove/${id}`,
  })
}

// 获取角色权限
export function getRolePermissionApi(id: number): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/gteam/getDetail/${id}`,
  })
}

// 获取角色列表
export function getUserListApi(params: params2GetUserList): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/admin/getAll`,
    data: params
  })
}

// 获取用户列表
export function editUserAdminApi(params: params2EditAdmin): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/admin/editAdmin`,
    data: params
  })
}

// 获取用户选择的角色列表（无分页）
export function getUserSelectRoleApi(): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/gteam/getAll`,
  })
}

// 获取权限列表（无分页）
export function getAuditListApi(): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/gteam/getPart`,
  })
}


// 拿到树
export function getTreeApi(id: number): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/area/role/getAreaRole/${id}`,
  })
}

// 提交修改树
export function saveTreeApi(params: params2SaveTree): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/area/role/save`,
    data: params
  })
}



