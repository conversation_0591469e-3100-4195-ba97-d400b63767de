import { http_auth, http_info } from "@/apis";
import type { APIResponse } from "@/utils/type";

// 获取用户信息
export function getUserInfoApi(): Promise<APIResponse>{
    return http_info.request({
        method: 'get',
        url: '/user/info/query',
    });
}
// 登出
export function logoutApi(): Promise<APIResponse> {
    return http_auth.request({
        method: 'get',
        url: '/auth/logout'
    })
}

