<script setup lang="ts">
import CmpButton from "@/components/CmpButton.vue"
import ModeTypeSwitcher from "@/components/ModeTypeSwitcher.vue"
import ShowTreeDialog from "@/view/other/editDomain/ShowTreeDialog.vue"
import {
  getTreeApi,
  saveTreeApi,
  type params2SaveTree,
} from "@/apis/path/permission"
import { transformToTreeNode } from "@/utils/func"
import { onMounted, ref } from "vue"
import { ElMessage } from "element-plus"
import { Role, Tree } from "@/utils/type"
import { useRoute } from "vue-router"
import router from "@/router"
import { TreeNodeData } from "element-plus/es/components/tree-v2/src/types"
const route = useRoute()
const treeData = ref<Tree[]>([]) // 树
const curHeaderMode = ref(0) // 0: 知识查看 || 1: 领域编辑
const dialogRef = ref() // showTreeDialog
const curRole = ref<Role>({
  id: -1,
  title: "",
  rightSet: "",
})

// 处理转换header
const handleChangeHeader = (newHeaderMode: number) => {
  curHeaderMode.value = newHeaderMode
}

// 获取树
const getTree = () => {
  if (route.query.id && route.query.title) {
    curRole.value.id = parseInt(route.query.id.toString())
    curRole.value.title = route.query.title.toString()
    getTreeApi(curRole.value.id).then((res) => {
      if (res.success) {
        // 这里前后端都做了树的过滤，如果渲染慢可以试着注释掉前端的
        // treeData.value = [filterTree(transformToTreeNode(res.data.tree[0]))]
        treeData.value = [transformToTreeNode(res.data.tree[0])]
      }
    })
  }
}
// 过滤树
const filterTree = (node: Tree): Tree => {
  const recurse = (node: Tree): Tree => {
    if (node.children) {
      const filteredNodes: Tree[] = []
      node.children.forEach((item) => {
        if (item.children) {
          const children = recurse(item)
          if (children.show) {
            filteredNodes.push(children)
          }
        } else {
          if (item.isTag === 1) {
            item.show = true
          } else {
            item.show = false
          }
          return item
        }
      })
      // 总结
      let showFlag = false
      filteredNodes.forEach((node) => {
        if (node.show) {
          showFlag = true
        }
      })
      node.show = showFlag
      if (node.isTag === 1) node.show = true
      if (node.show) node.children = filteredNodes
    }
    return node
  }
  return recurse(node)
}

// 获取修改klg和domain权限字符串
const getSubmitStr = () => {
  const klgCheckList: string[] = []
  const domainCheckList: string[] = []
  const collectAreaCodes = (nodes: Tree[]) => {
    nodes.forEach((node) => {
      if (node.isEditKlg) {
        klgCheckList.push(node.areaCode)
      }
      if (node.isEditArea) {
        domainCheckList.push(node.areaCode)
      }
      if (node.children) {
        collectAreaCodes(node.children)
      }
    })
  }
  collectAreaCodes(treeData.value)
  return [klgCheckList.join("@@"), domainCheckList.join("@@")]
}

// 处理打开dialog
const handleShowTreeDialog = () => {
  dialogRef.value.showDialog(curRole.value, treeData.value)
}
// 处理checkbox
const handleCheckbox = (data: TreeNodeData, checked: boolean) => {
  if (checked) {
    if (curHeaderMode.value === 0) {
      data.isEditKlg = checked
    } else if (curHeaderMode.value === 1 && data.isEditKlg) {
      data.isEditArea = checked
    }
    data.children.forEach((item) => {
      handleCheckbox(item, checked)
    })
  }
}
// 处理树的checkbox选择
const handleCheckChildren = (data: any, mode: number) => {
  if (data.isEditKlg && mode === 1) {
    data.isEditArea = false
    data.children.forEach((item) => {
      item.isEditKlg = false
      handleCheckChildren(item, mode)
    })
  }
  if (data.isEditArea && mode === 2) {
    data.children.forEach((item) => {
      item.isEditArea = false
      handleCheckChildren(item, mode)
    })
  }
}

// 跳转
const routerPush = () => {
  router.push("/permission/character")
}
// 提交
const submitPermission = () => {
  const params: params2SaveTree = {
    areaDataS: getSubmitStr()[0],
    areaEditS: getSubmitStr()[1],
    id: curRole.value.id,
  }
  saveTreeApi(params).then((res) => {
    if (res.success) {
      ElMessage.success("修改成功")
    } else {
      ElMessage.error(res.message)
    }
  })
}
onMounted(() => {
  getTree()
})
</script>
<template>
  <div class="wrapper">
    <div class="header-wrapper">
      <div class="line"></div>
      <div class="toolbar">
        <CmpButton type="info" @click="routerPush" class="btn">返回</CmpButton>
        <CmpButton type="primary" @click="handleShowTreeDialog" class="btn"
          >预览</CmpButton
        >
        <CmpButton type="primary" class="btn" @click="submitPermission"
          >提交</CmpButton
        >
      </div>
    </div>
    <div class="main-wrapper">
      <div class="line"></div>
      <div class="main-content">
        <el-form>
          <el-form-item label="角色名称">
            <div class="role-title">{{ curRole.title }}</div>
          </el-form-item>
          <el-form-item label="领域权限">
            <div class="domain-content">
              <div class="header-content">
                <mode-type-switcher
                  :mode="curHeaderMode"
                  @changeMode="handleChangeHeader"
                  :length="2"
                >
                  <template v-slot:mode0> 知识查看 </template>
                  <template v-slot:mode1> 领域编辑 </template>
                </mode-type-switcher>
              </div>
              <div class="tree-content">
                <el-tree
                  class="tree-body"
                  :data="treeData"
                  node-key="areaCode"
                  show-checkbox
                  :expand-on-click-node="false"
                  @check-change="handleCheckbox"
                  default-expand-all
                >
                  <template #default="{ node, data }">
                    <span class="tree-node">
                      <span class="tree-node-title tree-node-box">
                        <span>{{ node.label }}</span>
                      </span>
                      <span
                        class="tree-node-box"
                        v-if="curHeaderMode === 0 && data.isTag === 1"
                      >
                        <el-checkbox
                          v-model="data.isEditKlg"
                          @click="handleCheckChildren(data, 1)"
                        ></el-checkbox>
                        <span style="margin-left: 10px">知识查看</span>
                      </span>
                      <span
                        class="tree-node-box"
                        v-if="curHeaderMode === 1 && data.isTag === 1"
                      >
                        <el-checkbox
                          v-model="data.isEditArea"
                          :disabled="!data.isEditKlg"
                          @click="handleCheckChildren(data, 2)"
                        ></el-checkbox>
                        <span style="margin-left: 10px">领域编辑</span>
                      </span>
                    </span>
                  </template>
                </el-tree>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
  <!-- other -->
  <show-tree-dialog ref="dialogRef"></show-tree-dialog>
</template>
<style scoped>
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
.wrapper {
  width: 1200px;
  min-height: 750px;
  background-color: white;
  font-family: var(--text-family);

  .header-wrapper {
    width: 100%;
    .toolbar {
      gap: 10px;
      margin: 7px 0;
      padding: 0 20px;
    }
  }
  .main-wrapper {
    width: 100%;

    .main-content {
      overflow: hidden;
      padding: 20px;

      &::-webkit-scrollbar {
        width: 3px;
      }
      &::-webkit-scrollbar-thumb {
        background-color: #888;
        border-radius: 6px;
        height: 50px;
      }
      &::-webkit-scrollbar-track {
        background-color: #f1f1f1;
      }
      .role-title {
        width: 100%;
        padding: 4px 20px;
        margin-left: 20px;
        background-color: var(--color-light);
        border: 1px solid var(--color-boxborder);
      }
      .domain-content {
        width: 100%;
        height: 100%;
        .header-content {
          width: 100%;
          display: flex;
          justify-content: center;
        }
        .tree-content {
          margin-left: 20px;
          margin-top: 10px;
          width: 98%;
          /* height: 100%; */
          height: 500px;
          background-color: var(--color-light);
          overflow: auto;
          border: none;
          &::-webkit-scrollbar {
            width: 5px;
            height: 5px;
          }
          &::-webkit-scrollbar-thumb {
            background-color: #888;
            border-radius: 6px;
            height: 5px;
            width: 5px;
          }
          &::-webkit-scrollbar-track {
            background-color: #f1f1f1;
          }
          .tree-body {
            padding: 10px 0;
            height: 250px;
            border: none;
            /* display: inline-block; */
            background-color: var(--color-light);

            .tree-node {
              display: flex;
              height: 100%;
              align-items: center;
              .tree-node-title {
                background-color: white;
                padding: 0 10px;
                height: 85%;
              }
              .tree-node-box {
                margin-left: 10px;
                display: flex;
                align-items: center;
                justify-content: flex-start;
              }
            }
          }
        }
      }
    }
  }
}
.btn {
  margin-right: 20px;
  margin-top: 10px;
  margin-bottom: 10px;
  border-radius: 2px;
}
:deep(.el-vl__window) {
  height: 1000px;
}
</style>
@/utils/defaultList
