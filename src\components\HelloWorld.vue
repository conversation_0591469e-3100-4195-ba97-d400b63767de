<script setup lang="ts">
import { ref } from "vue"
const msg = "hello"
</script>
<template>
  <div class="wrapper">
    <div class="hello-container">
      <div class="hello-text">Hello & Welcome!</div>
      <div class="tip"><span class="tip-text">欢迎使用无尽本源知识中心！</span></div>
    </div>
  </div>
</template>
<style scoped>
.wrapper {
  width: 1200px;
  min-height: 750px;
  background-color: white;
  font-family: var(--text-family);
  display: flex;
  align-items: center;
  justify-content: center;
  .hello-container {
    color: white;
    background-color: var(--color-primary);
    width: 790px;
    height: 210px;
    display: flex;
    margin-bottom: 180px;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    border-radius: 7px;
    .hello-text {
      font-family: Arial, Helvetica, sans-serif;
      font-size: 48px;
      font-weight: 700;
    }
    .tip {
      width: 100%;
      margin-top: 55px;
      font-size: 14px;
      display: flex;
      justify-content: flex-end;
      .tip-text {
        margin-right: 50px;
      }
    }
  }
}
</style>
