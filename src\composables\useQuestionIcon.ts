import { ref, nextTick, type Ref } from 'vue';

/**
 * 问题图标管理选项
 */
interface QuestionIconOptions {
  /** 图标点击回调函数 */
  onIconClick?: (selectedText: string) => void;
  /** 图标位置偏移 */
  iconOffset?: { x: number; y: number };
  /** 保护时间（毫秒），防止立即隐藏 */
  protectionTime?: number;
}

/**
 * 问题图标管理返回值
 */
interface QuestionIconReturn {
  /** 问题图标是否可见 */
  questionIconVisible: Ref<boolean>;
  /** 问题图标位置 */
  questionIconPosition: Ref<{ x: number; y: number }>;
  /** 当前选中的文本 */
  currentSelectedText: Ref<string>;
  /** 显示问题图标 */
  showQuestionIcon: (data: any) => void;
  /** 处理问题图标点击事件 */
  handleQuestionIconClick: (event: MouseEvent) => void;
  /** 处理文档点击事件 */
  handleDocumentClick: (event: MouseEvent) => void;
  /** 问题图标元素引用 */
  questionIconElement: Ref<HTMLElement | null>;
}

/**
 * 问题图标管理 Composable
 * 用于管理文本选择后显示的问题图标功能
 */
export function useQuestionIcon(options: QuestionIconOptions = {}): QuestionIconReturn {
  const { onIconClick, iconOffset = { x: -15, y: 10 }, protectionTime = 100 } = options;

  // 响应式状态
  const questionIconVisible = ref(false);
  const questionIconPosition = ref({ x: 0, y: 0 });
  const currentSelectedText = ref('');
  const questionIconElement = ref<HTMLElement | null>(null);
  const isShowingQuestionIcon = ref(false);
  const currentSelection = ref<any>(null);

  /**
   * 显示问题图标

   */
  const showQuestionIcon = (data: any) => {
    if (!data || !data.content) {
      console.log('❌ 选中文本为空或无效');
      return;
    }
    console.log('显示问题图标:', data);
    // 保存选中的文本和选择对象
    currentSelectedText.value = data.content;

    // 优先使用传递的selection对象，否则使用window.getSelection()
    currentSelection.value = data.selection || window.getSelection();

    if (!currentSelection.value || currentSelection.value.rangeCount === 0) {
      console.log('❌ 无法获取选择范围');
      return;
    }

    try {
      // 获取选中文本的位置
      const range = currentSelection.value.getRangeAt(0);
      const rect = range.getBoundingClientRect();

      // 计算问号图标的位置（选中文本上方居中）
      questionIconPosition.value = {
        x: rect.left + rect.width / 2 + window.scrollX + iconOffset.x,
        y: rect.top - 40 + window.scrollY + iconOffset.y
      };

      // 设置保护标记，防止立即被点击事件隐藏
      isShowingQuestionIcon.value = true;

      // 显示问号图标
      questionIconVisible.value = true;

      // 使用 nextTick 确保状态更新
      nextTick(() => {
        // 延迟一段时间后取消保护，允许点击隐藏
        setTimeout(() => {
          isShowingQuestionIcon.value = false;
        }, protectionTime);

        // 添加全局点击事件监听，点击其他地方隐藏问号图标
        document.addEventListener('click', handleDocumentClick as EventListener, true);
      });
    } catch (error) {
      console.error('显示问号图标时出错:', error);
    }
  };

  /**
   * 处理问题图标点击事件
   * @param event 鼠标事件
   */
  const handleQuestionIconClick = (event: MouseEvent) => {
    event.stopPropagation();

    // 隐藏问号图标
    questionIconVisible.value = false;

    // 移除全局点击事件监听
    document.removeEventListener('click', handleDocumentClick as EventListener, true);

    // 调用回调函数或触发自定义事件
    if (onIconClick) {
      onIconClick(currentSelectedText.value);
    } else {
      // 默认行为：通过自定义事件通知显示QuestionDrawer
      const customEvent = new CustomEvent('showQuestionDrawer', {
        detail: { selectedText: currentSelectedText.value }
      });
      window.dispatchEvent(customEvent);
    }
  };

  /**
   * 处理文档点击事件，点击其他地方隐藏问号图标
   * @param event 鼠标事件
   */
  const handleDocumentClick = (event: MouseEvent) => {
    const target = event.target as HTMLElement;

    // 如果在保护期内，不处理点击隐藏
    if (isShowingQuestionIcon.value) {
      return;
    }

    // 如果点击的不是问号图标，则隐藏问号图标
    if (questionIconVisible.value && !questionIconElement.value?.contains(target)) {
      questionIconVisible.value = false;
      currentSelectedText.value = '';
      document.removeEventListener('click', handleDocumentClick as EventListener, true);
    }
  };

  return {
    questionIconVisible,
    questionIconPosition,
    currentSelectedText,
    showQuestionIcon,
    handleQuestionIconClick,
    handleDocumentClick,
    questionIconElement
  };
}
