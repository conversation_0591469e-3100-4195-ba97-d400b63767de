export interface APIResponse {
  success: boolean
  data: any
  message: string
}

export interface Role {
  id: number
  title: string
  count?: number
  rightSet?: string
}
export interface RoleBoxItem {
  name: string
  title: string
  check: boolean
}
export interface RolePermission {
  name: string
  title: string
  checkAll: boolean
  isIndeterminate: boolean
  checkedGroup: RoleBoxItem[]
  group: RoleBoxItem[]
}
export interface Tree {
  areaCode: string
  label: string
  angleId?: number
  activate?: number
  checked?: boolean
  show?: boolean
  areaId?: number
  isTag?: number
  isEditKlg?: boolean
  isEditArea?: boolean
  childrenList?: Tree[] | null
  children?: Tree[] | null
  angleList?: any[] | null
  showCheckBox?: boolean
  parentNode?: any 
}
export interface Angle {
  angleTitle: string
  oid: number
  list?: Domain[]
}
export interface Domain {
  areaCode: string
  title: string
  parentAreaCode?: string
  parentTitle?: string
  parentId?: number
  description?: string
  isTag?: number
  angle?: string
  angleList?: Angle[]
  areaList?: Angle[]
  parentNode: {
    isEditKlg: Boolean,
    isEditArea: Boolean,
  }
}
export interface AreaDep {
  areaSourceCode?: string
  areaTargetCode: string
  description: string
  oid?: number
}

export interface KlgDetail {
  id: number
  klgCode: string
  sortId: number
  sortTitle: string
  title: string
  sysTitles: string
  cnt: string
  tagIds: string
  status: number
  statusTime: string
  statusWho: string
  statusWhoid: string
  isOk: number
  who: string
  whoName: string
  whoid: number
  notice: string
  createTime: string
  modifiedTime: string
}

export interface AreaListItem {
  areaCode: string
  label: string
}

export interface RefListItem {
  klgCode?: string
  cntName: string
  refId: number
  indexPage: string
}

export interface ProofListItem {
  klgProofBlockId?: number | null
  klgProofBlockOrderNum?: number
  conclusion: string
  klgProofCondList: ProofCondItem[]
  isDel?: boolean  //是否可以被删除
}
export interface ProofCondItem {
  klgProofCondId?: number | null
  sort?: number
  refId?: string
  cnt: string
  klgProofCondOrderNum?: number
}

export interface AuditItem {
  actionName: string,
  auditOpinion: string | null,
  auditResult: string | null,
  createTime: string,
  id: number,
  processorName: string,
}
