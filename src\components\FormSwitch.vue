<script setup lang="ts">
import { ref } from "vue"
import { Search } from "@element-plus/icons-vue"
import {
  taskCompleteType,
  taskCompleteTypeDict,
  ReferenceSortList,
  refStatus,
  refStatusDict,
  KlgTypeDictPlus,
} from "@/utils/constant"
import { getDomainListApi } from "@/apis/path/task"
const emit = defineEmits(["taskList", "user", "reference", "recycle", "audit"])
const props = defineProps({
  needTaskComplete: {
    type: Boolean,
    default: false,
  },
  needDomain: {
    type: Boolean,
    default: false,
  },
  needSearch: {
    type: Boolean,
    default: false,
  },
  needSearchUser: {
    type: Boolean,
    default: false,
  },
  needRefSort: {
    type: Boolean,
    default: false,
  },
  needRefStatus: {
    type: Boolean,
    default: false,
  },
  needRefSearch: {
    type: Boolean,
    default: false,
  },
  needKlgType: {
    type: Boolean,
    default: false,
  },
  needSearchKlg: {
    type: Boolean,
    default: false,
  },
  needAuditType: {
    type: Boolean,
    default: false,
  },
  needAuditKlg: {
    type: Boolean,
    default: false,
  },
})
interface domain {
  areaCode: string
  title: string
  choose: boolean
}

// taskList的表单
const form2TaskList = ref({
  taskStatus: -1,
  domainTitle: "",
  taskTitle: "",
})
// user的表单
const form2UserManagement = ref({
  keyword: "",
})

// ref的表单
const form2Reference = ref({
  refSort: "",
  cntName: "",
  status: 2,
})
// recycle与audit可以合并成Klg, 但是我懒
// recycle的表单
const form2Recycle = ref({
  klgType: 0,
  keyword: "",
})
// audit的表单
const form2Audit = ref({
  klgType: 0,
  keyword: "",
})

const loading = ref(false)
const domainList = ref<domain[]>([])

// 我也在改变啊，谁不在改变啊，改变就得应该，就得改变，而不是不改变
const change = (emitstr: any, form: any) => {
  emit(emitstr, form)
}

// 筛选领域
const remoteDomain = (key: string) => {
  if (key) {
    loading.value = true
    getDomainListApi(key).then((res) => {
      domainList.value = res.data.list
      loading.value = false
    })
  } else {
    domainList.value = []
  }
}
</script>

<template>
  <span class="switch-container" style="justify-content: flex-end">
    <span class="left-content"></span>
    <span class="right-content">
      <span class="select-group">
        <!-- tasklist -->
        <el-select
          @change="change(`taskList`, form2TaskList)"
          v-model="form2TaskList.taskStatus"
          placeholder="请选择任务状态"
          style="margin-right: 10px"
          class="select"
          v-if="props.needTaskComplete"
        >
          <el-option
            v-for="(value, key) in taskCompleteTypeDict"
            :key="key"
            :label="key"
            :value="value"
            :class="form2TaskList.taskStatus === value ? 'highlight' : ''"
          >
            <div>
              <span class="option">{{ key }}</span>
            </div>
          </el-option>
        </el-select>
        <el-select
          suffix-icon="Search"
          @change="change(`taskList`, form2TaskList)"
          v-model="form2TaskList.domainTitle"
          style="margin-right: 10px"
          class="select"
          filterable
          remote
          reserve-keyword
          placeholder="请选择领域"
          :remote-method="remoteDomain"
          :remote-show-suffix="true"
          :loading="loading"
          clearable
          v-if="props.needDomain"
        >
          <el-option
            v-for="item in domainList"
            :key="item.areaCode"
            :label="item.title"
            :value="item.title"
            class="primary"
          />
        </el-select>
        <el-input
          class="input"
          v-model="form2TaskList.taskTitle"
          @keydown.enter="change(`taskList`, form2TaskList)"
          placeholder="请输入任务名称"
          style="margin-right: 10px"
          v-if="props.needSearch"
        >
          <template #suffix>
            <el-icon
              class="btn el-input__icon"
              @click="change(`taskList`, form2TaskList)"
              ><Search
            /></el-icon>
          </template>
        </el-input>
        <!-- userManagement -->
        <el-input
          class="input"
          v-model="form2UserManagement.keyword"
          @keydown.enter="change(`user`, form2UserManagement)"
          placeholder="请输入用户账号"
          style="margin-right: 10px"
          v-if="props.needSearchUser"
        >
          <template #suffix>
            <el-icon
              class="btn el-input__icon"
              @click="change(`user`, form2UserManagement)"
              ><Search
            /></el-icon>
          </template>
        </el-input>
        <!-- reference -->
        <el-select
          @change="change(`refer`, form2Reference)"
          v-model="form2Reference.refSort"
          placeholder="请选择文献类型"
          style="margin-right: 10px"
          class="select"
          v-if="props.needRefSort"
        >
          <el-option
            v-for="value in ReferenceSortList"
            :key="value"
            :label="value"
            :value="value"
            :class="form2Reference.refSort === value ? 'highlight' : ''"
          >
            <div>
              <span class="option">{{ value }}</span>
            </div>
          </el-option>
        </el-select>
        <el-select
          @change="change(`refer`, form2Reference)"
          v-model="form2Reference.status"
          placeholder="请选择文献状态"
          style="margin-right: 10px"
          class="select"
          v-if="props.needRefStatus"
        >
          <el-option
            v-for="(value, key) in refStatusDict"
            :key="value"
            :label="key"
            :value="value"
            :class="form2Reference.status === value ? 'highlight' : ''"
          >
            <div>
              <span class="option">{{ key }}</span>
            </div>
          </el-option>
        </el-select>
        <el-input
          class="input"
          v-model="form2Reference.cntName"
          @keydown.enter="change(`refer`, form2Reference)"
          placeholder="请输入内容名称"
          style="margin-right: 10px"
          v-if="props.needRefSearch"
        >
          <template #suffix>
            <el-icon
              class="btn el-input__icon"
              @click="change(`refer`, form2Reference)"
              ><Search
            /></el-icon>
          </template>
        </el-input>
        <!-- recycle -->
        <el-select
          @change="change(`recycle`, form2Recycle)"
          v-model="form2Recycle.klgType"
          placeholder="请选择知识类型"
          style="margin-right: 10px"
          class="select"
          v-if="props.needKlgType"
        >
          <el-option
            v-for="(value, key) in KlgTypeDictPlus"
            :key="value"
            :label="key"
            :value="value"
            :class="form2Recycle.klgType === value ? 'highlight' : ''"
          >
            <div>
              <span class="option">{{ key }}</span>
            </div>
          </el-option>
        </el-select>
        <el-input
          class="input"
          v-model="form2Recycle.keyword"
          @keydown.enter="change(`recycle`, form2Recycle)"
          placeholder="请输入内容名称"
          style="margin-right: 10px"
          v-if="props.needSearchKlg"
        >
          <template #suffix>
            <el-icon
              class="btn el-input__icon"
              @click="change(`recycle`, form2Recycle)"
              ><Search
            /></el-icon>
          </template>
        </el-input>
        <!-- audit -->
        <el-select
          @change="change(`audit`, form2Audit)"
          v-model="form2Audit.klgType"
          placeholder="请选择知识类型"
          style="margin-right: 10px"
          class="select"
          v-if="props.needAuditType"
        >
          <el-option
            v-for="(value, key) in KlgTypeDictPlus"
            :key="value"
            :label="key"
            :value="value"
            :class="form2Audit.klgType === value ? 'highlight' : ''"
          >
            <div>
              <span class="option">{{ key }}</span>
            </div>
          </el-option>
        </el-select>
        <el-input
          class="input"
          v-model="form2Audit.keyword"
          @keydown.enter="change(`audit`, form2Audit)"
          placeholder="请输入知识名称或作者"
          style="margin-right: 10px"
          v-if="props.needAuditKlg"
        >
          <template #suffix>
            <el-icon
              class="btn el-input__icon"
              @click="change(`audit`, form2Audit)"
              ><Search
            /></el-icon>
          </template>
        </el-input>
      </span>
    </span>
  </span>
</template>

<style scoped>
.switch-container {
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: white;
  &::after {
    content: "";
    height: 1px;
    width: 100%;
    background-color: var(--color-line);
    position: absolute;
    bottom: 0;
  }
  .left-content {
  }
  .right-content {
    .select-group {
      .left-text {
        margin-right: 110px;
        font-weight: bold;
      }
      .select {
        --el-color-primary: var(--color-primary);
        width: 180px;

        &:deep(.el-input) {
          --el-input-height: 35px;
          line-height: 35px;
        }
      }
    }
    .input {
      width: 200px;
    }
    .btn {
      cursor: pointer;
    }
    &:deep(.el-input__wrapper) {
      --el-input-focus-border-color: var(--color-primary);
      border-radius: 3px;
    }
  }
}
:deep(.el-select__caret.is-reverse) {
  transform: rotate(0deg);
}
.highlight {
  color: var(--color-primary);
}
</style>
