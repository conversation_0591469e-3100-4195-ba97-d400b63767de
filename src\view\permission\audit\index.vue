<script setup lang="ts">
import MyFlipper from "@/components/MyFlipper.vue"
import { getAuditListApi } from "@/apis/path/permission"
import { onMounted, ref } from "vue"
import { MAX_PAGESIZE } from "@/utils/constant"

const currentPage = ref(1) // 当前页
const pageSize = MAX_PAGESIZE // 页大小
const tableData = ref([]) // 表格数据
const total = ref(0) // 总数

const dialogRef = ref()

// 获取角色列表
const getRoleList = () => {
  getAuditListApi().then((res) => {
    if (res.success) {
      tableData.value = res.data.list
      total.value = res.data.total 
    }
  })
}

// 计算列表index
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize + index + 1
}

// 处理换页
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage
  getRoleList()
}

onMounted(() => {
  getRoleList()
})
</script>
<template>
  <div class="wrapper">
    <div class="header-wrapper">
    </div>
    <div class="main-wrapper">
      <div class="line"></div>
      <el-table
        class="table"
        :data="tableData"
        style="width: 100%"
        empty-text="暂无数据"
        :row-style="{ height: '50px', overflow: 'hidden' }"
      >
        <el-table-column
          type="index"
          :index="indexMethod"
          label="序号"
          width="160"
          align="center"
        />
        <el-table-column prop="title" label="角色名称" width="700" align="center">
          <template #default="scope">
            <span>{{ scope.row.title }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="number" label="成员数" width="300" align="center">
          <template #default="scope">
            <span>{{ scope.row.number !== 0 ? scope.row.number : "-" }}</span>
          </template>
        </el-table-column>
      </el-table>
      <my-flipper
        @change-page="handleChangePage"
        :current="currentPage"
        :page-size="pageSize"
        :total="total"
      ></my-flipper>
    </div>
  </div>
</template>
<style scoped>
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
.wrapper {
  width: 1200px;
  min-height: 750px;
  background-color: white;
  font-family: var(--text-family);
  

  .header-wrapper {
    width: 100%;
    min-height: 20px;
    .toolbar {
      margin: 20px 0;
      padding: 0 20px;
    }
  }
  .main-wrapper {
    padding: 0 20px;
    margin-top: 40px;
    .table {
      --el-color-primary: var(--color-primary);

      .operation {
        padding: 0 10px;
        display: flex;

        .op-btn {
          cursor: pointer;
          color: var(--color-primary);
          font-family: var(--text-family);
          font-weight: 400;
          &:hover {
            font-weight: bold;
          }
        }
      }
    }
  }
}
:deep(.el-button--primary.is-text.is-disabled) {
  color: var(--color-boxborder);
}
</style>
