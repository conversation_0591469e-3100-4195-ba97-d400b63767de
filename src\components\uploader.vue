<script setup lang="ts">
import CmpButton from "./CmpButton.vue"
import { v4 as uuidv4 } from "uuid"
import { ref } from "vue"
import { genFileId } from "element-plus"
import type { UploadInstance, UploadProps, UploadRawFile } from "element-plus"
import CosHelper, { PutObjectOptions } from "@/utils/cosHelper"
const cosHelper = new CosHelper()
const fileList = ref<UploadRawFile[]>([])
const fileNameList = ref<any[]>([])

const uploadRef = ref<UploadInstance>()

const handleExceed: UploadProps["onExceed"] = (files) => {
  uploadRef.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  uploadRef.value!.handleStart(file)
}
// 手动上传附件
const submitUpload = async (): Promise<string[] | null> => {
  const uploadKeys = ref<any[]>([])
  const uploadFiles = fileList.value
  if (uploadFiles.length > 0) {
    for (const file of uploadFiles) {
      // @ts-ignore
      await uploadFile({ file: file.raw }).then((key) => {
        if (key) {
          uploadKeys.value.push(key)
        }
      })
    }
  }
  // 清空已上传文件列表
  fileList.value = []
  return uploadKeys.value
}

// 上传附件
const uploadFile = async (request: { file: File }): Promise<string | null> => {
  const now = new Date()
  const padZero = (num: number) => {
    return num < 10 ? "0" + num : num.toString()
  }
  const year = now.getFullYear()
  const month = padZero(now.getMonth() + 1)
  const day = padZero(now.getDate())
  const uuid = uuidv4() // 生成uuid
  // 定义文件在 COS 上的路径，加入格式化后的当前日期与uuid
  const key = `${year}/${month}/${day}/${uuid}_${request.file.name}`

  const bucket = import.meta.env.VITE_COS_BUCKET_NAME
  const region = import.meta.env.VITE_COS_REGION

  try {
    const result = await cosHelper.putObject({
      Bucket: bucket,
      Region: region,
      Key: key,
      Body: request.file,
    })
    if (result.statusCode === 200) {
      // console.log("filenameListinupload", fileNameList.value)
      if (fileNameList.value) {
        for (const filename of fileNameList.value) {
          deleteFileFromCos(filename)
        }
      }
      return key // 上传成功，返回key
    } else {
      return null
    }
  } catch (error) {
    console.error("Upload error", error)
    throw error
  }
}
// 设置从后端拿到的filename以从cos拿文件
const setFileNameList = (list: []) => {
  fileNameList.value = list
  fetchFilesFromCos()
}

// 从cos拿文件并且放进fileList
const fetchFilesFromCos = async () => {
  // console.log("filenameListinfetch", fileNameList.value)
  if(!fileNameList.value) return
  const files = await Promise.all(
    fileNameList.value.map(async (fileName) => {
      const key = `${fileName}` // 定义文件在 COS 上的路径
      const bucket = import.meta.env.VITE_COS_BUCKET_NAME
      const region = import.meta.env.VITE_COS_REGION

      try {
        const matchResult = fileName.match(/_(.*)\./)
        const fileNameStr: string = matchResult ? matchResult[1] : "" // 匹配第一个"_"后面的作为文件名
        const url = await cosHelper.getObjectUrl({
          Bucket: bucket,
          Region: region,
          Key: key,
        })
        return {
          name: fileNameStr,
          url: url,
          size: 0,
          type: "application/pdf", // 所有文件都是 PDF
          uid: genFileId(),
          raw: new File(
            [new Blob([`data:application/octet-stream;base64,${btoa("")}`])],
            fileNameStr,
            { type: "application/pdf" }
          ),
        }
      } catch (error) {
        console.error(`Failed to fetch file ${fileName} from COS:`, error)
        return null
      }
    })
  )
  // @ts-ignore
  fileList.value = files.filter((file) => file !== null)
}

// 删除COS文件
const deleteFileFromCos = async (key: string) => {
  // console.log("todelkey", key)
  const bucket = import.meta.env.VITE_COS_BUCKET_NAME
  const region = import.meta.env.VITE_COS_REGION
  try {
    const result = await cosHelper.deleteObject({
      Bucket: bucket,
      Region: region,
      Key: key,
    })
    // console.log(`文件删除成功`, result)
  } catch (error) {
    console.error(`Failed to delete file ${key} from COS:`, error)
  }
}

// 下载预览文件
const handlePreview = async (file: any) => {
  // 会有一个错误信息，尝试通过非安全的HTTP连接加载了一个Blob URL，而现代浏览器出于安全考虑，要求这类资源必须通过HTTPS协议提供。因为网站没有正确配置HTTPS。
  if (file.type === 'application/pdf') {
    let downloadUrl = file.url;
    try {
      const response = await fetch(downloadUrl);  // 获取文件内容
      if (!response.ok) {
        throw new Error('文件下载失败');
      }
      const blob = await response.blob();  // 将文件内容转换为 Blob 对象
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);  // 创建一个 URL 对象
      link.download = file.name;  // 指定下载的文件名
      document.body.appendChild(link);  // 将链接添加到 DOM 中
      link.click();  // 触发下载
      document.body.removeChild(link);  // 下载后移除 <a> 元素
    } catch (error) {
      console.error(error);
    }
  }
}
defineExpose({
  submitUpload,
  setFileNameList,
})
</script>
<template>
  <el-upload
    ref="uploadRef"
    v-model:file-list="fileList"
    action="#"
    :limit="1"
    :on-exceed="handleExceed"
    :auto-upload="false"
    accept=".pdf"
    :on-preview="handlePreview"
  >
    <template #trigger>
      <cmp-button type="primary" @click.prevent="">选择附件</cmp-button>
    </template>
  </el-upload>
</template>
