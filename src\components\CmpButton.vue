<template>
  <button class="btn" :class="[type]" :disabled="disabled">
    <slot></slot>
  </button>
</template>

<script setup lang="ts">
defineProps<{
  type?: string // type 为info和primary类型
  disabled?: boolean
}>()
</script>

<style scoped lang="less">
.btn {
  font-family: var(--text-font-family);
  width: 120px;
  height: 35px;
  border-radius: 2px;
  font-weight: 400;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  cursor: pointer;
  
  &[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
  }
  &.info {
    background-color: #fafafa;
    border: 1px solid var(--color-primary);
    color: var(--color-primary);

    &:hover {
      border: none;
      background-color: var(--color-second);
    }
  }

  &.primary {
    border: none;
    color: rgba(255, 255, 255, 0.996);
    background-color: var(--color-primary);
    border: 1px solid transparent;

    &:hover {
      border: none;
      background-color: var(--color-second);
      color: var(--color-primary);
      // font-weight: 700;
    }
  }
}
</style>
