<template></template>
<script></script>
<!-- <script setup lang="ts">
import CmpButton from "@/components/CmpButton.vue"
import { getRolePermissionApi, editRoleApi } from "@/apis/path/permission"
import type { params2EditRole } from "@/apis/path/permission"
import { onMounted, ref } from "vue"
import { ElMessage } from "element-plus"
import { Role } from "@/utils/type"
import { useRoute } from "vue-router"
import { checkList, strToCheckList, checkListToStr } from "@/utils/defaultList"
import router from "@/router"
const route = useRoute()

const curRole = ref<Role>({
  id: -1,
  title: "",
  rightSet: "",
})

// 获取角色列表
const getRolePermission = () => {
  if (route.query.role)
    curRole.value.id = parseInt(route.query.role?.toString())
  getRolePermissionApi(curRole.value.id).then((res) => {
    if (res.success) {
      curRole.value.rightSet = res.data.detail.rightSet
      curRole.value.title = res.data.detail.title
      if (curRole.value.rightSet) strToCheckList(curRole.value.rightSet)
    }
  })
}

// 选择全选
const handleCheckAllChange = (item: any, mode?: boolean) => {
  if (mode) item.checkAll = !item.checkAll
  item.checkedGroup = item.checkAll ? item.group : []
  item.group.forEach((box) => {
    box.check = !box.check
  })
  item.isIndeterminate = false
}

// 更新全选和半选状态
const handleCheckChildrenChange = (item: any) => {
  const checkedCount = item.checkedGroup.length
  item.checkAll = checkedCount === item.group.length
  item.isIndeterminate = checkedCount > 0 && checkedCount < item.group.length
}

// 跳转
const routerPush = () => {
  checkList.value.forEach((item) => {
    item.checkedGroup = []
    item.checkAll = false
  })
  router.push("/permission/character")
}
// 提交
const submitPermission = () => {
  let len = 0
  checkList.value.forEach((item) => {
    len += item.checkedGroup.length
  })
  if (len !== 0) {
    const rightSet = checkListToStr()
    if (rightSet) curRole.value.rightSet = rightSet
    else curRole.value.rightSet = ""
    const params: params2EditRole = {
      id: curRole.value.id,
      title: curRole.value.title,
      rightSet: curRole.value.rightSet,
    }
    editRoleApi(params).then((res) => {
      if (res.success) {
        ElMessage.success("配置成功")
        routerPush()
      }
    })
  } else {
    ElMessage.error(`请至少选择一个页面权限`)
  }
}
onMounted(() => {
  getRolePermission()
})
</script>
<template>
  <div class="wrapper">
    <div class="header-wrapper">
      <div class="line"></div>
      <div class="toolbar">
        <CmpButton
          type="info"
          @click="routerPush"
          style="margin-right: 20px; margin-top: 10px; margin-bottom: 10px"
          >返回</CmpButton
        >
        <CmpButton type="primary" @click="submitPermission">提交</CmpButton>
      </div>
    </div>
    <div class="main-wrapper">
      <div class="line"></div>
      <div class="main-content">
        <el-form>
          <el-form-item label="角色名称">
            <div class="role-title">{{ curRole.title }}</div>
          </el-form-item>
          <el-form-item label="菜单权限">
            <el-form style="width: 100%">
              <el-form-item v-for="item in checkList">
                <div class="manage-block">
                  <span>
                    <el-checkbox
                      v-model="item.checkAll"
                      :indeterminate="item.isIndeterminate"
                      @change="handleCheckAllChange(item)"
                    >
                    </el-checkbox>
                    <CmpButton
                      type="info"
                      :class="item.checkAll ? '' : 'positive'"
                      style="margin-left: 10px"
                      @click.prevent="handleCheckAllChange(item, true)"
                      >{{ item.title }}</CmpButton
                    >
                  </span>
                  <el-checkbox-group
                    v-model="item.checkedGroup"
                    class="checkbox-group"
                    @change="handleCheckChildrenChange(item)"
                  >
                    <el-checkbox
                      v-for="box in item.group"
                      :key="box.name"
                      :label="box.name"
                      :value="box"
                    >
                      {{ box.title }}
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
              </el-form-item>
            </el-form>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>
<style scoped>
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
.wrapper {
  width: 1200px;
  min-height: 750px;
  background-color: white;
  font-family: var(--text-family);

  .header-wrapper {
    width: 100%;
    .toolbar {
      gap: 10px;
      margin: 7px 0;
      padding: 0 20px;
    }
  }
  .main-wrapper {
    width: 100%;

    .main-content {
      padding: 20px;
      .role-title {
        width: 100%;
        padding: 4px 20px;
        margin-left: 20px;
        background-color: var(--color-light);
        border: 1px solid var(--color-boxborder);
      }
      .manage-block {
        width: 100%;
        padding: 20px;
        margin-bottom: 10px;
        margin-left: 20px;
        background-color: var(--color-light);
        border: 1px solid var(--color-boxborder);
        .positive {
          color: var(--color-black);
          border-color: var(--color-boxborder);
        }
        .checkbox-group {
          margin-left: 25px;
        }
      }
    }
  }
}
</style>
@/utils/defaultList -->
