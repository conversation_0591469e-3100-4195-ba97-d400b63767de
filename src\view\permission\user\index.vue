<script setup lang="ts">
import MyFlipper from "@/components/MyFlipper.vue"
import FormSwitch from "@/components/FormSwitch.vue"
import EditRoleDialog from "@/view/permission/components/EditRoleDialog.vue"
import { getUserListApi } from "@/apis/path/permission"
import type { params2GetUserList } from "@/apis/path/permission"
import { onMounted, ref } from "vue"
import { MAX_PAGESIZE } from "@/utils/constant"
import router from "@/router"

const currentPage = ref(1) // 当前页
const pageSize = MAX_PAGESIZE // 页大小
const tableData = ref([]) // 表格数据
const total = ref(0) // 总数

const dialogRef = ref()

// 获取角色列表
const getUserList = (current: number, keyword?: string) => {
  let params: params2GetUserList = {
    current: current ? current : currentPage.value,
    limit: pageSize,
    keyword: keyword? keyword : '',
  }
  getUserListApi(params).then((res) => {
    if (res.success) {
      tableData.value = res.data.list
      total.value = res.data.total
    }
  })
}

// 计算列表index
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize + index + 1
}

// 处理换页
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage
  getUserList(newPage)
}
// 打开dialog
const handleOpenDialog = (mode: number, data?: any) => {
  if (data) {
    dialogRef.value.showDialog(mode, data)
  } else {
    dialogRef.value.showDialog(mode)
  }
}

// 处理搜索用户
const handleSearchUser = (item: any) =>{
    getUserList(currentPage.value, item.keyword)
}

onMounted(() => {
  getUserList(currentPage.value)
})
</script>
<template>
  <div class="wrapper">
    <div class="header-wrapper">
      <FormSwitch :need-search-user="true" @user="handleSearchUser"></FormSwitch>
      <div class="line"></div>
    </div>
    <div class="main-wrapper">
      <el-table
        class="table"
        :data="tableData"
        style="width: 100%"
        empty-text="暂无数据"
        :row-style="{ height: '50px', overflow: 'hidden' }"
      >
        <el-table-column
          type="index"
          :index="indexMethod"
          label="序号"
          width="60"
          align="center"
        />
        <el-table-column prop="adminname" label="用户账号" width="200">
          <template #default="scope">
            <span>{{ scope.row.adminname }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="lastLoginTime" label="最近登录时间" width="200" align="center">
          <template #default="scope">
            <span>{{ scope.row.lastLoginTime?scope.row.lastLoginTime:'-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="modifiedTime" label="账号有效期至" width="200" align="center">
          <template #default="scope">
            <span>{{ scope.row.modifiedTime?scope.row.modifiedTime:'-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="gname" label="角色" width="140" align="center">
          <template #default="scope">
            <span>{{ scope.row.gname }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="approvalAuthority" label="审批权限" width="140" align="center">
          <template #default="scope">
            <span>{{ scope.row.approvalAuthority }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="count" label="是否有效" width="100" align="center">
          <template #default="scope">
            <span>{{ scope.row.isAble === 1 ? '有效' : "失效" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template #default="scope">
            <span class="operation">
              <el-button
                class="op-btn"
                type="primary"
                @click="handleOpenDialog(1, scope.row)"
                text
              >
                配置角色
              </el-button>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <my-flipper
        @change-page="handleChangePage"
        :current="currentPage"
        :page-size="pageSize"
        :total="total"
      ></my-flipper>
    </div>
  </div>
  <!-- other -->
  <edit-role-dialog ref="dialogRef" @refresh="getUserList"></edit-role-dialog>
</template>
<style scoped>
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
.wrapper {
  width: 1200px;
  min-height: 750px;
  background-color: white;
  overflow-x: hidden;
  font-family: var(--text-family);

  .header-wrapper {
    width: 100%;
    .toolbar {
      margin: 20px 0;
      padding: 0 20px;
    }
  }
  .main-wrapper {
    padding: 0 20px;

    .table {
      --el-color-primary: var(--color-primary);
      margin-top: 30px;
      .operation {
        padding: 0 10px;
        display: flex;

        .op-btn {
          cursor: pointer;
          color: var(--color-primary);
          font-family: var(--text-family);
          font-weight: 400;
          &:hover {
            font-weight: bold;
          }
        }
      }
    }
  }
}
:deep(.el-button--primary.is-text.is-disabled) {
  color: var(--color-boxborder);
}
</style>
