<script setup lang="ts">
import {
  getDomainDetailApi,
  editTagApi,
  params2EditTag,
} from "@/apis/path/domain"
import HoverEditor from "@/components/HoverEditor.vue"
import { emitter } from "@/utils/emitter"
import { ElMessage } from "element-plus"
import { onMounted, ref, watch } from "vue"
import { useRoute } from "vue-router"
import { Event } from "@/types/event"
import router from "@/router"

// 处理刷新树
const handleRefreshTree = () => {
  emitter.emit(Event.TREE_REFRESH, true)
}

const route = useRoute()
const hoverVisible = ref(true)
const tagOptions = [
  { value: 0, label: "否" },
  { value: 1, label: "是" },
]
const curDomain = ref({
  areaCode: "",
  title: "",
  parentTitle: "",
  parentAreaCode: '',
  parentId: -1,
  description: "",
  isTag: 0,
  angle: "",
  angleList: [],
  areaList: [],
  parentNode: {
    isEditKlg: false,
    isEditArea: false,
  },
})

// 获取领域详情
const getDomainDetail = () => {
  if (route.query.areaCode && route.query.label) {
    getDomainDetailApi(route.query.areaCode.toString()).then((res) => {
      if (res.success && route.query.areaCode) {
        curDomain.value.title = res.data.list.title
        curDomain.value.parentTitle = res.data.list.parentTitle
        curDomain.value.parentAreaCode = res.data.list.parentAreaCode
        curDomain.value.description = res.data.list.description
        curDomain.value.angle = res.data.list.angle
        curDomain.value.angleList = res.data.list.angleList
        curDomain.value.areaList = res.data.list.areaList
        if(res.data.list.isTag) curDomain.value.isTag = res.data.list.isTag
        else curDomain.value.isTag = 0
      }
    })
  }
}
// 处理编辑tag
const handleEditTag = () => {
  const params: params2EditTag = {
    areaCode: curDomain.value.areaCode,
    isTag: curDomain.value.isTag,
  }
  editTagApi(params).then((res) => {
    if (res.success) {
      handleRefreshTree()
      ElMessage.success("编辑成功")
    } else {
      ElMessage.error(res.message)
    }
  })
}
// 处理返回父级
const handlePush = () => {
    router.replace({
      query: {
        ...route.query,
        areaCode: curDomain.value.parentAreaCode,
        label: curDomain.value.parentTitle,
      }
    })
}
watch(
  () => route.query.areaCode,
  (newValue) => {
    if (route.query.areaCode && route.query.label) {
      curDomain.value.areaCode = route.query.areaCode.toString()
      curDomain.value.title = route.query.label.toString()
      getDomainDetail()
    }
  },
  { deep: true, immediate: true }
)
onMounted(() => {
  handleRefreshTree()
})
</script>
<template>
  <div class="wrapper">
    <div class="header-wrapper">
      <span class="header-title" style="word-break: break-all">{{
        route.query.label
      }}</span>
      <span class="header-return" @click="handlePush">返回父级</span>
    </div>
    <div class="line"></div>
    <div class="form-container">
      <el-form class="main-form">
        <el-form-item label="是否为标签领域">
          <hover-editor
            v-if="hoverVisible"
            :label="`修改是否为标签`"
            @edit="hoverVisible = !hoverVisible"
          >
            {{ curDomain.isTag === 1 ? "是" : "否" }}
          </hover-editor>
          <span class="input-box" style="width: 100%" v-else>
            <el-select
              v-model="curDomain.isTag"
              popper-class="primary"
              style="width: 200px"
            >
              <el-option
                v-for="item in tagOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <CmpButton
              type="primary"
              class="input-btn"
              @click.prevent="handleEditTag"
              >保存</CmpButton
            >
            <CmpButton
              type="info"
              class="input-btn"
              @click.prevent="hoverVisible = !hoverVisible"
              >取消</CmpButton
            >
          </span>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<style scoped>
.wrapper {
  width: 62.5vw;
  background-color: white;
  min-height: 750px;
  word-break: break-all;
  .header-wrapper {
    width: 100%;
    display: flex;
    justify-content: space-between;

    .header-title {
      margin: 10px 30px;
      font-weight: 600;
      color: var(--color-black);
    }
    .header-return {
      color: var(--color-primary);
      font-size: 12px;
      margin: 10px 30px;
      display: flex;
      align-items: center;

      &:hover {
        cursor: pointer;
        font-weight: 600;
      }
    }
  }
  .form-container {
    z-index: 2;
    padding: 10px;
    margin: 0 70px;
    margin-top: 20px;
    width: 100%;

    .main-form {
      width: 100%;
      color: var(--color-black);
      :deep(.el-form-item__label) {
        color: var(--color-black);
        font-weight: 600;
        .input-box {
          display: flex;
          align-items: center;
          flex-direction: row;
        }
      }
    }
  }
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
  margin-bottom: 15px;
}
.input-btn {
  width: 50px;
  height: 28px;
  margin-left: 5px;
}
</style>
<style>
.primary {
  --el-color-primary: var(--color-primary);
}
</style>
