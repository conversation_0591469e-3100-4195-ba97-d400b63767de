<script setup lang="ts">
import FormSwitch from '@/components/FormSwitch.vue';
import MyFlipper from '@/components/MyFlipper.vue';
import CmpButton from '@/components/CmpButton.vue';
import ReferDialog from '@/view/document/components/ReferDialog.vue';
import { refStatus, refStatusDict, ReferenceSortList, MAX_PAGESIZE } from '@/utils/constant';
import { findKeyByValue } from '@/utils/func';
import { getRefListApi, publishRefApi, recallRefApi, removeRefApi } from '@/apis/path/document';
import type { params2GetRef } from '@/apis/path/document';
import { onMounted, ref } from 'vue';
import { ElMessage } from 'element-plus';

const dialogRef = ref(); // dialog
const currentPage = ref(1); // 当前页
const pageSize = MAX_PAGESIZE; // 页大小
const tableData = ref([]); // 表格数据
const total = ref(0); // 总数
const selectionTaskList = ref([]); // 选择任务列表

// 获取文献列表
const getRefList = (current: number, cntName?: string, refSort?: string, status?: number) => {
  let params: params2GetRef = {
    current: current ? current : currentPage.value,
    limit: pageSize,
    cntName: cntName ? cntName : '',
    refSort: refSort ? refSort : '',
    status: status === 0 || status === 1 || status === 2 ? status : 2
  };
  getRefListApi(params).then((res) => {
    if (res.success) {
      tableData.value = res.data.refVoList;
      total.value = res.data.total;
    }
  });
};
// 处理文献列表更新
const handleRefreshList = (form?: any) => {
  if (form) {
    getRefList(
      currentPage.value,
      form.cntName,
      form.refSort !== ReferenceSortList[0] ? form.refSort : '',
      form.status
    );
  } else {
    getRefList(currentPage.value);
  }
};
// 处理发布文献
const handlePublishRef = (list: [any]) => {
  publishRefApi(list).then((res) => {
    if (res.success) {
      ElMessage.success('发布成功');
      handleRefreshList();
    } else {
      ElMessage.error(res.message);
    }
  });
};

// 处理撤回文献
const handleRecallRef = (list: [any]) => {
  recallRefApi(list).then((res) => {
    if (res.success) {
      ElMessage.success('撤回成功');
      handleRefreshList();
    } else {
      ElMessage.error(res.message);
    }
  });
};
// 处理删除文献
const handleRemoveRef = (id: number) => {
  removeRefApi(id).then((res) => {
    if (res.success) {
      ElMessage.success('删除成功');
      handleRefreshList();
    } else {
      ElMessage.error(res.message);
    }
  });
};
// 计算列表index
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize + index + 1;
};
// 处理选择task
const handleSelectionChange = (list: []) => {
  selectionTaskList.value = [];
  list.forEach((item) => {
    selectionTaskList.value.push(item.id);
  });
};
// 处理换页
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  getRefList(newPage);
};

// 处理打开dialog
const handleDialog = (mode: number, data?: any) => {
  if (mode === 1 && selectionTaskList.value.length === 0) {
    ElMessage.warning('请选择资料');
  } else {
    if (data) {
      dialogRef.value.showDialog(mode, data);
    } else {
      dialogRef.value.showDialog(mode);
    }
  }
};
onMounted(() => {
  getRefList(currentPage.value);
});
</script>
<template>
  <div class="wrapper">
    <div class="header-wrapper">
      <form-switch
        :need-ref-sort="true"
        :need-ref-status="true"
        :need-ref-search="true"
        @refer="handleRefreshList"
      ></form-switch>
      <div class="line"></div>
      <div class="toolbar">
        <CmpButton type="primary" @click="handleDialog(0)">新增文献</CmpButton>
        <CmpButton type="primary" @click="handleDialog(1)" style="margin-left: 20px"
          >批量发布</CmpButton
        >
      </div>
    </div>
    <div class="main-wrapper">
      <div class="line"></div>
      <el-table
        class="table"
        :data="tableData"
        style="width: 100%; overflow-x: hidden"
        empty-text="暂无数据"
        :row-style="{ height: '55px', overflow: 'hidden' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="30"
          :selectable="(row) => row.status !== refStatus.publish"
        />
        <el-table-column type="index" :index="indexMethod" label="序号" width="60" align="center" />
        <el-table-column prop="cntName" label="内容名称" min-width="300">
          <template #default="scope">
            <el-tooltip
              placement="top"
              :content="scope.row.cntName"
              :raw-content="true"
              :show-after="200"
              effect="customized"
            >
              <span class="ck-content ellipsis-text-inline" v-html="scope.row.cntName"></span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="refSort" label="文献类型" width="100" align="center" />
        <el-table-column prop="creator" label="创建者" width="100" align="center">
          <template #default="scope">
            <el-tooltip
              placement="top"
              :content="scope.row.creator"
              :raw-content="true"
              :show-after="200"
              effect="customized"
            >
              <span class="ellipsis-text-inline" v-html="scope.row.creator"></span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="200" align="center" />
        <el-table-column label="状态" width="100" align="center">
          <template #default="scope">
            {{ findKeyByValue(scope.row.status, refStatusDict) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" align="center">
          <template #default="scope">
            <span class="operationBlock">
              <el-button
                type="primary"
                @click="handlePublishRef([scope.row.id])"
                text
                class="operationBtn"
                :disabled="scope.row.status === refStatus.publish"
              >
                发布
              </el-button>
              <el-button
                type="primary"
                @click="handleDialog(2, scope.row)"
                text
                class="operationBtn"
                :disabled="scope.row.status === refStatus.publish"
              >
                编辑
              </el-button>
              <el-button
                type="primary"
                v-if="scope.row.status === 1"
                @click="handleRecallRef([scope.row.id])"
                text
                class="operationBtn"
              >
                撤回
              </el-button>
              <el-button
                type="primary"
                v-else-if="scope.row.status === 0"
                @click="handleRemoveRef(scope.row.id)"
                text
                class="operationBtn"
              >
                删除
              </el-button>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <my-flipper
        @change-page="handleChangePage"
        :current="currentPage"
        :page-size="pageSize"
        :total="total"
      ></my-flipper>
    </div>
  </div>
  <!-- other -->
  <refer-dialog
    ref="dialogRef"
    @refresh="handleRefreshList"
    @publish="handlePublishRef(selectionTaskList as any)"
  ></refer-dialog>
</template>
<style scoped>
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
.wrapper {
  width: 1200px;
  min-height: 750px;
  background-color: white;
  font-family: var(--text-family);

  .header-wrapper {
    width: 100%;
    .toolbar {
      margin: 7px 0;
      padding: 0 20px;
    }
  }
  .main-wrapper {
    padding: 0 10px;

    .table {
      --el-color-primary: var(--color-primary);
      :deep(.cell) {
        padding: 0 6px;
        line-height: 55px;
        max-height: 55px;
      }
      :deep(p) {
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .operationBlock {
        padding: 0 10px;
        display: flex;
        .operationBtn {
          font-family: var(--text-family);
          font-weight: 400;
          &:hover {
            font-weight: 600;
          }
        }
      }
    }
  }
}
:deep(.el-button--primary.is-text.is-disabled) {
  color: var(--color-boxborder);
}
</style>
