import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import type { FormInstance } from 'element-plus'
import router from '@/router'

/**
 *   跳转登录
 */

export const jumpLogin = () => {
  const route = router.currentRoute.value
  router.push(route.fullPath)
}

/**
 * 下载文件
 * @param response
 * @returns
 */
export const downloadFile = (response) => {
  return new Promise((resolve, reject) => {
    const fileReader = new FileReader()
    fileReader.onload = function () {
      try {
        const jsonData = JSON.parse((this as any).result) // 成功 说明是普通对象数据
        if (jsonData?.code !== 200) {
          ElMessage.error(jsonData?.message ?? '请求失败')
          reject(jsonData)
        }
      } catch (err) {
        // 解析成对象失败，说明是正常的文件流
        const blob = new Blob([response.data])
        // 本地保存文件
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        const filename = response?.headers?.['content-disposition']
          ?.split('filename*=')?.[1]
          ?.substr(7)
        link.setAttribute('download', decodeURI(filename))
        document.body.appendChild(link)
        link.click()
        resolve(response.data)
      }
    }
    fileReader.readAsText(response.data)
  })
}

// 处理时间,时间对象转成  YYYY-MM-DD
// 或者  2023-09-12 00:00:00 转成 YYYY-MM-DD
export function handleTime(time: Date) {
  if (!(time instanceof Date)) return time
  return time.getFullYear() + '-' + (time.getMonth() + 1) + '-' + time.getDate()
}

// 解析时间,时间对象转成 2023-09-12 00:00:00 -> YYYY-MM-DD
export function parseTime(time: string) {
  if (!time) return ''
  return time.split(' ')[0]
}
// 手机号脱敏处理
export function handlePhone(phone: string) {
  return ''.concat(phone.slice(0, 3)).concat('*'.repeat(4)).concat(phone.slice(7))
}

// 时间选择组件器组件禁用选择未来时间，只能选择过去的时间
export const disabledDate = (time: Date) => {
  return time.getTime() > Date.now()
}

// 获取缓存
export const getCache = (key: string) => {
  return JSON.parse(localStorage.getItem(key) as string)
}

// 设置缓存 (里面存对象)
export const setCache = (item: string, key: string, val: object | string | null) => {
  // return localStorage
  const cache = getCache(item)
  // if (!cache || typeof cache === 'string') {
  //   // 之前没有
  //   localStorage.setItem(item, JSON.stringify(val))
  // } else if (typeof cache === 'object') {
  //   // 之前有缓存
  // }
  localStorage.setItem(item, JSON.stringify(Object.assign({}, cache, { [key]: val })))
}

// 表单同意验证函数
export async function validateFormFn(
  formEl: FormInstance | FormInstance[] | undefined,
  callback: Function
) {
  if (!formEl) return
  if (Array.isArray(formEl)) {
    let flag = true
    for (const f of formEl) {
      await f.validate(async (valid) => {
        if (!valid) {
          flag = false
        }
      })
    }
    if (flag) callback()
  } else {
    await formEl.validate(async (valid, fields) => {
      if (valid) {
        callback()
      }
    })
  }
}
// 关闭对话框重置表单验证
export function resetValidate(formEl: FormInstance | undefined) {
  if (!formEl) return
  formEl.resetFields()
}

// 处理cookie
// https://developer.mozilla.org/zh-CN/docs/DOM/document.cookie
/* eslint-disable */
export const docCookies = {
  getItem: function (sKey: string) {
    return (
      decodeURIComponent(
        document.cookie.replace(
          new RegExp(
            '(?:(?:^|.*;)\\s*' +
              encodeURIComponent(sKey).replace(/[-.+*]/g, '\\$&') +
              '\\s*\\=\\s*([^;]*).*$)|^.*$'
          ),
          '$1'
        )
      ) || null
    )
  },
  setItem: function (
    sKey: string,
    sValue: string,
    sDomain: string,
    sPath?: string,
    vEnd?: Date | string | number,
    bSecure?: string
  ) {
    if (!sKey || /^(?:expires|max\-age|path|domain|secure)$/i.test(sKey)) {
      return false
    }
    let sExpires = ''
    if (vEnd) {
      switch (vEnd.constructor) {
        case Number:
          sExpires =
            vEnd === Infinity ? '; expires=Fri, 31 Dec 9999 23:59:59 GMT' : '; max-age=' + vEnd
          break
        case String:
          sExpires = '; expires=' + vEnd
          break
        case Date:
          // @ts-ignore
          sExpires = '; expires=' + vEnd.toUTCString()
          break
      }
    }
    document.cookie =
      encodeURIComponent(sKey) +
      '=' +
      encodeURIComponent(sValue) +
      sExpires +
      (sDomain ? '; domain=' + sDomain : '') +
      (sPath ? '; path=' + sPath : '') +
      (bSecure ? '; secure' : '')
    return true
  },
  removeItem: function (sKey: string, sPath: string, sDomain: string) {
    if (!sKey || !this.hasItem(sKey)) {
      return false
    }
    document.cookie =
      encodeURIComponent(sKey) +
      '=; expires=Thu, 01 Jan 1970 00:00:00 GMT' +
      (sDomain ? '; domain=' + sDomain : '') +
      (sPath ? '; path=' + sPath : '')
    return true
  },
  hasItem: function (sKey: string) {
    return new RegExp(
      '(?:^|;\\s*)' + encodeURIComponent(sKey).replace(/[-.+*]/g, '\\$&') + '\\s*\\='
    ).test(document.cookie)
  },
  keys: /* optional method: you can safely remove it! */ function () {
    // @ts-ignore
    const aKeys = document.cookie
      .replace(/((?:^|\s*;)[^\=]+)(?=;|$)|^\s*|\s*(?:\=[^;]*)?(?:\1|$)/g, '')
      .split(/\s*(?:\=[^;]*)?;\s*/)
    for (let nIdx = 0; nIdx < aKeys.length; nIdx++) {
      aKeys[nIdx] = decodeURIComponent(aKeys[nIdx])
    }
    return aKeys
  }
}
/* eslint-enable */

// 处理路由重定向
// export function nextJumpTo(){
//   setTimeout(() => {
//     window.open(`${import.meta.env.VITE_APP_YOUTH_URL}/login?type=3&&redirect=${import.meta.env.VITE_BLACK_URL}`,'_self')
//   }, 500);
// }
