<script setup lang="ts">
import KlgInfo from './KlgInfo.vue';
import PreKlgShowList from '@/components/PreKlgShowList.vue';
import AuditRecordList from '@/components/AuditRecordList.vue';

import ShowAnswerDrawer from '@/components/ShowAnswerDrawer.vue';

import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const curKlgCode = ref('');
const klgInfoRef = ref();

// 处理点击选择 - 现在通过自定义事件处理，不再需要直接调用showDrawer

watch(
  () => route.query.klgCode,
  () => {
    if (route.query.klgCode) {
      curKlgCode.value = route.query.klgCode.toString();
    }
  },
  { deep: true, immediate: true }
);
</script>
<template>
  <div class="wrapper">
    <div class="left-wrapper">
      <KlgInfo
        ref="klgInfoRef"
        :editable="false"
        :klgCode="curKlgCode"
      ></KlgInfo>
    </div>
    <div class="right-wrapper">
      <PreKlgShowList
        :klgCode="curKlgCode"
        :questionList="klgInfoRef?.questionList"
      ></PreKlgShowList>
      <AuditRecordList
        style="margin-top: 10px"
        :klgCode="curKlgCode"
      ></AuditRecordList>
    </div>
  </div>
  <!-- ShowAnswerDrawer 全局组件，供浮动弹窗使用 -->
  <ShowAnswerDrawer />
</template>
<style scoped>
.wrapper {
  display: flex;
  flex-direction: row;
  .left-wrapper {
    width: 750px;
    margin-right: 10px;
  }
  .right-wrapper {
    width: 450px;
  }
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
</style>
