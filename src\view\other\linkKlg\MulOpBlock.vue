<script setup lang="ts">
import MyFlipper from "@/components/MyFlipper.vue"

import { ElMessage, ElTable, ElTree, INPUT_EVENT } from "element-plus"
import { nextTick, ref, watch } from "vue"
import { Tree } from "@/utils/type"
import { transformToTreeNode, findKeyByValue } from "@/utils/func"
import {
  KlgTypeDict,
  MAX_PAGESIZE,
  TreeType,
  klgAuditTypeDict,
} from "@/utils/constant"
import { getTreeApi } from "@/apis/path/domain"
import {
  getKlgListApi,
  params2GetKlgList,
  params2MulOpKlg,
  areaToKlgsListItem,
  move4AreaApi,
  add4AreaApi,
} from "@/apis/path/klg"

const filterText = ref("")
const treeRef = ref<InstanceType<typeof ElTree>>()
const tableRef = ref<InstanceType<typeof ElTable>>()
const treeData = ref()
const tableData = ref()
const total = ref(0)
const currentPage = ref(1)
const pageSize = MAX_PAGESIZE
const listLength = ref(0) // 选择klg个数
const params2MulOpKlgs = ref<params2MulOpKlg>({
  targetAreaCode: "",
  areaToKlgsList: [],
})

const params = ref<params2GetKlgList>({
  current: currentPage.value,
  limit: pageSize,
  areaCode: "",
  keyword: "",
  hasPre: 2,
  name: "", // 作者（获取全部列表时用）
  number: "", // 编号（获取全部列表时用）
  range: 1, // 查询领域范围 0: all || 1:mine
  sortBy: 0, //
  sortOrder: 0,
  status: "", // 审核状态1待审核0审核通过2审核拒绝（获取全部列表时使用）
  type: "", // 知识类型
})
// 获取编辑权限树
const getTree = () => {
  getTreeApi(TreeType.edit).then((res) => {
    treeData.value = [transformToTreeNode(res.data.tree[0])]
    treeData.value[0].unclick = true
  })
}
// 处理表格选择
const handleSelectionChange = (list: [], row: any) => {
  const item = params2MulOpKlgs.value.areaToKlgsList.find((item) => {
    return item.clickAreaCode === params.value.areaCode
  })
  if (item) {
    const index = item.klgCodeList.findIndex(
      (item) => item.klgCode === row.klgCode
    )
    if (index !== -1) {
      item.klgCodeList.splice(index, 1)
      --listLength.value
    } else {
      const obj = {
        klgCode: row.klgCode,
        title: row.title,
      }
      console.log("obj", obj)
      item.klgCodeList.push(obj)
      ++listLength.value
    }
    // console.log("有item", item)
  } else {
    // console.log("没有item")
    saveKlgs(row)
    ++listLength.value
  }
}
// 处理表格选择全部
const handleSelectionAllChange = (list: []) => {
  console.log("list", list)
  const item = params2MulOpKlgs.value.areaToKlgsList.find((item) => {
    return item.clickAreaCode === params.value.areaCode
  })
  if (item) {
    if (list.length === 0) {
      tableData.value.forEach((row) => {
        const index = item.klgCodeList.findIndex(
          (i) => i.klgCode === row.klgCode
        )
        --listLength.value
        item.klgCodeList.splice(index, 1)
      })
    } else {
      list.forEach((row) => {
        const index = item.klgCodeList.findIndex((i)=> i.klgCode === row.klgCode)
        if(index === -1) {
          handleSelectionChange([], row)
        }
      })
    }
  } else {
    list.forEach((i, index) => {
      if (index === 0) {
        saveKlgs(i)
        ++listLength.value
      } else {
        handleSelectionChange([], i)
      }
    })
  }
}
// 改变当行的状态
const handleChangeRow = (row: any) => {
  const selectedRows = tableRef.value?.getSelectionRows()
  selectedRows.forEach((item) => {
    if (item.klgCode === row.klgCode) {
      tableRef.value?.toggleRowSelection(item)
    }
  })
}
// 过滤node
const filterNode = (value: string, data: Tree) => {
  if (!value) return true
  return data.label.includes(value)
}
watch(filterText, (val) => {
  treeRef.value!.filter(val)
})
// 处理点击node
const handleClickTreeNode = (data: Tree) => {
  params.value.areaCode = data.areaCode
  getTableData()
}
// 处理换页
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage
  getTableData(newPage)
}
// 计算列表index
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize + index + 1
}
// 获取table
const getTableData = (page?: number) => {
  params.value.current = page ? page : currentPage.value
  getKlgListApi(params.value).then((res) => {
    if (res.success) {
      tableData.value = res.data.list
      total.value = res.data.total
      const area = params2MulOpKlgs.value.areaToKlgsList.find((item) => {
        if (item.clickAreaCode === params.value.areaCode) {
          return item
        }
      })
      area?.klgCodeList.forEach((item) => {
        tableData.value.forEach((row) => {
          if (row.klgCode === item) {
            nextTick(() => {
              tableRef.value?.toggleRowSelection(row, true)
            })
          }
        })
      })
    }
  })
}
// 保存选择知识点
const saveKlgs = (row: any) => {
  const moveKlgItem: areaToKlgsListItem = {
    clickAreaCode: params.value.areaCode,
    klgCodeList: [
      {
        klgCode: row.klgCode,
        title: row.title,
      },
    ],
  }
  params2MulOpKlgs.value.areaToKlgsList.push(moveKlgItem)
}
// 删除选择的tag
const handleCloseTag = (tag: any) => {
  params2MulOpKlgs.value.areaToKlgsList.forEach((areaklgs) => {
    const index = areaklgs.klgCodeList.findIndex(
      (item) => item.klgCode === tag.klgCode
    )
    if (index !== -1) {
      areaklgs.klgCodeList.splice(index, 1)
      --listLength.value
    }
  })
  handleChangeRow(tag)
}
// 设置目标领域
const setTargetAreaCode = (code: string) => {
  params2MulOpKlgs.value.targetAreaCode = code
}
// 处理提交
const handleSubmit = async (mode: number): Promise<Boolean> => {
  const params = {
    areaToKlgsList: params2MulOpKlgs.value.areaToKlgsList.map((item) => {
      return {
        clickAreaCode: item.clickAreaCode,
        klgCodeList: item.klgCodeList.map((i) => {
          return i.klgCode
        }),
      }
    }),
    targetAreaCode: params2MulOpKlgs.value.targetAreaCode,
  }
  if (mode === 5) {
    // 批量添入
    try {
      const res = await add4AreaApi(params)
      if (res.success) {
        ElMessage.success("添入成功")
        return true
      } else {
        ElMessage.error(res.message)
        return false
      }
    } catch (e) {
      return false
    }
  } else if (mode === 6) {
    // 批量移入
    try {
      const res = await move4AreaApi(params)
      if (res.success) {
        ElMessage.success("移入成功")
        return true
      } else {
        ElMessage.error(res.message)
        return false
      }
    } catch (e) {
      return false
    }
  }
  return false
}
// 重置params
const resetParams = () => {
  params2MulOpKlgs.value.areaToKlgsList = []
  params2MulOpKlgs.value.targetAreaCode = ""
  listLength.value = 0
  nextTick(() => {
    tableRef.value?.clearSelection()
  })
}
defineExpose({
  getTree,
  setTargetAreaCode,
  handleSubmit,
  resetParams,
})
</script>
<template>
  <div class="wrapper">
    <div class="up-container">
      <span class="up-left">
        <el-input v-model="filterText" placeholder="请输入查询内容" />
        <span class="tree-container">
          <el-tree
            ref="treeRef"
            class="tree"
            :data="treeData"
            node-key="areaCode"
            :filter-node-method="filterNode"
            :highlight-current="true"
            :expand-on-click-node="false"
          >
            <template #default="{ node, data }">
              <span
                class="tree-node"
                :class="!data.isEditArea ? 'unclick' : ''"
                @click="!data.isEditArea ? '' : handleClickTreeNode(data)"
              >
                <span> {{ node.label }}</span>
              </span>
            </template>
          </el-tree>
        </span>
      </span>
      <span class="up-right">
        <el-table
          ref="tableRef"
          :data="tableData"
          @select="handleSelectionChange"
          @select-all="handleSelectionAllChange"
          height="445"
        >
          <el-table-column type="selection" width="30" />
          <el-table-column
            type="index"
            :index="indexMethod"
            label="序号"
            width="60"
            align="center"
          />
          <el-table-column label="知识名称" width="200">
            <template #default="scope">
              <span class="ck-content" v-html="scope.row.title"></span>
            </template>
          </el-table-column>
          <el-table-column label="知识类型" width="100" align="center">
            <template #default="scope">
              {{ findKeyByValue(scope.row.sortId, KlgTypeDict) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="modifiedTime"
            label="保存时间"
            width="200"
            align="center"
          ></el-table-column>
          <el-table-column label="状态" width="70" align="center">
            <template #default="scope">
              {{ findKeyByValue(scope.row.status, klgAuditTypeDict) }}
            </template>
          </el-table-column>
        </el-table>
        <my-flipper
          @change-page="handleChangePage"
          :current="currentPage"
          :page-size="pageSize"
          :total="total"
        ></my-flipper>
      </span>
    </div>

    <div class="down-container">
      <el-collapse accordion>
        <el-collapse-item name="1">
          <template #title> 已选中: {{ listLength }} 个知识点 </template>
          <div>
            <span
              v-for="(item, idx1) in params2MulOpKlgs.areaToKlgsList"
              :key="idx1"
            >
              <el-tag
                v-for="(tag, idx2) in item.klgCodeList"
                :key="idx2"
                closable
                @close="handleCloseTag(tag)"
                :disable-transitions="true"
              >
                <span style="word-break: break-all;" v-html="tag.title"></span>
              </el-tag>
            </span>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>
<style scoped>
:deep(.el-collapse) {
  border: none;
  background-color: transparent;
  height: auto;
  --el-collapse-content-bg-color: transparent;
}
:deep(.el-collapse-item__header) {
  border: none;
  background-color: transparent;
  height: 20px;
}
:deep(.el-collapse-item__content) {
  border: none;
  background-color: transparent;
  padding: 0;
}
:deep(.el-collapse-item__wrap) {
  border: none;
}
:deep(.el-tree-node__content) {
  min-width: 174px;
}
:deep(p) {
  margin: 0;
}
y .unclick {
  color: var(--color-deep);
}
.wrapper {
  display: flex;
  flex-direction: column;
  .up-container {
    width: 100%;
    display: flex;
    flex-direction: row;
    .up-left {
      padding: 5px;
      background-color: var(--color-second);
      display: flex;
      width: 100%;
      flex-direction: column;

      .tree-container {
        display: flex;
        width: 100%;

        .tree {
          display: flex;
          height: 440px;
          width: 100%;
          max-width: 192px;
          overflow: auto;
          background-color: var(--color-second);
          .tree-node {
            color: var(--color-black);
            background-color: white;
            padding: 2px 5px;
          }
          .unclick {
            color: var(--color-boxborder);
            cursor: not-allowed;
          }

          &::-webkit-scrollbar {
            width: 5px; /* 定义纵向滚动条的宽度 */
            height: 5px; /* 定义横向滚动条的高度 */
          }
          &::-webkit-scrollbar-thumb {
            background-color: #888;
            border-radius: 6px;
          }
          &::-webkit-scrollbar-track {
            background-color: #f1f1f1;
          }
        }
      }
    }
    .up-right {
      /* width: 100%; */
      margin-left: 5px;
    }
  }
  .down-container {
    margin-top: 5px;
    padding: 10px 20px;
    background-color: var(--color-second);
    font-weight: 600;
    font-size: 12px;
  }
}
</style>
