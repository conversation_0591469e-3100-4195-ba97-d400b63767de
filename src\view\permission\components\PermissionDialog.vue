<script setup lang="ts">
import CmpButton from "@/components/CmpButton.vue"
import { ElMain, ElMessage, FormInstance, tagEmits } from "element-plus"
import { addRole<PERSON>pi, editRole<PERSON>pi, deleteRole<PERSON><PERSON> } from "@/apis/path/permission"
import type { params2AddRole, params2EditRole } from "@/apis/path/permission"
import { ref, defineExpose } from "vue"
import { Role } from "@/utils/type"

const curMode = ref<Number>()
const curRole = ref<Role>({
  id: -1,
  title: "",
  count: 0,
})
const dialogVisible = ref(false) // 是否dialog可见
const titleText = ref("")
const roleFormRef = ref<FormInstance>()
const emits = defineEmits(["refresh"])
// 展示dialog
const showDialog = (mode: number, data?: Role) => {
  // 初始化为空再赋值
  curRole.value.id = -1
  curRole.value.title = ""
  curRole.value.count = 0

  curMode.value = mode
  dialogVisible.value = true
  switch (mode) {
    case 1:
      titleText.value = "新建角色"
      break
    case 2:
      titleText.value = "修改角色"
      break
    case 3:
      titleText.value = "删除角色"
      break
  }
  if (data) {
    curRole.value.id = data.id
    curRole.value.title = data.title
    curRole.value.count = data.count
  }
}
// 处理提交新建角色
const handleAddRole = () => {
  roleFormRef.value?.validate((valid) => {
    if (valid) {
      const params: params2AddRole = {
        title: curRole.value.title,
      }
      addRoleApi(params).then((res) => {
        if (res.success) {
          ElMessage.success("添加成功")
          handleClose()
          emits("refresh")
        }
      })
    }
  })
}
// 处理提交修改角色
const handleChangeRole = () => {
  roleFormRef.value?.validate((valid) => {
    if (valid) {
      const params: params2EditRole = {
        id: curRole.value.id,
        title: curRole.value.title,
      }
      editRoleApi(params).then((res) => {
        if (res.success) {
          ElMessage.success("修改成功")
          handleClose()
          emits("refresh")
        }else {
          ElMessage.error(res.message)
        }
      })
    }
  })
}

// 处理提交删除角色
const handleDeleteRole = () => {
  deleteRoleApi(curRole.value.id).then((res) => {
    if (res.success) {
      ElMessage.success("删除成功")
      handleClose()
      emits("refresh")
    } else {
      ElMessage.error("删除失败")
    }
  })
}

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  showDialog,
})
</script>
<template>
  <el-dialog v-model="dialogVisible" width="600" destroy-on-close>
    <template #header>
      <span>{{ titleText }}</span>
    </template>
    <el-form ref="roleFormRef" :model="curRole">
      <el-form-item
        v-if="curMode === 1 || curMode === 2"
        label="角色名称"
        prop="title"
        required
        :rules="{
          required: true,
          message: '请输入角色名称',
          trigger: 'blur',
        }"
      >
        <el-input
          v-model="curRole.title"
          placeholder="请输入角色名称"
          @keydown.enter="handleAddRole"
        ></el-input>
      </el-form-item>
      <el-form-item v-else-if="curMode === 3">
        <span style="display: flex; justify-content: center; width: 100%">
          <span style="color: var(--color-black)"
            >您要删除的角色将不能被恢复，您确定要删除该角色吗？</span
          >
        </span>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <CmpButton type="info" @click="handleClose">关闭</CmpButton>
        <CmpButton type="primary" v-if="curMode === 1" @click="handleAddRole"
          >确定</CmpButton
        >
        <CmpButton
          type="primary"
          v-else-if="curMode === 2"
          @click="handleChangeRole"
          >确定</CmpButton
        >
        <CmpButton
          type="primary"
          v-else-if="curMode === 3"
          @click="handleDeleteRole"
          >确定</CmpButton
        >
      </div>
    </template>
  </el-dialog>
</template>
<style scoped>
.dialog-footer {
  margin-top: 100px;
  display: flex;
  justify-content: center;
  gap: 40px;
}
</style>
