import "./assets/main.less";
import { createApp } from "vue";
import { createPinia } from "pinia";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import App from "./App.vue";
import router from "./router";
import "element-plus/dist/index.css";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";
import locale from "element-plus/es/locale/lang/zh-cn";
import hljsVuePlugin from '@highlightjs/vue-plugin';
import 'highlight.js/styles/agate.min.css';

const app = createApp(App);

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
app.use(createPinia().use(piniaPluginPersistedstate));
app.use(router);
app.use(ElementPlus, { locale });
app.use(hljsVuePlugin);
app.mount("#app");
