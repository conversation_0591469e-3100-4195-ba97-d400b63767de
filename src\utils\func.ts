import { MAX_PAGESIZE } from '@/utils/constant';
// @ts-ignore
import picSrc from '@/assets/image/common/pic_icon.jpg';
// @ts-ignore
import tableSrc from '@/assets/image/common/table.png';
// @ts-ignore
import codeSrc from '@/assets/image/common/terminal.png';
export const findKeyByValue = (
  value: string | number,
  dict: { [key: string]: string } | { [key: string]: number }
) => Object.keys(dict).find((key) => dict[key] === value);

// 转换数据格式
export const transformToTreeNode = (data: any) => {
  return {
    areaCode: data.areaCode,
    label: data.title,
    checked: data.checked,
    show: false,
    areaId: data.areaId,
    isEditArea: data.areaEdit1,
    isEditKlg: data.areaData1,
    isTag: data.isTag,
    children: data.areaList ? data.areaList.map(transformToTreeNode) : undefined,
    showCheckBox: data.areaEdit1
  };
};
