<script setup lang="ts">
import { ref } from 'vue';
import CmpButton from '@/components/CmpButton.vue';

import { addMyAuditPoolApi, changeAuditKlgApi } from '@/apis/path/audit';
import { ElMessage } from 'element-plus';
const dialogVisible = ref(false);
const tableData = ref();
const curMode = ref(); // 0：我来审核 | 1：换人审核
const titleText = ref();

const emits = defineEmits(['submit']);

// 展示dialog
const showDialog = (mode: number, data: any) => {
  dialogVisible.value = true;
  curMode.value = mode;
  tableData.value = data;
  switch (mode) {
    case 0:
      titleText.value = '我来审核';
      break;
    case 1:
      titleText.value = '换人审核';
      break;
  }
};
// 处理提交
const handleSubmit = () => {
  if (curMode.value === 0) {
    const list = tableData.value.map((item) => {
      return item.klgCode;
    });
    addMyAuditPoolApi(list).then((res) => {
      if (res.success) {
        ElMessage.success('添加成功');
        dialogVisible.value = false;
        emits('submit', true);
      } else {
        ElMessage.error(res.message);
      }
    });
  } else if (curMode.value === 1) {
    const list = tableData.value.map((item) => {
      return item.klgCode;
    });
    changeAuditKlgApi(list).then((res) => {
      if (res.success) {
        ElMessage.success('移除成功');
        dialogVisible.value = false;
        emits('submit', true);
      } else {
        ElMessage.error(res.message);
      }
    });
  }
};
defineExpose({
  showDialog
});
</script>
<template>
  <el-dialog v-model="dialogVisible" width="800">
    <template #header>
      <span style="font-weight: 600">{{ titleText }}</span>
    </template>
    <span v-show="curMode === 0">您确认要审核下面所有知识吗？</span>
    <span v-show="curMode === 1">您确认要将下列知识退回到待审核例表中吗？</span>
    <el-table :data="tableData" class="table">
      <el-table-column label="知识名称" min-width="180" height="55">
        <template #default="scope">
          <el-tooltip
            placement="top"
            :content="scope.row.title"
            :raw-content="true"
            :show-after="200"
            effect="customized"
          >
            <span class="ck-content ellipsis-text-inline" v-html="scope.row.title"></span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="作者" align="center" width="100" class="ellipsis-text">
        <template #default="scope">
          <el-tooltip
            placement="top"
            :content="scope.row.whoName"
            :raw-content="true"
            :show-after="200"
            effect="customized"
          >
            <p class="ck-content ellipsis-text-inline" v-html="scope.row.whoName"></p>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="提交时间" align="center"></el-table-column>
    </el-table>
    <template #footer>
      <div class="footer">
        <cmp-button type="info" @click="dialogVisible = false">关闭</cmp-button>
        <cmp-button type="primary" @click="handleSubmit"> 确定 </cmp-button>
      </div>
    </template>
  </el-dialog>
</template>
<style scoped>
:deep(p) {
  margin: 0;
}
.table {
  --el-color-primary: var(--color-primary);
  :deep(.cell) {
    padding: 0 6px;
    line-height: 55px;
    max-height: 55px;
  }
  :deep(p) {
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.footer {
  display: flex;
  justify-content: center;
  gap: 40px;
}
</style>
