// 不带span的版本，可以实现table局部划词，大小写匹配, will be used in the future
import { clone, throttle } from 'lodash-es';
import { ElMessage } from 'element-plus';
import { escape } from 'lodash-es';

const throwErr = throttle((msg: string) => {
  ElMessage.error(msg);
}, 5000);

function handleLineWord(): null | string | undefined {
  const selection = window.getSelection();
  if (!selection || selection.isCollapsed) {
    return null;
  }
  const range = selection.getRangeAt(0);
  const htmlContent = document.getElementById('htmlContent');
  const lineWordHtmlContents = document.getElementsByClassName('lineWordContent');

  let cloneContents;
  let node: Node | HTMLElement | null = range.commonAncestorContainer;
  let str;
  if (node.nodeType == Node.TEXT_NODE) {
    node = node.parentElement;
  }
  console.dir(node);
  if ((node as HTMLElement)?.closest("[class^='inline-equation']")) {
    cloneContents = (node as HTMLElement).closest("[class^='inline-equation']");
    const latexCode = (cloneContents as HTMLLegendElement).getAttribute('latexCode');
    str = `<script type="math/tex">${latexCode}</script>`;
  } else if ((node as HTMLElement)?.closest("[class^='equation']")) {
    cloneContents = (node as HTMLElement).closest("[class^='equation']");
    const latexCode = (cloneContents as HTMLLegendElement).getAttribute('latexCode');
    str = `<script type="math/tex; mode=display">${latexCode}</script>`;
  } else {
    cloneContents = range.cloneContents();
    str = buildLineWord(cloneContents);
    // console.log(str, str.length);
  }
  console.log(str);
  if (str.trim().length == 0) {
    return null;
  }

  let flag = false;
  for (let i = 0; i < lineWordHtmlContents.length; i++) {
    if (lineWordHtmlContents[i]?.contains(range.commonAncestorContainer)) {
      flag = true;
    }
  }
  console.log('2V2_lineWordHtmlContents', lineWordHtmlContents);
  if (!flag) {
    throwErr('请在内容区域内划词');
    return null;
  }
  // if (!htmlContent?.contains(range.commonAncestorContainer)) {
  //   throwErr("请在内容区域内划词")
  //   return null
  // }
  console.log("linewordcontent", str)
  return str;
}

function buildLineWord(node: Node): string {
  let str = '';
  const dfs = (node: Node) => {
    if (node.nodeType == Node.TEXT_NODE) {
      const textContent = node.textContent as string;
      // str += escape(textContent.replace(/\n/g, '').replace(/\r/g, '').replace(/\t/g, ''));
      str += escape(textContent);
    } else if (node.nodeType == Node.ELEMENT_NODE) {
      if ((node as HTMLElement).tagName == 'IMG') {
        str += (node as HTMLElement).outerHTML;
      } else {
        if ((node as HTMLElement).closest("[class^='inline-equation']")) {
          const element = (node as HTMLElement).closest("[class^='inline-equation']");
          const latexCode = element?.getAttribute('latexCode') as string;
          str += `<script type="math/tex">${latexCode}</script>`;
        } else if ((node as HTMLElement).closest("[class^='equation']")) {
          const element = (node as HTMLElement).closest("[class^='equation']");
          const latexCode = element?.getAttribute('latexCode') as string;
          str += `<script type="math/tex; mode=display">${latexCode}</script>`;
        } else {
          for (const child of (node as HTMLElement).childNodes) {
            dfs(child);
          }
        }
      }
    }
  };
  for (const childNode of node.childNodes) {
    dfs(childNode);
  }
  return str;
}

export { handleLineWord }
 