<script setup lang="ts">
import CmpButton from "@/components/CmpButton.vue"
import { ElMessage, FormInstance } from "element-plus"
import { getUserSelectRoleApi, editUserAdminApi } from "@/apis/path/permission"
import type { params2EditAdmin } from "@/apis/path/permission"
import { ref, defineExpose, onMounted, reactive } from "vue"
import { ApprovalAuthorityList } from "@/utils/constant"
import { Role } from "@/utils/type"
const curMode = ref<Number>()
const curData = ref<params2EditAdmin>({
  adminname: "",
  approvalAuthority: "",
  expireTime: "",
  gid: -1,
  gname: "",
  id: -1,
  name: "",
  pwd: "",
})
const selectRoleList = ref<Role[]>([])
const dialogVisible = ref(false) // 是否dialog可见
const titleText = ref("")
const roleFormRef = ref<FormInstance>()
const emits = defineEmits(["refresh"])

// 展示dialog
const showDialog = (mode: number, data?: any) => {
  // 初始化
  curData.value.adminname = data.adminname
  curData.value.approvalAuthority = data.approvalAuthority
  curData.value.expireTime = data.expireTime
  curData.value.gid = data.gid
  curData.value.gname = data.gname
  curData.value.id = data.id
  curData.value.name = data.name
  curData.value.pwd = data.pwd

  curMode.value = mode
  dialogVisible.value = true
  switch (mode) {
    case 1:
      titleText.value = "配置角色"
      break
  }
}
// 初始化selectRoleList
const initRoleList = () => {
  getUserSelectRoleApi().then((res) => {
    if (res.success) {
      selectRoleList.value = res.data.list
    }
  })
}
// 处理提交修改角色
const handleChangeRole = () => {
  roleFormRef.value?.validate((valid) => {
    if (valid) {
      const params: params2EditAdmin = {
        adminname: curData.value.adminname,
        approvalAuthority: curData.value.approvalAuthority,
        expireTime: curData.value.expireTime,
        gid: curData.value.gid,
        gname: curData.value.gname,
        id: curData.value.id,
        name: curData.value.name,
        pwd: curData.value.pwd,
      }
      editUserAdminApi(params).then((res) => {
        if (res.success) {
          ElMessage.success("修改成功")
          handleClose()
          emits("refresh")
        } else {
          ElMessage.error(res.message)
        }
      })
    }
  })
}

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false
}
// 处理选择角色
const handleSelectRole = (item: any) => {
  curData.value.gid = item.id
  curData.value.gname = item.title
}
onMounted(() => {
  initRoleList()
})
defineExpose({
  showDialog,
})
</script>
<template>
  <el-dialog v-model="dialogVisible" width="600" destroy-on-close>
    <template #header>
      <span>{{ titleText }}</span>
    </template>
    <el-form ref="roleFormRef" :model="curData" style="padding: 0 30px">
      <el-form-item label="用户账户" prop="adminname" required :rules="{}">
        <el-input
          v-model="curData.adminname"
          placeholder="请输入角色名称"
          disabled
        ></el-input>
      </el-form-item>
      <el-form-item label="选择角色" prop="gname" required>
        <el-select
          placeholder="请选择"
          style="margin-right: 10px"
          v-model="curData.gname"
        >
          <el-option
            v-for="item in selectRoleList"
            :key="item.id"
            :label="item.title"
            :value="item.title"
            :class="curData.gname === item.title ? 'highlight' : ''"
            @click="handleSelectRole(item)"
          >
            <div>
              <span class="option">{{ item.title }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审批权限" prop="approvalAuthority" required>
        <el-select
          placeholder="请选择"
          style="margin-right: 10px"
          v-model="curData.approvalAuthority"
        >
          <el-option
            v-for="item in ApprovalAuthorityList"
            :key="item"
            :label="item"
            :value="item"
            :class="curData.approvalAuthority === item ? 'highlight' : ''"
          >
            <div>
              <span class="option">{{ item }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <CmpButton type="info" @click="handleClose">关闭</CmpButton>
        <CmpButton type="primary" @click="handleChangeRole">确定</CmpButton>
      </div>
    </template>
  </el-dialog>
</template>
<style scoped>
.dialog-footer {
  margin-top: 100px;
  display: flex;
  justify-content: center;
  gap: 40px;
}

.highlight {
  --el-color-primary: var(--color-primary);
}
</style>
