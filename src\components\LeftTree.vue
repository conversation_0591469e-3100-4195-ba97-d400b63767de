<script setup lang="ts">
import { nextTick, onMounted, onUnmounted, ref, unref, watch } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { getTreeApi, getFullTreeApi, selectAngleApi } from '@/apis/path/domain';
import { ElTree } from 'element-plus';
import { useRoute, useRouter } from 'vue-router';
import { Tree } from '@/utils/type';
import { TreeType } from '@/utils/constant';
import { Event } from '@/types/event';
import { emitter } from '@/utils/emitter';

const router = useRouter();
const route = useRoute();

const treeRef = ref<InstanceType<typeof ElTree>>();
const saveNode = ref();
const treeSearchKey = ref(''); // tree搜索的key
const defaultExpandIdList = ref<string[]>([]); // 这里存放要默认展开的节点id列表
const currentNode = ref<{
  areaCode: string;
  label: string;
}>({
  areaCode: '',
  label: ''
}); // 初始化节点
const treeTypeFlag = ref(0); // 1: 编辑权限 | 2: 查看权限 | 3: 全量树
const treeData = ref<Tree[]>([]);

// 存储动态生成的 popover 引用
const popoverRefs = ref<Record<string, any>>({});
// 设置 popover 引用
const setPopoverRef = (id: string, el: any) => {
  if (el) {
    popoverRefs.value[id] = el;
  } else {
    // 如果弹窗被销毁，移除对应的引用
    delete popoverRefs.value[id];
  }
};
// 关闭弹窗
const closePopover = (id: string) => {
  if (popoverRefs.value[id]) {
    popoverRefs.value[id].hide();
  }
};
// 获得树
const GetTree = () => {
  treeData.value = [];
  treeTypeFlag.value = 0;
  treeSearchKey.value = '';
  if (route.path.startsWith('/domain/maintain'))
    treeTypeFlag.value = TreeType.edit; // edit
  else if (route.path.startsWith('/klg/maintain'))
    treeTypeFlag.value = TreeType.show; // show
  else if (route.path.startsWith('/domain/tag')) treeTypeFlag.value = TreeType.all;
  else if (route.path.startsWith('/audit/pool')) treeTypeFlag.value = TreeType.audit; // AUDIT

  const getTreeFunc =
    treeTypeFlag.value == TreeType.all
      ? getFullTreeApi()
      : getTreeApi(treeTypeFlag.value === 2 ? 2 : 1);
  getTreeFunc.then((res) => {
    if (res.data.tree) {
      treeData.value = [transformToTreeNode(res.data.tree[0])];
      setDefaultNode();
      restoreTreeState();
    }
  });
};
// 设置默认节点
const setDefaultNode = () => {
  const defaultNode = {
    areaCode: treeData.value[0].areaCode,
    label: treeData.value[0].label
  };
  sessionStorage.setItem('defaultNode', JSON.stringify(defaultNode));
};

// 从sessionStorage恢复树的展开状态
const restoreTreeState = () => {
  initData();
  const savedKeys = sessionStorage.getItem('expandedKeys');
  const curNode = sessionStorage.getItem('currentNode');
  const defaultNode = sessionStorage.getItem('defaultNode');
  const filterNode = sessionStorage.getItem('filterNode');
  if (filterNode && treeRef.value) {
    treeSearchKey.value = JSON.parse(filterNode);
  }
  if (savedKeys && JSON.parse(savedKeys).length > 0 && treeRef.value) {
    defaultExpandIdList.value = JSON.parse(savedKeys);
  } else {
    if (defaultExpandIdList.value.length === 0) {
      defaultExpandIdList.value.push(treeData.value[0].areaCode);
      sessionStorage.setItem('expandedKeys', JSON.stringify([treeData.value[0].areaCode]));
    }
  }
  if (route.query.areaCode && route.query.label) {
    currentNode.value.areaCode = route.query.areaCode.toString();
    currentNode.value.label = route.query.label.toString();
  } else {
    if (curNode && treeRef.value) {
      currentNode.value = JSON.parse(curNode);
    } else {
      if (defaultNode) {
        currentNode.value = JSON.parse(defaultNode);
      }
      router.replace({
        query: {
          ...route.query,
          areaCode: treeData.value[0].areaCode,
          label: treeData.value[0].label
        }
      });
    }
  }
  nextTick(() => {
    treeRef.value?.setCurrentKey(currentNode.value.areaCode, true);
    const node = treeRef.value?.getCurrentNode() as Tree;
    if (node) {
      handleNodeClick(node);
    } else {
      handleNodeClick(treeData.value[0]);
    }
  });
};

// 打开节点存key
const handleNodeExpand = (data: Tree) => {
  // 保存当前展开的节点
  let treeTypeFlag = false;
  defaultExpandIdList.value.some((item) => {
    if (item === data.areaCode) {
      // 判断当前节点是否存在， 存在不做处理
      treeTypeFlag = true;
      return true;
    }
  });
  if (!treeTypeFlag && data.children?.length !== 0) {
    // 不存在则存到数组里
    defaultExpandIdList.value.push(data.areaCode);
  }
};
// 树节点关闭去除key
const handleNodeCollapse = (data: Tree) => {
  // 删除当前关闭的节点
  defaultExpandIdList.value.some((item, i) => {
    if (item === data.areaCode) {
      defaultExpandIdList.value.splice(i, 1);
    }
  });
  removeChildrenIds(data); // 递归删除父节点下的所有子节点
};
// 删除树子节点key
const removeChildrenIds = (data: Tree) => {
  // console.log('=== execute removeChildrenIds===')
  if (data.children) {
    data.children.forEach(function (item) {
      const index = defaultExpandIdList.value.indexOf(item.areaCode);
      if (index > 0) {
        defaultExpandIdList.value.splice(index, 1);
      }
      removeChildrenIds(item);
    });
  }
};

// 保存节点状态
const saveTreeState = () => {
  sessionStorage.setItem('expandedKeys', JSON.stringify(defaultExpandIdList.value));
  if (saveNode.value) {
    sessionStorage.setItem('currentNode', JSON.stringify(saveNode.value));
  }
};

// 转换数据格式
const transformToTreeNode = (data: any): Tree => {
  const activateAngle = data.angleList?.find((item) => item.activate === 1);
  const activateId = activateAngle ? activateAngle.oid : data.areaList[0]?.angleId;
  const node: Tree = {
    areaCode: data.areaCode,
    label: data.title,
    isTag: data.isTag,
    isEditKlg: data.areaData1,
    isEditArea: data.areaEdit1,
    angleId: data.angleId ? data.angleId : 0,
    activate: data.angleList?.length > 0 ? activateId : 0,
    angleList: data.angleList
      ? data.angleList.map((item) => {
          return {
            ...item,
            isExpanded: false,
            isOver: false
          };
        })
      : null,
    childrenList: data.areaList ? data.areaList.map(transformToTreeNode) : undefined
  };
  return filterChildren(node);
};

// 筛选所展示的节点
const filterChildren = (node: any) => {
  node.children = node.childrenList;

  // if (treeTypeFlag.value === TreeType.all) {
  //   node.children = node.childrenList;
  //   console.log('=== 全量树模式，直接使用 childrenList ===');
  // } else {
  //   node.children =
  //     node.activate === 0
  //       ? node.childrenList
  //       : node.childrenList?.filter((item) => item.angleId === node.activate);
  //   console.log('=== 筛选模式，筛选后的 children ===', node.children);
  // }
  return node;
};

// 处理改变角度
const handleChangeAngle = (data: any, angle: any) => {
  data.activate = angle.oid;
  closePopover(data.areaCode);
  filterChildren(data);
};
// 处理改变角度
const handleExpand = (angle: any) => {
  angle.isExpanded = !angle.isExpanded;
};

// 筛选
const filterTreeNode = (value: string, data: Tree) => {
  if (!value) {
    return true;
  }
  return data.label.includes(value);
};

// 初始化数据
const initData = () => {
  if (route.query.filterKey) {
    treeSearchKey.value = route.query.filterKey.toString();
  } else {
    treeSearchKey.value = '';
  }
  if (route.query.areaCode && route.query.label) {
    // 如果路由有areaCode，则使用路由的areaCode并且设置sessionStorage
    const curNode = {
      areaCode: route.query.areaCode,
      label: route.query.label
    };
    sessionStorage.setItem('currentNode', JSON.stringify(curNode));
  } else {
    // 路由没有则用sesstionStorage的
    const curNode = sessionStorage.getItem('currentNode');
    if (curNode) {
      const node = JSON.parse(curNode);
      router.replace({
        query: {
          ...route.query,
          areaCode: node.areaCode,
          label: node.label
        }
      });
    }
  }
};

// 处理节点点击
const handleNodeClick = (data: Tree) => {
  if (treeRef.value) {
    const currentNode = treeRef.value.getCurrentNode();
    // 存值去除children
    saveNode.value = {
      areaCode: currentNode.areaCode,
      label: currentNode.label
    };
  }
  data.parentNode = getParentNodeInfo(data.areaCode);
  emitter.emit(Event.CHECK_TREE_NODE, data);
  router.replace({
    query: {
      ...route.query,
      areaCode: data.areaCode,
      label: data.label
    }
  });
};

// 处理输入框过滤为空
const handleTextEmpty = () => {
  if (treeSearchKey.value === '') {
    // 确保treeRef已经被设置，并且有store和nodesMap
    if (treeRef.value && treeRef.value.store && treeRef.value.store.nodesMap) {
      const nodes = treeRef.value.store.nodesMap;
      for (const key in nodes) {
        if (nodes[key]) {
          nodes[key].expanded = false;
        }
      }
    }
  }
};
// 处理添加klg
const handleAddKlg = (data: any) => {
  sessionStorage.setItem('step', '0');
  sessionStorage.setItem('area', data.areaCode);
  window.open(`/editklg`, `_self`);
};

// 从树中获得当前节点的父节点的权限
const getParentNodeInfo = (areaCode: string) => {
  const node = treeRef.value?.getNode(areaCode).parent;
  let result = {
    parentAreaCode: '',
    isEditKlg: false,
    isEditArea: false
  };
  if (node) {
    result.parentAreaCode = node.data.areaCode;
    result.isEditKlg = node.data.isEditKlg;
    result.isEditArea = node.data.isEditArea;
  }
  return result;
  // const checkNode = (node: Tree) => {
  //   node.children?.forEach((n) => {
  //     if (n.areaCode === areaCode) {
  //       result.parentAreaCode = node.areaCode
  //       result.isEditArea = node.isEditArea ? node.isEditArea : false
  //       result.isEditKlg = node.isEditKlg ? node.isEditKlg : false
  //     }
  //   })
  //   node.children?.forEach((n) => {
  //     checkNode(n)
  //   })
  // }
  // checkNode(treeData.value[0])
};

//
const handleEmitNodeClick = (areaCode: string) => {
  if (areaCode) {
    nextTick(() => {
      treeRef.value?.setCurrentKey(areaCode, true);
      const node = treeRef.value?.getCurrentNode() as Tree;
      if (node) {
        handleNodeClick(node);
      } else {
        handleNodeClick(treeData.value[0]);
      }
    });
  }
};
// 处理筛选树角度
const handleTreeFilter = (data: any) => {
  const { areaCode, angleId } = data;
  const node = treeRef.value?.getNode(areaCode);
  if (node) {
    node.data.activate = angleId;
    node.data = filterChildren(node.data);
  }
};
// 处理排序
const handleOrder = (data: any) => {
  const { areaCode, angleId, sortAreaList } = data;
  const node = treeRef.value?.getNode(areaCode);
  if (node) {
    const list: any[] = [];
    const dataList: any[] = [];
    sortAreaList.forEach((item) => {
      list.push(node.childNodes.find((node) => node.data.areaCode === item.areaCode));
      dataList.push(node.data.children.find((node) => node.areaCode === item.areaCode));
    });
    node.childNodes = list;
    node.data.children = dataList;
  }
};
// 处理渲染
const handleRenderLi = (data: any) => {
  nextTick(() => {
    const elements = document.querySelectorAll('.inline-text');
    elements.forEach((element) => {
      if (element) {
        // 获取元素的宽度
        const width = element.clientWidth;
        const angle = data.angleList.find((item) => item.angleTitle === element.textContent);
        // 判断宽度是否大于 150px
        if (width > 270) {
          angle.isOver = true;
        } else {
        }
      }
    });
  });
};
// 处理渲染
const handleHideLi = (data: any) => {
  data.angleList.forEach((item) => (item.isExpanded = false));
};

onMounted(() => {
  emitter.on(Event.TREE_REFRESH, GetTree);
  emitter.on(Event.EMIT_NODE_CLICK, handleEmitNodeClick);
  // emitter.on(Event.TREE_FILTER, handleTreeFilter);
  emitter.on(Event.TREE_ORDER, handleOrder);
});
onUnmounted(() => {
  emitter.off(Event.TREE_REFRESH, GetTree);
  emitter.off(Event.EMIT_NODE_CLICK, handleEmitNodeClick);
  // emitter.off(Event.TREE_FILTER, handleTreeFilter);
  emitter.off(Event.TREE_ORDER, handleOrder);
});
// 搜索树
watch(treeSearchKey, (val) => {
  sessionStorage.setItem('filterNode', JSON.stringify(val));
  router.replace({
    query: {
      ...route.query,
      filterKey: val === '' ? undefined : val
    }
  });
  if (val === '') {
    handleTextEmpty();
    restoreTreeState();
  }
  treeRef.value!.filter(val);
});

// 保存树
window.addEventListener('beforeunload', () => {
  saveTreeState();
});

// 更新树的curNode高亮
watch(
  () => route.query.areaCode,
  (oldval, newval) => {
    if (route.query.areaCode && route.query.label) {
      const curNode = {
        areaCode: route.query.areaCode,
        label: route.query.label
      };
      sessionStorage.setItem('currentNode', JSON.stringify(curNode));
      treeRef.value?.setCurrentKey(curNode.areaCode?.toString());
    } else {
      const defaultNode = sessionStorage.getItem('defaultNode');
      if (defaultNode) {
        currentNode.value = JSON.parse(defaultNode);
        router.replace({
          query: {
            ...route.query,
            areaCode: currentNode.value.areaCode,
            label: currentNode.value.label
          }
        });
        sessionStorage.setItem('currentNode', defaultNode);
        treeRef.value?.setCurrentKey(currentNode.value.areaCode?.toString());
      }
    }
  },
  { deep: true, immediate: true }
);
</script>

<template>
  <div class="left-wrapper">
    <el-input class="input" v-model="treeSearchKey" placeholder="请输入查询内容" clearable>
      <template #suffix v-if="treeSearchKey">
        <el-icon class="search-icon" style="cursor: pointer"><Search /></el-icon>
      </template>
    </el-input>
    <div class="block" style="width: 100%">
      <el-tree
        ref="treeRef"
        class="tree"
        empty-text="暂无数据"
        :data="treeData"
        :default-expanded-keys="defaultExpandIdList"
        node-key="areaCode"
        @node-click="handleNodeClick"
        :filter-node-method="filterTreeNode"
        :auto-expand-parent="true"
        :highlight-current="true"
        :expand-on-click-node="false"
        :current-node-key="currentNode.areaCode"
        @node-expand="handleNodeExpand"
        @node-collapse="handleNodeCollapse"
      >
        <template #default="{ node, data }">
          <span class="tag-icon">
            <img v-if="data.isTag === 1 && treeTypeFlag < 2" src="@/assets/image/domain/u199.svg" />
          </span>
          <span class="tree-node">
            <span :class="{ uneditable: !data.isEditKlg }">{{ node.label }}</span>
          </span>
          <span class="tree-btn-group">
            <el-popover
              :ref="(el) => setPopoverRef(data.areaCode, el)"
              placement="right"
              :width="320"
              trigger="click"
              @click.stop
              @before-enter="handleRenderLi(data)"
              @before-hide="handleHideLi(data)"
              popper-class="angle-btn"
              :offset="8"
              v-if="treeTypeFlag > TreeType.edit && data.angleList && data.angleList.length > 0"
            >
              <template #reference>
                <span @click.stop>
                  <el-icon
                    v-if="data.isEditArea"
                    class="tree-filter-btn"
                    color="#FFFFFF"
                    :size="10"
                  >
                    <Search />
                  </el-icon>
                </span>
              </template>
              <span class="ul-block" v-for="angle in data.angleList" :key="angle.oid">
                <span
                  class="li-btn"
                  :class="data.activate === angle.oid ? 'selected-li' : ''"
                  @click="handleChangeAngle(data, angle)"
                >
                  <span class="li-text" style="display: flex; flex-direction: row">
                    <span
                      class="inline-text"
                      :class="{
                        'expand-text': angle.isExpanded,
                        'not-expand-text': !angle.isExpanded && angle.isOver
                      }"
                    >
                      {{ angle.angleTitle }}
                    </span>
                    <span
                      v-if="!angle.isExpanded && angle.isOver"
                      style="white-space: nowrap"
                      @click.stop="handleExpand(angle)"
                      >...</span
                    >
                    <!-- <span
                      v-if="angle.isExpanded"
                      @click.stop="handleExpand(angle)"
                      class="close-btn"
                      >收起
                    </span> -->
                  </span>
                  <el-icon v-if="data.activate === angle.oid" :size="20"><Check /></el-icon>
                </span>
              </span>
            </el-popover>
            <span v-if="treeTypeFlag === 2 && data.isEditArea">
              <el-icon
                v-if="data.isEditArea"
                @click.stop="handleAddKlg(data)"
                class="tree-add-btn"
                color="#FFFFFF"
                :size="10"
              >
                <Plus />
              </el-icon>
            </span>
          </span>
        </template>
      </el-tree>
    </div>
  </div>
</template>
<style scoped>
:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--color-primary) inset;
}
:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
  min-width: 280px;
}
.left-wrapper {
  width: 300px;
  padding: 10px 10px;
  height: 100%;
  background-color: var(--color-second);

  .input {
    width: 100%;
  }
  .block {
    /* display: flex; */
    width: 100%;
    height: calc(100% - 25px);
    overflow: auto;
    border: none;

    &::-webkit-scrollbar {
      display: none;
    }

    .tree {
      /* height: 250px; */
      border: none;
      display: flex;
      background-color: var(--color-theme-hover);
      .tag-icon {
        width: 20px;
      }
      .tree-node {
        background-color: white;
        padding: 2px 10px;
        .uneditable {
          color: var(--color-boxborder);
          cursor: disabled;
        }
      }
      .tree-btn-group {
        display: flex;
        align-items: center;
        .tree-filter-btn {
          width: 15px;
          height: 15px;
          margin-left: 7px;
          cursor: pointer;
          padding: 3px;
          background-color: var(--color-primary-objection);
          border-radius: 50%;
        }
        .tree-add-btn {
          width: 15px;
          height: 15px;
          margin-left: 7px;
          cursor: pointer;
          padding: 3px;
          background-color: var(--color-primary);
          border-radius: 50%;
        }
      }
    }
  }
  .block {
    &::-webkit-scrollbar {
      display: block;
      height: 5px;
      width: 5px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #888;
      border-radius: 6px;
      width: 5px;
      height: 5px;
    }

    &::-webkit-scrollbar-track {
      background-color: var(--color-theme-hover);
    }
  }
}
:deep(.el-popover .el-popper) {
  padding: 0;
}
</style>
<style>
.el-popper.is-light.angle-btn > .el-popper__arrow:before {
  background-color: var(--color-primary-objection);
}
.el-popover.angle-btn.el-popper {
  background-color: var(--color-primary-objection);
  padding: 0;
}
.angle-btn {
  padding: 0;
  font-family: var(--text-family);
  .ul-block {
    display: flex;
    flex-direction: column;
    /* background-color: blue; */
    .li-btn {
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      padding: 3px 10px;
      border: 1px solid var(--color-primary-objection);
      border-radius: 4px;
      background-color: white;
      &:hover {
        background-color: var(--color-primary-objection);
        color: white;
      }
      .li-text {
        max-width: 270px;
        .inline-text {
          white-space: nowrap;
        }
        .not-expand-text {
          white-space: nowrap;
          overflow: hidden;
        }
        .expand-text {
          white-space: pre-wrap;
        }
        .close-btn {
          color: var(--color-primary);
          white-space: nowrap;
          align-self: center;
          margin-left: 5px;
        }
      }
    }
    .selected-li {
      background-color: var(--color-primary-objection);
      color: white;
    }
  }
}
</style>
