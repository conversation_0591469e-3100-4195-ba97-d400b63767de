import { buildRegSubString, findAllOccurrences } from './regUtils';
import { QuestionAction, type RenderInfo } from '@/types/word';

export function handleDraftQuestion(
  question: any,
  regString: string,
  renderInfoIndexes: number[],
  renderInfoList: RenderInfo[],
  action: QuestionAction = QuestionAction.add
) {
  const regSubString = buildRegSubString(question.associatedWords);
  // console.log('regSubString', regSubString);
  if (regSubString.length == 0) {
    return;
  }
  const positions = findAllOccurrences(regString, regSubString);
  for (const position of positions) {
    handle(
      position,
      regSubString.length,
      question.questionId,
      renderInfoIndexes,
      renderInfoList,
      action
    );
  }
}

function handle(
  position: number,
  length: number,
  qid: number,
  renderInfoIndexes: number[],
  renderInfoList: RenderInfo[],
  action: QuestionAction
) {
  for (let i = position; i < position + length; i++) {
    const index = renderInfoIndexes[i];
    const renderInfo = renderInfoList[index];
    if (action == QuestionAction.add) {
      if (!renderInfo.questionCountMap[qid]) {
        renderInfo.questionCountMap[qid] = 0;
      }
      renderInfo.questionCountMap[qid]++;
    } else {
      delete renderInfo.questionCountMap[qid];
      renderInfo.questionCountMap[qid]--;
    }
  }
}

export function handleVideoQuestion(
  question: any,
  regString: string,
  renderInfoIndexes: Array<{
    listIndex: number;
    index: number;
  }>,
  renderInfoListList: RenderInfo[][],
  action: QuestionAction = QuestionAction.add
) {
  const regSubString = buildRegSubString(question.associatedWords);
  // console.log('regSubString', regSubString);
  if (regSubString.length == 0) {
    return;
  }
  const positions = findAllOccurrences(regString, regSubString);
  for (const position of positions) {
    handle2(
      position,
      regSubString.length,
      question.questionId,
      renderInfoIndexes,
      renderInfoListList,
      action
    );
  }
}

function handle2(
  position: number,
  length: number,
  qid: number,
  renderInfoIndexes: Array<{
    listIndex: number;
    index: number;
  }>,
  renderInfoListList: RenderInfo[][],
  action: QuestionAction
) {
  for (let i = position; i < position + length; i++) {
    const { listIndex, index } = renderInfoIndexes[i];
    const renderInfo = renderInfoListList[listIndex][index];
    if (action == QuestionAction.add) {
      if (!renderInfo.questionCountMap[qid]) {
        renderInfo.questionCountMap[qid] = 0;
      }
      renderInfo.questionCountMap[qid]++;
    } else {
      delete renderInfo.questionCountMap[qid];
      renderInfo.questionCountMap[qid]--;
    }
  }
}
