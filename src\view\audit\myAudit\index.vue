<script setup lang="ts">
import FormSwitch from '@/components/FormSwitch.vue';
import CmpButton from '@/components/CmpButton.vue';
import MyFlipper from '@/components/MyFlipper.vue';
import AuditDialog from '@/view/audit/components/AuditDialog.vue';

import { onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { KlgTypeDict, MAX_PAGESIZE } from '@/utils/constant';
import router from '@/router';
import { getMyAuditKlgListApi } from '@/apis/path/audit';
import type { params2GetMyAuditList } from '@/apis/path/audit';
import { findKeyByValue } from '@/utils/func';
import { ElMessage } from 'element-plus';
const route = useRoute();
const currentPage = ref(1);
const pageSize = MAX_PAGESIZE;
const total = ref(0);
const dialogRef = ref();

const selectionKlgList = ref<any[]>([]);
const tableData = ref();
const curDomain = ref({
  areaCode: '',
  title: '',
  type: null,
  keyword: ''
});
// 计算列表index
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize + index + 1;
};
// 处理表格选择
const handleSelectionChange = (list: []) => {
  selectionKlgList.value = list;
};
// 处理换页
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  getMyAuditKlgList(newPage);
};
// 获取待审核列表
const getMyAuditKlgList = (page?: number) => {
  const params: params2GetMyAuditList = {
    current: page ? page : currentPage.value,
    limit: pageSize,
    type: curDomain.value.type,
    keyword: curDomain.value.keyword
  };
  getMyAuditKlgListApi(params).then((res) => {
    if (res.success) {
      tableData.value = res.data.list;
      total.value = res.data.total;
    }
  });
};
// 处理搜索
const handleSearch = (data: any) => {
  curDomain.value.type = data.klgType !== 0 ? data.klgType : null;
  curDomain.value.keyword = data.keyword;
  getMyAuditKlgList();
};
// 处理dialog
const handleDialog = (row?: any) => {
  if (row) {
    selectionKlgList.value.push(row);
  }
  if (selectionKlgList.value.length !== 0) {
    dialogRef.value.showDialog(1, selectionKlgList.value);
  } else {
    ElMessage.warning('请至少选择一条知识！');
  }
};
// 处理提交
const handleSubmitAudit = (flag: boolean) => {
  if (flag) {
    getMyAuditKlgList();
  }
};
// 去审核页
const routerPush = (row: any) => {
  window.open(`/auditklg?klgCode=${row.klgCode}`, '_blank');
};

onMounted(() => {
  getMyAuditKlgList();
  document.addEventListener('visibilitychange', () => {
    if (!document['hidden']) {
      getMyAuditKlgList();
      // console.log('出现');
    } else {
      //隐藏
      // console.log('隐藏');
    }
  });
});
</script>
<template>
  <div class="wrapper">
    <div class="header">
      <span class="header-title">{{ curDomain.title }}</span>
      <FormSwitch :need-audit-type="true" :need-audit-klg="true" @audit="handleSearch"></FormSwitch>
    </div>
    <div class="line"></div>
    <div class="main-container">
      <div class="toolbar">
        <CmpButton type="primary" @click="handleDialog()">批量换人</CmpButton>
      </div>
      <div class="line"></div>
      <el-table
        class="table"
        :data="tableData"
        style="width: 100%; overflow-x: hidden"
        empty-text="暂无数据"
        :row-style="{ height: '55px', overflow: 'hidden' }"
        @selection-change="handleSelectionChange"
        :cell-style="{
          height: '55px',
          overflow: 'hidden'
        }"
      >
        <el-table-column type="selection" width="30" />
        <el-table-column type="index" :index="indexMethod" label="序号" width="60" align="center" />
        <el-table-column label="知识名称" min-width="350" height="55">
          <template #default="scope">
            <el-tooltip
              placement="top"
              :content="scope.row.title"
              :raw-content="true"
              :show-after="200"
              effect="customized"
            >
              <span class="ck-content ellipsis-text-inline" v-html="scope.row.title"></span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="知识类型" width="100" align="center">
          <template #default="scope">
            {{ findKeyByValue(scope.row.sortId, KlgTypeDict) }}
          </template>
        </el-table-column>
        <el-table-column label="作者" min-width="120" align="center">
          <template #default="scope">
            <el-tooltip
              placement="top"
              :content="scope.row.whoName"
              :raw-content="true"
              :show-after="200"
              effect="customized"
            >
              <p class="ck-content ellipsis-text-inline" v-html="scope.row.whoName"></p>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="提交日期" width="200" align="center" />
        <el-table-column prop="currActionName" label="审核环节" width="100" align="center" />
        <el-table-column label="操作" width="200" align="center">
          <template #default="scope">
            <span class="operationBlock">
              <el-button type="primary" @click="routerPush(scope.row)" text class="operationBtn">
                审核
              </el-button>
              <el-button type="primary" @click="handleDialog(scope.row)" text class="operationBtn">
                换人审核
              </el-button>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <my-flipper
        @change-page="handleChangePage"
        :current="currentPage"
        :page-size="pageSize"
        :total="total"
      ></my-flipper>
    </div>
  </div>
  <!-- other -->
  <AuditDialog ref="dialogRef" @submit="handleSubmitAudit"></AuditDialog>
</template>
<style scoped>
:deep(p) {
  margin: 0;
}

.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
.wrapper {
  width: 1200px;
  min-height: 750px;
  background-color: white;
  .header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .header-title {
      display: flex;
      align-items: center;
      margin-left: 20px;
    }
  }

  .main-container {
    padding: 0 20px;
    .table {
      --el-color-primary: var(--color-primary);
      :deep(.cell) {
        padding: 0 6px;
        line-height: 55px;
        max-height: 55px;
      }
      :deep(p) {
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .operationBlock {
        .operationBtn {
          font-family: var(--text-family);
          font-weight: 400;
          &:hover {
            font-weight: 600;
          }
        }
      }
    }
    .toolbar {
      margin: 10px 0;
    }
  }
}
</style>
