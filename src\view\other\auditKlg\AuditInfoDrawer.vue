<script setup lang="ts">
import { auditTypeDict } from "@/utils/constant"
import { findKeyByValue } from "@/utils/func"
import { ref } from "vue"

const drawerVisible = ref(false)
const curData = ref()
// 展示drawer
const showDrawer = (data: any) => {
  drawerVisible.value = true
  curData.value = data
}
defineExpose({
  showDrawer,
})
</script>
<template>
  <el-drawer v-model="drawerVisible" direction="rtl">
    <template #header> <span class="header-text">审核意见</span> </template>
    <div class="line"></div>
    <div class="main-container">
      <el-form>
        <el-form-item label="审核时间:">
          {{ curData.createTime }}
        </el-form-item>
        <el-form-item label="审核环节:">
          {{ curData.actionName }}
        </el-form-item>
        <el-form-item label="审核人:">
          {{ curData.processorName ? curData.processorName : "-" }}
        </el-form-item>
        <el-form-item label="审核结果:">
          {{
            findKeyByValue(curData.auditResult, auditTypeDict)
              ? findKeyByValue(curData.auditResult, auditTypeDict)
              : "-"
          }}
        </el-form-item>
        <el-form-item label="审核意见:">
          <span v-if="!curData.auditOpinion"> - </span>
        </el-form-item>
        <el-form-item>
          <div v-if="curData.auditOpinion" class="opinion">
            {{ curData.auditOpinion }}
          </div>
        </el-form-item>
      </el-form>
    </div>
  </el-drawer>
</template>
<style scoped>
:deep(.el-form-item) {
  margin: 0;
}
:deep(.el-form-item__label) {
  color: var(--color-black);
  display: flex;
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
.header-text {
  color: var(--color-black);
  font-weight: 600;
}
.main-container {
  .opinion {
    flex: 1;
    padding: 0 10px;
    width: 100%;
    border: 1px solid var(--color-boxborder);
    border-radius: 3px;
    background-color: var(--color-light);
  }
}
</style>
