<script setup lang="ts">
import MyFlipper from "@/components/MyFlipper.vue"
import CmpButton from "@/components/CmpButton.vue"
import PermissionDialog from "@/view/permission/components/PermissionDialog.vue"
import { getRoleListApi } from "@/apis/path/permission"
import { onMounted, ref } from "vue"
import { MAX_PAGESIZE } from "@/utils/constant"
import router from "@/router"

const currentPage = ref(1) // 当前页
const pageSize = MAX_PAGESIZE // 页大小
const tableData = ref([]) // 表格数据
const total = ref(0) // 总数

const dialogRef = ref()

// 获取角色列表
const getRoleList = (current: number) => {
  let params = {
    current: current ? current : currentPage.value,
    limit: pageSize,
  }
  getRoleListApi(params).then((res) => {
    if (res.success) {
      tableData.value = res.data.list
      total.value = res.data.total
    }
  })
}

// 计算列表index
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize + index + 1
}

// 处理换页
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage
  getRoleList(newPage)
}
// 打开dialog
const handleOpenDialog = (mode: number, data?: any) => {
  if (data) {
    dialogRef.value.showDialog(mode, data)
  } else {
    dialogRef.value.showDialog(mode)
  }
}
// 跳转
const routerPush = (mode: number, role: any) => {
  if (mode === 1) {
    router.push({
      path: "/editrole",
      query: {
        role: role.id,
      }
    })
  } else if (mode === 2) {
    router.push({
      path: "/editdomain",
      query: {
        id: role.id,
        title: role.title,
      }
    })
  }
}

onMounted(() => {
  getRoleList(currentPage.value)
})
</script>
<template>
  <div class="wrapper">
    <div class="header-wrapper">
      <div class="toolbar">
        <CmpButton type="primary" @click="handleOpenDialog(1)"
          >新增角色</CmpButton
        >
      </div>
    </div>
    <div class="main-wrapper">
      <div class="line"></div>
      <el-table
        class="table"
        :data="tableData"
        style="width: 100%"
        empty-text="暂无数据"
        :row-style="{ height: '50px', overflow: 'hidden' }"
      >
        <el-table-column
          type="index"
          :index="indexMethod"
          label="序号"
          width="160"
          align="center"
        />
        <el-table-column prop="title" label="角色名称" width="300">
          <template #default="scope">
            <span>{{ scope.row.title }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="count" label="成员数" width="300" align="center">
          <template #default="scope">
            <span>{{ scope.row.count !== 0 ? scope.row.count : "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="400" align="center">
          <template #default="scope">
            <span class="operation">
              <el-button
                class="op-btn"
                type="primary"
                @click="handleOpenDialog(2, scope.row)"
                text
              >
                修改
              </el-button>
              <el-button
                class="op-btn"
                type="primary"
                @click="routerPush(1, scope.row)"
                text
              >
                配置页面
              </el-button>
              <el-button
                class="op-btn"
                type="primary"
                @click="routerPush(2, scope.row)"
                text
              >
                配置领域
              </el-button>
              <el-button
                class="op-btn"
                type="primary"
                @click="handleOpenDialog(3, scope.row)"
                text
              >
                删除
              </el-button>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <my-flipper
        @change-page="handleChangePage"
        :current="currentPage"
        :page-size="pageSize"
        :total="total"
      ></my-flipper>
    </div>
  </div>
  <!-- other -->
  <permission-dialog ref="dialogRef" @refresh="getRoleList"></permission-dialog>
</template>
<style scoped>
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
.wrapper {
  width: 1200px;
  min-height: 750px;
  background-color: white;
  font-family: var(--text-family);
  overflow-x: hidden;

  .header-wrapper {
    width: 100%;
    .toolbar {
      margin: 20px 0;
      padding: 0 20px;
    }
  }
  .main-wrapper {
    padding: 0 20px;

    .table {
      --el-color-primary: var(--color-primary);

      .operation {
        padding: 0 10px;
        display: flex;

        .op-btn {
          cursor: pointer;
          color: var(--color-primary);
          font-family: var(--text-family);
          font-weight: 400;
          &:hover {
            font-weight: bold;
          }
        }
      }
    }
  }
}
:deep(.el-button--primary.is-text.is-disabled) {
  color: var(--color-boxborder);
}
</style>
