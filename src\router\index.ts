import { createRouter, createWebHistory } from 'vue-router';
import { docCookies } from '@/utils/cookieop';
import { getUserInfoApi } from '@/apis/path/user';
import { ref } from 'vue';
import { ElMessageBox } from 'element-plus';
import { userInfoStore } from '@/stores/userInfo';

const caninblack = ref(false); // can in blackpearl 能否进入blackpearl的权限
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/welcome',
      component: () => import('@/layout/index.vue'),
      children: [
        {
          path: '/welcome',
          name: 'welcome',
          component: () => import('@/components/HelloWorld.vue')
        },
        {
          path: '/rule',
          name: 'rule',
          component: () => import('@/view/other/editKlg/rule.vue')
        },
        {
          path: '/editklg',
          name: 'editklg',
          component: () => import('@/view/other/editKlg/index.vue')
        },
        {
          path: '/linkklg',
          name: 'linkklg',
          component: () => import('@/view/other/linkKlg/index.vue')
        },
        {
          path: '/auditklg',
          name: 'auditklg',
          component: () => import('@/view/other/auditKlg/index.vue')
        },
        {
          path: '/klgdetail',
          name: 'klgdetail',
          component: () => import('@/view/other/klgDetail/index.vue')
        },
        {
          path: '/editrole',
          name: 'editrole',
          component: () => import('@/view/other/editRole/index.vue'),
          meta: {
            backendname: 'roleManagement',
            requiresAuth: true
          }
        },
        {
          path: '/editdomain',
          name: 'editdomain',
          component: () => import('@/view/other/editDomain/index.vue'),
          meta: {
            backendname: 'roleManagement',
            requiresAuth: true
          }
        },
        {
          path: '/domain',
          component: () => import('@/view/domain/index.vue'),
          meta: {
            requiresAuth: true
          },
          children: [
            {
              path: '/domain/tag',
              name: 'tagMaintain',
              component: () => import('@/view/domain/tagMaintain/index.vue'),
              meta: {
                backendname: 'tagMaintain',
                requiresAuth: true
              }
            },
            {
              path: '/domain/maintain',
              name: 'domainMaintain',
              component: () => import('@/view/domain/domainMaintain/index.vue'),
              meta: {
                backendname: 'domainMaintain',
                requiresAuth: true
              }
            }
          ]
        },
        {
          path: '/task',
          name: 'task',
          component: () => import('@/view/task/index.vue'),
          meta: {
            requiresAuth: true
          },
          children: [
            {
              path: '/task/list',
              name: 'taskList',
              component: () => import('@/view/task/taskList/index.vue'),
              meta: {
                backendname: 'taskList',
                requiresAuth: true
              }
            },
            {
              path: '/task/mytask',
              name: 'myTask',
              component: () => import('@/view/task/myTask/index.vue'),
              meta: {
                backendname: 'myTask',
                requiresAuth: true
              }
            }
          ]
        },
        {
          path: '/klg',
          name: 'klg',
          component: () => import('@/view/klg/index.vue'),
          meta: {
            requiresAuth: true
          },
          children: [
            {
              path: '/klg/maintain',
              name: 'klgMaintain',
              component: () => import('@/view/klg/klgMaintain/index.vue'),
              meta: {
                backendname: 'klgMaintain',
                requiresAuth: true
              }
            },
            {
              path: '/klg/recycle',
              name: 'recycle',
              component: () => import('@/view/klg/recycle/index.vue'),
              meta: {
                backendname: 'klgRecycle',
                requiresAuth: true
              }
            }
          ]
        },
        {
          path: '/audit',
          name: 'audit',
          component: () => import('@/view/audit/index.vue'),
          meta: {
            requiresAuth: true
          },
          children: [
            {
              path: '/audit/pool',
              name: 'klgAuditPool',
              component: () => import('@/view/audit/klgAudit/index.vue'),
              meta: {
                backendname: 'klgAuditPool',
                requiresAuth: true
              }
            },
            {
              path: '/audit/myaudit',
              name: 'myKlgAudit',
              component: () => import('@/view/audit/myAudit/index.vue'),
              meta: {
                backendname: 'myKlgAudit',
                requiresAuth: true
              }
            }
          ]
        },
        {
          path: '/document',
          name: 'document',
          component: () => import('@/view/document/index.vue'),
          meta: {
            requiresAuth: true
          },
          children: [
            {
              path: '/document/reference',
              name: 'reference',
              component: () => import('@/view/document/reference/index.vue'),
              meta: {
                backendname: 'myTask',
                requiresAuth: true
              }
            }
          ]
        }
        // {
        //   path: "/permission",
        //   name: "permission",
        //   component: () => import("@/view/permission/index.vue"),
        //   meta: {
        //     requiresAuth: true,
        //   },
        //   children: [
        //     {
        //       path: "/permission/user",
        //       name: "user",
        //       component: () => import("@/view/permission/user/index.vue"),
        //       meta: {
        //         backendname: "userManagement",
        //         requiresAuth: true,
        //       },
        //     },
        //     {
        //       path: "/permission/character",
        //       name: "role",
        //       component: () => import("@/view/permission/character/index.vue"),
        //       meta: {
        //         backendname: "roleManagement",
        //         requiresAuth: true,
        //       },
        //     },
        //     {
        //       path: "/permission/audit",
        //       name: "approval",
        //       component: () => import("@/view/permission/audit/index.vue"),
        //       meta: {
        //         backendname: "approvalManagement",
        //         requiresAuth: true,
        //       },
        //     },
        //   ],
        // },
      ]
    },
    {
      path: '/:pathMatch(.*)*', // 匹配所有不存在的路径
      redirect: '/welcome'
    }
  ]
});

router.beforeEach((_to, _from, next) => {
  const userinfo = userInfoStore();
  const userId = userinfo.getUserId();
  if (userId) {
    next();
  } else {
    userinfo
      .getUserInfo()
      .then(() => {
        next();
      })
      .catch((error) => console.error(error));
  }
});
export default router;
