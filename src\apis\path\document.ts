import { http } from "@/apis"
import type { APIResponse } from "@/utils/type"
interface page {
  current: number
  limit: number
}
export interface params2GetRef extends page{
  cntName: string
  refSort: string
  status: number
}
export interface params2AddRef { 
  articleName: string
  author: string
  bookName: string
  fileName: string
  flag: number // 资料类型 0: 著作|| 1: 网络|| 2: 其他
  networkName: string
  pubDate: string // 出版日期
  pubHouse: string // 出版社
  pubPlace: string // 出版地
  pubYear: number // 出版年
  refDesc: string
  url: string
}
export interface params2EditRef extends params2AddRef{
  refId: number
}
export interface params2GetPubRef extends page{
  cntName: string,
  refSort: string,
  status: number,
}

// 获取文献列表
export function getRefListApi(params: params2GetRef): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/ref/getRef/`,
    data: params,
  })
}

// 增加文献
export function addRefApi(params: params2AddRef): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/ref/addRef`,
    data: params,
  })
}
// 编辑文献
export function editRefApi(params: params2EditRef): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/ref/updateRef`,
    data: params,
  })
}

// 发布文献
export function publishRefApi(list: [any]): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/ref/publishRef`,
    data: list,
  })
}

// 撤回文献
export function recallRefApi(list: [any]): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/ref/recallRef`,
    data: list,
  })
}

// 删除文献
export function removeRefApi(id: number): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/ref/removeRef/${id}`,
  })
}

// 获取文献编辑表单
export function getEditRefApi(id: number): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/ref/clickEdit/${id}`,
  })
}
// 获取发布文献
export function getPubRefsApi(params: params2GetPubRef): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/ref/getPubRef`,
    data: params,
  })
}
// 批量上传
export function uploadFileApi(params: any): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/ref/uploadFile`,
    data: params
  })
}