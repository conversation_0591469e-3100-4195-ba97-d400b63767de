<script setup lang="ts">
import {
  KlgTypeDictPlus,
  klgAuditTypeDict,
  searchRange,
  sortByDict,
  sortOrderDict,
  hasPreDict,
} from "@/utils/constant"
import { findKeyByValue } from "@/utils/func"
import { ref } from "vue"

const props = defineProps({
  disabled: Boolean,
})
const blankForm = {
  klgName: "",
  klgType: findKeyByValue(0, KlgTypeDictPlus),
  klgStatus: findKeyByValue(-1, klgAuditTypeDict),
  author: "",
  range: findKeyByValue(1, searchRange),
  sortBy: findKeyByValue(0, sortByDict),
  sortOrder: findKeyByValue(0, sortOrderDict),
  hasPre: findKeyByValue(2, hasPreDict),
}
const form = ref(blankForm)
const emits = defineEmits(["filter"])

// 改变
const change = (form: any) => {
  emits("filter", form)
}
</script>
<template>
  <div class="main-wrapper">
    <div class="line-wrapper">
      <el-input
        v-model="form.klgName"
        placeholder="请输入知识名称"
        @keydown.enter="change(form)"
        suffix-icon="Search"
      ></el-input>
      <el-select
        v-model="form.klgType"
        placeholder="请选择知识类型"
        @change="change(form)"
      >
        <el-option
          v-for="(key, value) in KlgTypeDictPlus"
          :key="key"
          :label="value"
          :value="value"
          class="primary"
        >
        </el-option>
      </el-select>
      <el-select
        v-model="form.klgStatus"
        placeholder="请选择知识状态"
        @change="change(form)"
      >
        <el-option
          v-for="(key, value) in klgAuditTypeDict"
          :key="key"
          :label="value"
          :value="value"
          class="primary"
        />
      </el-select>
      <el-input
        v-model="form.author"
        placeholder="请输入作者"
        :disabled="props.disabled"
        suffix-icon="Search"
        @keydown.enter="change(form)"
      ></el-input>
    </div>
    <div class="line-wrapper">
      <el-select
        v-model="form.range"
        placeholder="请选择查询范围"
        @change="change(form)"
      >
        <el-option
          v-for="(key, value) in searchRange"
          :key="key"
          :label="value"
          :value="value"
          class="primary"
        />
      </el-select>
      <el-select
        v-model="form.sortBy"
        placeholder="请选择排序依据"
        @change="change(form)"
      >
        <el-option
          v-for="(key, value) in sortByDict"
          :key="key"
          :label="value"
          :value="value"
          class="primary"
        />
      </el-select>
      <el-select
        v-model="form.sortOrder"
        placeholder="请选择排序顺序"
        @change="change(form)"
      >
        <el-option
          v-for="(key, value) in sortOrderDict"
          :key="key"
          :label="value"
          :value="value"
          class="primary"
        />
      </el-select>
      <el-select
        v-model="form.hasPre"
        placeholder="是否有前驱"
        @change="change(form)"
      >
        <el-option
          v-for="(key, value) in hasPreDict"
          :key="key"
          :label="value"
          :value="value"
          class="primary"
        />
      </el-select>
    </div>
  </div>
</template>
<style scoped>
.main-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  .line-wrapper {
    width: 98%;
    display: flex;
    flex-direction: row;
    gap: 10px;
    margin-bottom: 5px;
  }
}
</style>
