<template>
  <el-popover
    :visible="floatingVisible"
    :virtual-ref="referenceElement"
    placement="top"
    virtual-triggering
    popper-class="my-content"
  >
    <span class="container">
      <div class="ck-content container-content" v-html="content"></div>
    </span>
  </el-popover>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref, watch } from "vue"

const referenceElement = ref<HTMLElement | null>(null)

const floatingVisible = ref(false)

const model = defineModel<HTMLElement | null>()
const content = ref("")

watch(
  () => model.value,
  (newVal) => {
    if (newVal) {
      content.value = newVal?.getAttribute("thumbnail") as string
      referenceElement.value = newVal
      floatingVisible.value = true
    }
  }
)

// @ts-ignore
const handler = (event: Event) => {
  floatingVisible.value = false
  model.value = null
}
onMounted(() => {
  document.addEventListener("click", handler)
})

onBeforeUnmount(() => {
  document.removeEventListener("click", handler)
})
</script>

<style lang="less" scoped>
// .content {
//   position: relative;
//   width: 150px;
//   //   overflow: hidden;
//   background: var(--el-bg-color-overlay);
//   border: 1px solid var(--el-border-color-light);
//   box-shadow: var(--el-box-shadow-light);
//   border-radius: var(--el-popover-border-radius);
//   &::before {
//     content: '';
//     position: absolute;
//     z-index: -1;
//     top: -15px;
//     left: calc(50% - 15px);
//     width: 30px;
//     height: 30px;
//     transform: rotateZ(45deg);
//     // background-color: red;
//     // border: 1px solid var(--el-border-color-light);
//     box-shadow: var(--el-box-shadow-light);
//     border-radius: var(--el-popover-border-radius);
//   }
// }
:deep(img) {
  width: 400px;
  height: auto;
}
</style>
<style>
.el-popover.my-content {
  width: max-content !important;
  max-width: 600px !important;
  max-height: 400px !important;
  display: flex;
  .container {
    /* max-width: 600px; */
    overflow: scroll;
    &::-webkit-scrollbar {
      width: 5px;
      height: 5px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #888;
      border-radius: 6px;
      height: 50px;
    }
    .container-content {
      display: block;
      width: max-content;
    }
  }
}
</style>
