lockfileVersion: '6.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

overrides:
  strip-ansi-cjs: 1.0.0
  semver: ^7

dependencies:
  '@ckeditor/ckeditor5-core':
    specifier: 41.4.2
    version: 41.4.2
  lodash-es:
    specifier: ^4.17.21
    version: 4.17.21

devDependencies:
  '@babel/core':
    specifier: ^7.17.7
    version: 7.25.2
  '@ckeditor/ckeditor5-dev-bump-year':
    specifier: ^40.1.0
    version: 40.5.0
  '@ckeditor/ckeditor5-dev-ci':
    specifier: ^40.1.0
    version: 40.5.0
  '@ckeditor/ckeditor5-dev-release-tools':
    specifier: ^40.1.0
    version: 40.5.0(webpack@5.94.0)
  '@ckeditor/ckeditor5-dev-utils':
    specifier: ^40.1.0
    version: 40.5.0(webpack@5.94.0)
  '@types/lodash-es':
    specifier: ^4.17.6
    version: 4.17.12
  '@vue/test-utils':
    specifier: ^2.3.1
    version: 2.4.6
  babel-loader:
    specifier: ^8.2.3
    version: 8.3.0(@babel/core@7.25.2)(webpack@5.94.0)
  babel-plugin-istanbul:
    specifier: ^6.1.0
    version: 6.1.1
  chai:
    specifier: ^4.3.6
    version: 4.5.0
  ckeditor5:
    specifier: 41.4.2
    version: 41.4.2
  coveralls:
    specifier: ^3.1.1
    version: 3.1.1
  eslint:
    specifier: ^7.32.0
    version: 7.32.0
  eslint-config-ckeditor5:
    specifier: ^5.3.2
    version: 5.3.2(eslint@7.32.0)
  eslint-plugin-vue:
    specifier: ^9.9.0
    version: 9.27.0(eslint@7.32.0)
  husky:
    specifier: ^8.0.2
    version: 8.0.3
  karma:
    specifier: ^6.3.17
    version: 6.4.4
  karma-chai:
    specifier: ^0.1.0
    version: 0.1.0(chai@4.5.0)(karma@6.4.4)
  karma-chrome-launcher:
    specifier: ^3.1.1
    version: 3.2.0
  karma-coverage:
    specifier: ^2.2.0
    version: 2.2.1
  karma-firefox-launcher:
    specifier: ^2.1.2
    version: 2.1.3
  karma-mocha:
    specifier: ^2.0.1
    version: 2.0.1
  karma-mocha-reporter:
    specifier: ^2.2.5
    version: 2.2.5(karma@6.4.4)
  karma-sinon:
    specifier: ^1.0.5
    version: 1.0.5(karma@6.4.4)(sinon@13.0.2)
  karma-sourcemap-loader:
    specifier: ^0.3.8
    version: 0.3.8
  karma-webpack:
    specifier: ^5.0.0
    version: 5.0.1(webpack@5.94.0)
  lint-staged:
    specifier: ^10.2.11
    version: 10.5.4
  listr2:
    specifier: ^6.5.0
    version: 6.6.1
  minimist:
    specifier: ^1.2.5
    version: 1.2.8
  mocha:
    specifier: ^9.2.2
    version: 9.2.2
  sinon:
    specifier: ^13.0.1
    version: 13.0.2
  terser-webpack-plugin:
    specifier: ^3.0.2
    version: 3.1.0(webpack@5.94.0)
  ts-loader:
    specifier: ^9.4.2
    version: 9.5.1(typescript@4.9.5)(webpack@5.94.0)
  typescript:
    specifier: ^4.9.5
    version: 4.9.5
  vue:
    specifier: ^3.2.31
    version: 3.4.38(typescript@4.9.5)
  vue-eslint-parser:
    specifier: ^9.1.0
    version: 9.4.3(eslint@7.32.0)
  webpack:
    specifier: ^5.73.0
    version: 5.94.0(webpack-cli@4.10.0)
  webpack-cli:
    specifier: ^4.10.0
    version: 4.10.0(webpack@5.94.0)

packages:

  /@ampproject/remapping@2.3.0:
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25
    dev: true

  /@babel/code-frame@7.12.11:
    resolution: {integrity: sha512-Zt1yodBx1UcyiePMSkWnU4hPqhwq7hGi2nFL1LeA3EUl+q2LQx16MISgJ0+z7dnmgvP9QtIleuETGOiOH1RcIw==}
    dependencies:
      '@babel/highlight': 7.24.7
    dev: true

  /@babel/code-frame@7.24.7:
    resolution: {integrity: sha512-BcYH1CVJBO9tvyIZ2jVeXgSIMvGZ2FDRvDdOIVQyuklNKSsx+eppDEBq/g47Ayw+RqNFE+URvOShmf+f/qwAlA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/highlight': 7.24.7
      picocolors: 1.0.1
    dev: true

  /@babel/compat-data@7.25.4:
    resolution: {integrity: sha512-+LGRog6RAsCJrrrg/IO6LGmpphNe5DiK30dGjCoxxeGv49B10/3XYGxPsAwrDlMFcFEvdAUavDT8r9k/hSyQqQ==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/core@7.25.2:
    resolution: {integrity: sha512-BBt3opiCOxUr9euZ5/ro/Xv8/V7yJ5bjYMqG/C1YAo8MIKAnumZalCN+msbci3Pigy4lIQfPUpfMM27HMGaYEA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.24.7
      '@babel/generator': 7.25.5
      '@babel/helper-compilation-targets': 7.25.2
      '@babel/helper-module-transforms': 7.25.2(@babel/core@7.25.2)
      '@babel/helpers': 7.25.0
      '@babel/parser': 7.25.4
      '@babel/template': 7.25.0
      '@babel/traverse': 7.25.4
      '@babel/types': 7.25.4
      convert-source-map: 2.0.0
      debug: 4.3.6
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 7.6.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/generator@7.25.5:
    resolution: {integrity: sha512-abd43wyLfbWoxC6ahM8xTkqLpGB2iWBVyuKC9/srhFunCd1SDNrV1s72bBpK4hLj8KLzHBBcOblvLQZBNw9r3w==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.25.4
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 2.5.2
    dev: true

  /@babel/helper-compilation-targets@7.25.2:
    resolution: {integrity: sha512-U2U5LsSaZ7TAt3cfaymQ8WHh0pxvdHoEk6HVpaexxixjyEquMh0L0YNJNM6CTGKMXV1iksi0iZkGw4AcFkPaaw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/compat-data': 7.25.4
      '@babel/helper-validator-option': 7.24.8
      browserslist: 4.23.3
      lru-cache: 5.1.1
      semver: 7.6.3
    dev: true

  /@babel/helper-module-imports@7.24.7:
    resolution: {integrity: sha512-8AyH3C+74cgCVVXow/myrynrAGv+nTVg5vKu2nZph9x7RcRwzmh0VFallJuFTZ9mx6u4eSdXZfcOzSqTUm0HCA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': 7.25.4
      '@babel/types': 7.25.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-module-transforms@7.25.2(@babel/core@7.25.2):
    resolution: {integrity: sha512-BjyRAbix6j/wv83ftcVJmBt72QtHI56C7JXZoG2xATiLpmoC7dpd8WnkikExHDVPpi/3qCmO6WY1EaXOluiecQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-module-imports': 7.24.7
      '@babel/helper-simple-access': 7.24.7
      '@babel/helper-validator-identifier': 7.24.7
      '@babel/traverse': 7.25.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-plugin-utils@7.24.8:
    resolution: {integrity: sha512-FFWx5142D8h2Mgr/iPVGH5G7w6jDn4jUSpZTyDnQO0Yn7Ks2Kuz6Pci8H6MPCoUJegd/UZQ3tAvfLCxQSnWWwg==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-simple-access@7.24.7:
    resolution: {integrity: sha512-zBAIvbCMh5Ts+b86r/CjU+4XGYIs+R1j951gxI3KmmxBMhCg4oQMsv6ZXQ64XOm/cvzfU1FmoCyt6+owc5QMYg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': 7.25.4
      '@babel/types': 7.25.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-string-parser@7.24.8:
    resolution: {integrity: sha512-pO9KhhRcuUyGnJWwyEgnRJTSIZHiT+vMD0kPeD+so0l7mxkMT19g3pjY9GTnHySck/hDzq+dtW/4VgnMkippsQ==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-validator-identifier@7.24.7:
    resolution: {integrity: sha512-rR+PBcQ1SMQDDyF6X0wxtG8QyLCgUB0eRAGguqRLfkCA87l7yAP7ehq8SNj96OOGTO8OBV70KhuFYcIkHXOg0w==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-validator-option@7.24.8:
    resolution: {integrity: sha512-xb8t9tD1MHLungh/AIoWYN+gVHaB9kwlu8gffXGSt3FFEIT7RjS+xWbc2vUD1UTZdIpKj/ab3rdqJ7ufngyi2Q==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helpers@7.25.0:
    resolution: {integrity: sha512-MjgLZ42aCm0oGjJj8CtSM3DB8NOOf8h2l7DCTePJs29u+v7yO/RBX9nShlKMgFnRks/Q4tBAe7Hxnov9VkGwLw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.25.0
      '@babel/types': 7.25.4
    dev: true

  /@babel/highlight@7.24.7:
    resolution: {integrity: sha512-EStJpq4OuY8xYfhGVXngigBJRWxftKX9ksiGDnmlY3o7B/V7KIAc9X4oiK87uPJSc/vs5L869bem5fhZa8caZw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.24.7
      chalk: 2.4.2
      js-tokens: 4.0.0
      picocolors: 1.0.1
    dev: true

  /@babel/parser@7.25.4:
    resolution: {integrity: sha512-nq+eWrOgdtu3jG5Os4TQP3x3cLA8hR8TvJNjD8vnPa20WGycimcparWnLK4jJhElTK6SDyuJo1weMKO/5LpmLA==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': 7.25.4
    dev: true

  /@babel/runtime@7.25.4:
    resolution: {integrity: sha512-DSgLeL/FNcpXuzav5wfYvHCGvynXkJbn3Zvc3823AEe9nPwW9IK4UoCSS5yGymmQzN0pCPvivtgS6/8U2kkm1w==}
    engines: {node: '>=6.9.0'}
    dependencies:
      regenerator-runtime: 0.14.1
    dev: true

  /@babel/template@7.25.0:
    resolution: {integrity: sha512-aOOgh1/5XzKvg1jvVz7AVrx2piJ2XBi227DHmbY6y+bM9H2FlN+IfecYu4Xl0cNiiVejlsCri89LUsbj8vJD9Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.24.7
      '@babel/parser': 7.25.4
      '@babel/types': 7.25.4
    dev: true

  /@babel/traverse@7.25.4:
    resolution: {integrity: sha512-VJ4XsrD+nOvlXyLzmLzUs/0qjFS4sK30te5yEFlvbbUNEgKaVb2BHZUpAL+ttLPQAHNrsI3zZisbfha5Cvr8vg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.24.7
      '@babel/generator': 7.25.5
      '@babel/parser': 7.25.4
      '@babel/template': 7.25.0
      '@babel/types': 7.25.4
      debug: 4.3.6
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/types@7.25.4:
    resolution: {integrity: sha512-zQ1ijeeCXVEh+aNL0RlmkPkG8HUiDcU2pzQQFjtbntgAczRASFzj4H+6+bV+dy1ntKR14I/DypeuRG1uma98iQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': 7.24.8
      '@babel/helper-validator-identifier': 7.24.7
      to-fast-properties: 2.0.0
    dev: true

  /@ckeditor/ckeditor5-adapter-ckfinder@41.4.2:
    resolution: {integrity: sha512-yXVVEy+lEmyvYwTxn76Ff53fK/qJphf9stoBF4kFKKK/Tfi59EMog2Ctk3iIkLLyt74KmxzvuCXZwE00wDqfLA==}
    dependencies:
      ckeditor5: 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-alignment@41.4.2:
    resolution: {integrity: sha512-kFiEIZfUNt2TCrwJgM4mot6LLqzbH4vbfYcjbrsUz28kLv8guzcwKXPRe0ZrHo+WS7Cny8j5aCEuUAs3sxEmAg==}
    dependencies:
      ckeditor5: 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-autoformat@41.4.2:
    resolution: {integrity: sha512-Ukit63jHiAuLyfESdLSc6ya12xKJxDP83krZFiMY5bfXssg0z39CGuHOZlwaI9r7bBjWWMGJJeaC/9i/6L9y7g==}
    dependencies:
      ckeditor5: 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-autosave@41.4.2:
    resolution: {integrity: sha512-TgaUhpFfG9csm4seL7LQSS6Rn+Gcm/wteGyD+Yl50BG1mfMIL259KEFkVTXDRwJadQm2KiiHZDLqpcd/lAqc0A==}
    dependencies:
      ckeditor5: 41.4.2
      lodash-es: 4.17.21
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-basic-styles@41.4.2:
    resolution: {integrity: sha512-+Y+M9/JRX3xSHX/E5zFwzuKPzEeeuL61wcig1DuEz7GvK5sRLJcbdVQmiJnfDh3iOcf/ob1nahNgHAEoCno0Dw==}
    dependencies:
      ckeditor5: 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-block-quote@41.4.2:
    resolution: {integrity: sha512-tkEKd3pmDO8QWm243FRDRUv5COayXYZJpMFUL6blw3m6IXb4ujcXKn61A+UwUO+kM0NGTuepHZJkFSuV1/90sw==}
    dependencies:
      ckeditor5: 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-build-balloon-block@41.4.2:
    resolution: {integrity: sha512-p2pXJcS0hNuDZXyMi0frSwLZBm1hGGEEajJtAvKdg+pKZvvrIabQzvjtrkUf5P6Lbhl/z1/v2h0pBa76025Q5Q==}
    dependencies:
      '@ckeditor/ckeditor5-adapter-ckfinder': 41.4.2
      '@ckeditor/ckeditor5-autoformat': 41.4.2
      '@ckeditor/ckeditor5-basic-styles': 41.4.2
      '@ckeditor/ckeditor5-block-quote': 41.4.2
      '@ckeditor/ckeditor5-ckbox': 41.4.2
      '@ckeditor/ckeditor5-ckfinder': 41.4.2
      '@ckeditor/ckeditor5-cloud-services': 41.4.2
      '@ckeditor/ckeditor5-easy-image': 41.4.2
      '@ckeditor/ckeditor5-editor-balloon': 41.4.2
      '@ckeditor/ckeditor5-essentials': 41.4.2
      '@ckeditor/ckeditor5-heading': 41.4.2
      '@ckeditor/ckeditor5-image': 41.4.2
      '@ckeditor/ckeditor5-indent': 41.4.2
      '@ckeditor/ckeditor5-link': 41.4.2
      '@ckeditor/ckeditor5-list': 41.4.2
      '@ckeditor/ckeditor5-media-embed': 41.4.2
      '@ckeditor/ckeditor5-paragraph': 41.4.2
      '@ckeditor/ckeditor5-paste-from-office': 41.4.2
      '@ckeditor/ckeditor5-table': 41.4.2
      '@ckeditor/ckeditor5-typing': 41.4.2
      '@ckeditor/ckeditor5-ui': 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-build-balloon@41.4.2:
    resolution: {integrity: sha512-4FLvd2OV4UgPva0/+xHT4ZuEzKLxB9M6D04/G0YFFwvbPiy71zH0iEMj132Wd0fdEJ0fwjF1HfDzhNgI9BPhPA==}
    dependencies:
      '@ckeditor/ckeditor5-adapter-ckfinder': 41.4.2
      '@ckeditor/ckeditor5-autoformat': 41.4.2
      '@ckeditor/ckeditor5-basic-styles': 41.4.2
      '@ckeditor/ckeditor5-block-quote': 41.4.2
      '@ckeditor/ckeditor5-ckbox': 41.4.2
      '@ckeditor/ckeditor5-ckfinder': 41.4.2
      '@ckeditor/ckeditor5-cloud-services': 41.4.2
      '@ckeditor/ckeditor5-easy-image': 41.4.2
      '@ckeditor/ckeditor5-editor-balloon': 41.4.2
      '@ckeditor/ckeditor5-essentials': 41.4.2
      '@ckeditor/ckeditor5-heading': 41.4.2
      '@ckeditor/ckeditor5-image': 41.4.2
      '@ckeditor/ckeditor5-indent': 41.4.2
      '@ckeditor/ckeditor5-link': 41.4.2
      '@ckeditor/ckeditor5-list': 41.4.2
      '@ckeditor/ckeditor5-media-embed': 41.4.2
      '@ckeditor/ckeditor5-paragraph': 41.4.2
      '@ckeditor/ckeditor5-paste-from-office': 41.4.2
      '@ckeditor/ckeditor5-table': 41.4.2
      '@ckeditor/ckeditor5-typing': 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-build-classic@41.4.2:
    resolution: {integrity: sha512-fdsxmEPdNdo/usXyYMS2cN/E9KcQIrtmImUYDI+jEs6yzmSgI8By01n+enkKgVxu+2t+g+ctjQwrCpiyWIHTAQ==}
    dependencies:
      '@ckeditor/ckeditor5-adapter-ckfinder': 41.4.2
      '@ckeditor/ckeditor5-autoformat': 41.4.2
      '@ckeditor/ckeditor5-basic-styles': 41.4.2
      '@ckeditor/ckeditor5-block-quote': 41.4.2
      '@ckeditor/ckeditor5-ckbox': 41.4.2
      '@ckeditor/ckeditor5-ckfinder': 41.4.2
      '@ckeditor/ckeditor5-cloud-services': 41.4.2
      '@ckeditor/ckeditor5-easy-image': 41.4.2
      '@ckeditor/ckeditor5-editor-classic': 41.4.2
      '@ckeditor/ckeditor5-essentials': 41.4.2
      '@ckeditor/ckeditor5-heading': 41.4.2
      '@ckeditor/ckeditor5-image': 41.4.2
      '@ckeditor/ckeditor5-indent': 41.4.2
      '@ckeditor/ckeditor5-link': 41.4.2
      '@ckeditor/ckeditor5-list': 41.4.2
      '@ckeditor/ckeditor5-media-embed': 41.4.2
      '@ckeditor/ckeditor5-paragraph': 41.4.2
      '@ckeditor/ckeditor5-paste-from-office': 41.4.2
      '@ckeditor/ckeditor5-table': 41.4.2
      '@ckeditor/ckeditor5-typing': 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-build-decoupled-document@41.4.2:
    resolution: {integrity: sha512-YIjcJc9flghFsA5gZaL5oIf6TxLJxUZpXgWYa9l5C68enm36bsDycgJWGGbexHK2HOR9AnBr6ThUBQJkn3StTg==}
    dependencies:
      '@ckeditor/ckeditor5-adapter-ckfinder': 41.4.2
      '@ckeditor/ckeditor5-alignment': 41.4.2
      '@ckeditor/ckeditor5-autoformat': 41.4.2
      '@ckeditor/ckeditor5-basic-styles': 41.4.2
      '@ckeditor/ckeditor5-block-quote': 41.4.2
      '@ckeditor/ckeditor5-ckbox': 41.4.2
      '@ckeditor/ckeditor5-ckfinder': 41.4.2
      '@ckeditor/ckeditor5-cloud-services': 41.4.2
      '@ckeditor/ckeditor5-easy-image': 41.4.2
      '@ckeditor/ckeditor5-editor-decoupled': 41.4.2
      '@ckeditor/ckeditor5-essentials': 41.4.2
      '@ckeditor/ckeditor5-font': 41.4.2
      '@ckeditor/ckeditor5-heading': 41.4.2
      '@ckeditor/ckeditor5-image': 41.4.2
      '@ckeditor/ckeditor5-indent': 41.4.2
      '@ckeditor/ckeditor5-link': 41.4.2
      '@ckeditor/ckeditor5-list': 41.4.2
      '@ckeditor/ckeditor5-media-embed': 41.4.2
      '@ckeditor/ckeditor5-paragraph': 41.4.2
      '@ckeditor/ckeditor5-paste-from-office': 41.4.2
      '@ckeditor/ckeditor5-table': 41.4.2
      '@ckeditor/ckeditor5-typing': 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-build-inline@41.4.2:
    resolution: {integrity: sha512-h57jjFm0cSOFGSTA2MbvhSXMFxUbPZqwIeA9S0bfUNcGrnm84YaerX8ev8ZP2iMLOckPOcjBiC/70Sp0SvGwxg==}
    dependencies:
      '@ckeditor/ckeditor5-adapter-ckfinder': 41.4.2
      '@ckeditor/ckeditor5-autoformat': 41.4.2
      '@ckeditor/ckeditor5-basic-styles': 41.4.2
      '@ckeditor/ckeditor5-block-quote': 41.4.2
      '@ckeditor/ckeditor5-ckbox': 41.4.2
      '@ckeditor/ckeditor5-ckfinder': 41.4.2
      '@ckeditor/ckeditor5-cloud-services': 41.4.2
      '@ckeditor/ckeditor5-easy-image': 41.4.2
      '@ckeditor/ckeditor5-editor-inline': 41.4.2
      '@ckeditor/ckeditor5-essentials': 41.4.2
      '@ckeditor/ckeditor5-heading': 41.4.2
      '@ckeditor/ckeditor5-image': 41.4.2
      '@ckeditor/ckeditor5-indent': 41.4.2
      '@ckeditor/ckeditor5-link': 41.4.2
      '@ckeditor/ckeditor5-list': 41.4.2
      '@ckeditor/ckeditor5-media-embed': 41.4.2
      '@ckeditor/ckeditor5-paragraph': 41.4.2
      '@ckeditor/ckeditor5-paste-from-office': 41.4.2
      '@ckeditor/ckeditor5-table': 41.4.2
      '@ckeditor/ckeditor5-typing': 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-build-multi-root@41.4.2:
    resolution: {integrity: sha512-Hou3cHuzx2YR6cfbTYYgk2cSBkudCfPru471KxsXUtzw+B7KuHLm3LtnLJNpScF2WoGaOoaFzB9D2PkzlhF5hA==}
    dependencies:
      '@ckeditor/ckeditor5-adapter-ckfinder': 41.4.2
      '@ckeditor/ckeditor5-autoformat': 41.4.2
      '@ckeditor/ckeditor5-basic-styles': 41.4.2
      '@ckeditor/ckeditor5-block-quote': 41.4.2
      '@ckeditor/ckeditor5-ckbox': 41.4.2
      '@ckeditor/ckeditor5-ckfinder': 41.4.2
      '@ckeditor/ckeditor5-cloud-services': 41.4.2
      '@ckeditor/ckeditor5-easy-image': 41.4.2
      '@ckeditor/ckeditor5-editor-multi-root': 41.4.2
      '@ckeditor/ckeditor5-essentials': 41.4.2
      '@ckeditor/ckeditor5-heading': 41.4.2
      '@ckeditor/ckeditor5-image': 41.4.2
      '@ckeditor/ckeditor5-indent': 41.4.2
      '@ckeditor/ckeditor5-link': 41.4.2
      '@ckeditor/ckeditor5-list': 41.4.2
      '@ckeditor/ckeditor5-media-embed': 41.4.2
      '@ckeditor/ckeditor5-paragraph': 41.4.2
      '@ckeditor/ckeditor5-paste-from-office': 41.4.2
      '@ckeditor/ckeditor5-table': 41.4.2
      '@ckeditor/ckeditor5-typing': 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-ckbox@41.4.2:
    resolution: {integrity: sha512-u6HVTW7O+YSeeCZ+plg78aW74B2G+E7uKy5YQxvB99uCXGWmYy57D2maaEkPI87ZwZD3VlRnvAalaAdngc4M1g==}
    dependencies:
      blurhash: 2.0.5
      ckeditor5: 41.4.2
      lodash-es: 4.17.21
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-ckfinder@41.4.2:
    resolution: {integrity: sha512-QB3igdZOBI+I8q6eTA5+q27VQrj3Nu7PctNKRehwMC/Z6URboTnntqtkZ3inAZEbWcoLTN2tpDthlufUbQ+cfA==}
    dependencies:
      ckeditor5: 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-clipboard@41.4.2:
    resolution: {integrity: sha512-cMoGXClFxp5uR5Wr1cZnop5IdmqTZXTcrUuEoyhF+1hk+QDhp2ibQ2dTKu6hw+TTzw3Xd6g8Kj0Oj+mXoIur+w==}
    dependencies:
      '@ckeditor/ckeditor5-core': 41.4.2
      '@ckeditor/ckeditor5-engine': 41.4.2
      '@ckeditor/ckeditor5-ui': 41.4.2
      '@ckeditor/ckeditor5-utils': 41.4.2
      '@ckeditor/ckeditor5-widget': 41.4.2
      lodash-es: 4.17.21
    dev: true

  /@ckeditor/ckeditor5-cloud-services@41.4.2:
    resolution: {integrity: sha512-rgDrpEonA2AchfvgYaeb/olMk/HYxUK4B8XPqs+nJxLmBraTv2lANsgsMbwsqAIiRjT9MknmJdX+CEbqljgL/w==}
    dependencies:
      ckeditor5: 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-code-block@41.4.2:
    resolution: {integrity: sha512-NyPvffk+yA2rWsiF3Q/dDyO1ZtUvlX5hEVEWCG9C4wz9NVtBmoK0v1HmcsBDYQ//TwLY3N8HA0LX00UGTHVGFw==}
    dependencies:
      ckeditor5: 41.4.2
      lodash-es: 4.17.21
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-core@41.4.2:
    resolution: {integrity: sha512-kCIJjviiMNIMBMx7XFXFp1IeTELQKv7xyPJiVFDyUftIfthf9uWty72ipZ3BBNBGBkaoTiSzDZ507EsX6czuIQ==}
    dependencies:
      '@ckeditor/ckeditor5-engine': 41.4.2
      '@ckeditor/ckeditor5-utils': 41.4.2
      lodash-es: 4.17.21

  /@ckeditor/ckeditor5-dev-bump-year@40.5.0:
    resolution: {integrity: sha512-Kt0b+jh35ruN7N3Tjda5hd1aO0zgIu6olBS8O1TkZJblvruRSSsPfgOUy/pFBFwzECKPMSNadjoHOqjWtXOApg==}
    engines: {node: '>=18.0.0', npm: '>=5.7.1'}
    dependencies:
      chalk: 4.1.2
      glob: 10.4.5
    dev: true

  /@ckeditor/ckeditor5-dev-ci@40.5.0:
    resolution: {integrity: sha512-E3aaLhh/ddZTIzTxsImPLkPCAQYru6vaDw0qawz7gmOay9nuU+go4Y7vdLbcOv9+xQGfOkr8sPoIPm67MEQqiQ==}
    engines: {node: '>=18.0.0', npm: '>=5.7.1'}
    hasBin: true
    dependencies:
      minimist: 1.2.8
      node-fetch: 2.7.0
      slack-notify: 2.0.7
    transitivePeerDependencies:
      - encoding
    dev: true

  /@ckeditor/ckeditor5-dev-release-tools@40.5.0(webpack@5.94.0):
    resolution: {integrity: sha512-FsZxwBqUrZFxO2jbZ0E0wHxJln0jebLC5YQgG9Q5haylTqooCQDQiiPheMoLAlCur2ZqbHYrWhqj0mV2Z7jZ2w==}
    engines: {node: '>=18.0.0', npm: '>=5.7.1'}
    dependencies:
      '@ckeditor/ckeditor5-dev-utils': 40.5.0(webpack@5.94.0)
      '@octokit/rest': 19.0.13
      chalk: 4.1.2
      cli-columns: 4.0.0
      cli-table: 0.3.11
      compare-func: 2.0.0
      concat-stream: 2.0.0
      conventional-changelog-writer: 6.0.1
      conventional-commits-filter: 3.0.0
      conventional-commits-parser: 4.0.0
      date-fns: 2.30.0
      diff: 5.2.0
      fs-extra: 9.1.0
      git-raw-commits: 3.0.0
      glob: 10.4.5
      inquirer: 7.3.3
      lodash: 4.17.21
      minimatch: 3.1.2
      mkdirp: 1.0.4
      parse-github-url: 1.0.3
      semver: 7.6.3
      upath: 2.0.1
    transitivePeerDependencies:
      - bluebird
      - encoding
      - supports-color
      - webpack
    dev: true

  /@ckeditor/ckeditor5-dev-translations@40.5.0:
    resolution: {integrity: sha512-oYTpZAchobqYjeWX5v2Awhg8HRZ6UKiFNc7CeSvfm4l3krJPO4NAxfNZrdRwT4pCn4mz9zovfUvJlb2oWVEQxQ==}
    engines: {node: '>=18.0.0', npm: '>=5.7.1'}
    dependencies:
      '@babel/parser': 7.25.4
      '@babel/traverse': 7.25.4
      chalk: 4.1.2
      pofile: 1.1.4
      rimraf: 3.0.2
      webpack-sources: 2.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@ckeditor/ckeditor5-dev-utils@40.5.0(webpack@5.94.0):
    resolution: {integrity: sha512-O8Fb7M9BIwI59xIK0/cnVG3u5OK2k2US3Q8rphnwfxWItmOPAnnt8zZt5Kpsa3Rbpe/kIIi5BOgOOjZO8dFHdw==}
    engines: {node: '>=18.0.0', npm: '>=5.7.1'}
    dependencies:
      '@ckeditor/ckeditor5-dev-translations': 40.5.0
      chalk: 3.0.0
      cli-cursor: 3.1.0
      cli-spinners: 2.9.2
      css-loader: 5.2.7(webpack@5.94.0)
      cssnano: 6.1.2(postcss@8.4.41)
      del: 5.1.0
      esbuild-loader: 3.0.1(webpack@5.94.0)
      fs-extra: 9.1.0
      is-interactive: 1.0.0
      javascript-stringify: 1.6.0
      mini-css-extract-plugin: 2.9.1(webpack@5.94.0)
      postcss: 8.4.41
      postcss-import: 14.1.0(postcss@8.4.41)
      postcss-loader: 4.3.0(postcss@8.4.41)(webpack@5.94.0)
      postcss-mixins: 9.0.4(postcss@8.4.41)
      postcss-nesting: 10.2.0(postcss@8.4.41)
      raw-loader: 4.0.2(webpack@5.94.0)
      shelljs: 0.8.5
      style-loader: 2.0.0(webpack@5.94.0)
      terser-webpack-plugin: 4.2.3(webpack@5.94.0)
      through2: 3.0.2
    transitivePeerDependencies:
      - bluebird
      - supports-color
      - webpack
    dev: true

  /@ckeditor/ckeditor5-easy-image@41.4.2:
    resolution: {integrity: sha512-HJJ3Z4R4mCazV2cz+s8bI00ci3/KyIa+fXodBN1+kg3PldX471zSj+DtyFsZyKnUcpUTVygjPEaHKBDpxtUhjQ==}
    dependencies:
      ckeditor5: 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-editor-balloon@41.4.2:
    resolution: {integrity: sha512-5KI9spGZY1W2GpRLc0aJiqm1/33sGX9vxXAvIRabSF1uhK4b2WP6zdjGy0IcwBpIRnAkEGoPoetFmx1esJOVDw==}
    dependencies:
      ckeditor5: 41.4.2
      lodash-es: 4.17.21
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-editor-classic@41.4.2:
    resolution: {integrity: sha512-BVf4ipZz36eCTDFiQ8hqN+oBmuvZPzCmNDDjCYuHNGCKGLaIo1Yzi09fHPUWDw1U+en6Cgnwc2HSWgwf7zC7aA==}
    dependencies:
      ckeditor5: 41.4.2
      lodash-es: 4.17.21
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-editor-decoupled@41.4.2:
    resolution: {integrity: sha512-kzy+Az4Dn+5dCR0FMk1qzlGaqbgNSi0a7qLr17ghfVnqbLYmhhELjgLOKU9cjjIm5L2KMEH2qRq5QHlacO90kA==}
    dependencies:
      ckeditor5: 41.4.2
      lodash-es: 4.17.21
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-editor-inline@41.4.2:
    resolution: {integrity: sha512-NlDYZzVVpZblkeVLNrguC437PMqYEXNRGB+KF2uzV5/vPAjz3SjleVcGlbTAWVbMQAUMoOtrmrJjeTR4S93UMA==}
    dependencies:
      ckeditor5: 41.4.2
      lodash-es: 4.17.21
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-editor-multi-root@41.4.2:
    resolution: {integrity: sha512-sqmSEHzX0C3L5H+Svj1dSOyetxOnVb5vL2eS/EdzRpnhThwaPsTVWI83bGHPRTh4h89yEli3nMbNsdTTnsR7Rw==}
    dependencies:
      ckeditor5: 41.4.2
      lodash-es: 4.17.21
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-engine@41.4.2:
    resolution: {integrity: sha512-25JqIzNYvCqQ6f02YY+a8A8xtjClzI0YCio0JGoRG3JHJXzYsQbTPsiokuE1BCwMCu3gYoFz8eKJYt2selLsCw==}
    dependencies:
      '@ckeditor/ckeditor5-utils': 41.4.2
      lodash-es: 4.17.21

  /@ckeditor/ckeditor5-enter@41.4.2:
    resolution: {integrity: sha512-pvNNcFGn7TFFuJ1QbT0Jggd5xflORxa5i32nZuSzDLVflXGDKq53xSXxapCzd7XsiVXQlufbXt2SlGj7lhyP1w==}
    dependencies:
      '@ckeditor/ckeditor5-core': 41.4.2
      '@ckeditor/ckeditor5-engine': 41.4.2
      '@ckeditor/ckeditor5-utils': 41.4.2
    dev: true

  /@ckeditor/ckeditor5-essentials@41.4.2:
    resolution: {integrity: sha512-1UCvk76v2+NDfHfTo3Qg0EJYpddgSC/i66jY3NnQUFt1zt68rAzm/kFOVYjTD/QDn6pyiMAIUeMlKFkswF+upg==}
    dependencies:
      ckeditor5: 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-find-and-replace@41.4.2:
    resolution: {integrity: sha512-y3JZF9UMgf6Zwt3HzaPI9B8Gbwc1s+IoK78LFuhkP9B/rgQDBFWi3fXo6ywHsHKZ/EK5JZQuHMdI9czyBuG29Q==}
    dependencies:
      '@ckeditor/ckeditor5-ui': 41.4.2
      ckeditor5: 41.4.2
      lodash-es: 4.17.21
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-font@41.4.2:
    resolution: {integrity: sha512-++7oIK+MXtHGUQkqmXgZqGDBCEsHCuGkss43goGZ97PcRwLegnDRInowj3K/r3nwQcts1VAWnnLCnCSSYbcGIQ==}
    dependencies:
      ckeditor5: 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-heading@41.4.2:
    resolution: {integrity: sha512-8AyMumy90nY49reQlHuCgSJFSaym4emCVF6vAAqd71FHtmgtfS9w3xMqXAk6QbgMjfy46cwind0JITZfBfKqLg==}
    dependencies:
      ckeditor5: 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-highlight@41.4.2:
    resolution: {integrity: sha512-xAb3Kox0KfoenZaRWgWaZPQwYLauK46WdQ4zYJ16ozQN5mssnS8sU27EFx0OG5EOv9EBurmOcHnP3Rih1szROQ==}
    dependencies:
      ckeditor5: 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-horizontal-line@41.4.2:
    resolution: {integrity: sha512-le+6melLc8lQTPBWppnWXWaX16KXcvXz8ZOO4uuD7+w5JrtRheEG1N35nTblpeT+QcyBjL9mSu519xReL2qjBA==}
    dependencies:
      ckeditor5: 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-html-embed@41.4.2:
    resolution: {integrity: sha512-rpQMp6ckpYPWnBg8vL23SdKfJ0F80C1iIIO7EA9ZyimPc+hWH7kVF7f8D2Q2ckG1LrlXAXn9cg4tahMFGeiSzw==}
    dependencies:
      ckeditor5: 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-html-support@41.4.2:
    resolution: {integrity: sha512-QHqFgzQucCRvEOPdxcXOMervxhlK6DiR6JqUvgeJyyiWWQT0HGiG7Vy7QKhL6S0w5BUYFoS5B8rj5LjOEm+xsg==}
    dependencies:
      ckeditor5: 41.4.2
      lodash-es: 4.17.21
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-image@41.4.2:
    resolution: {integrity: sha512-4AgXdvYr6tGzEqwAHVRl+LA8nPRER7vQthVBuT4g1FEkRB6w9kgRsPM2JfsGekoGd8GU0WnMaz8kAcL4C2urYg==}
    dependencies:
      '@ckeditor/ckeditor5-ui': 41.4.2
      ckeditor5: 41.4.2
      lodash-es: 4.17.21
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-indent@41.4.2:
    resolution: {integrity: sha512-pghHa+DKya6788nTiU1ZItKmAgjf+up4Rqe5GOkxKB7vJc189KSBJYc5foov65nM831rXcWgTk4jybK+JGHmjQ==}
    dependencies:
      ckeditor5: 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-language@41.4.2:
    resolution: {integrity: sha512-YrjwPRxtHDf99fnsbYxos/OuJcdEYYk4sx8oyVgwG/se0yk4IObx7MZGVebGiqd5cZQRxAxP8VGNgRqjHzpcsg==}
    dependencies:
      ckeditor5: 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-link@41.4.2:
    resolution: {integrity: sha512-woMv9/BxkDjG5xsS/OyaxW9tWTuiG6wZWWcYxVJq8FOW2NL68CKQLmyoQ1rdv/2Gw4UPUXTtB+1uGVmQDMXDsQ==}
    dependencies:
      '@ckeditor/ckeditor5-ui': 41.4.2
      ckeditor5: 41.4.2
      lodash-es: 4.17.21
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-list@41.4.2:
    resolution: {integrity: sha512-nGb36jNJO6YAU35piKabey9B13xw6TnmL5VySS2dEqSt/DTy7RdY5z2K7CU/NGuIGe/bPBZgU1J0dQkRr2F3hA==}
    dependencies:
      ckeditor5: 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-markdown-gfm@41.4.2:
    resolution: {integrity: sha512-4izHzZ2AO9QMo+WirGVPYu3qqf+YuYe0CtF37rhdRNFLwDMYV7lGBpEj24US/3lV7CuEKM1V5N2Ojl6b4ew10w==}
    dependencies:
      ckeditor5: 41.4.2
      marked: 4.0.12
      turndown: 6.0.0
      turndown-plugin-gfm: 1.0.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-media-embed@41.4.2:
    resolution: {integrity: sha512-+4JqfbnMrB9Si2gAKKCRZTY1hixlk11mY8+PA+32UezyCq/myoAlVGT8ytCr3rywe58nbkGGAv2QbVo6fy8zoQ==}
    dependencies:
      '@ckeditor/ckeditor5-ui': 41.4.2
      ckeditor5: 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-mention@41.4.2:
    resolution: {integrity: sha512-jO8eZE/4NIRJ5Tm/mIdgnLqkBnYj7l3jU4HZLkYvU5tEV5Xk6Rf8bsqMkkBvquf3LVhQbwAiLNjtlrHf68vU7Q==}
    dependencies:
      ckeditor5: 41.4.2
      lodash-es: 4.17.21
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-minimap@41.4.2:
    resolution: {integrity: sha512-SJUHeD6l6UVFlY/Uh2vZIr7qHbz5A4Ud285zxAZpiiiv0NP4wQDw6bo28tD/QkCMm1hRcLCkKWd1aNDkFe+42w==}
    dependencies:
      ckeditor5: 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-page-break@41.4.2:
    resolution: {integrity: sha512-J9sIBgBKhAeZn+KpZADUj6z7VjrbUtHHFL88Ivx2h9jePZPT/LIfDwnnrJEnMjf2KF1bkHvIdP23cZz2BzXwKg==}
    dependencies:
      ckeditor5: 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-paragraph@41.4.2:
    resolution: {integrity: sha512-tOsD40Fcqli5zkH/68WhcqYU8BL4qb8J5xGuk1xmBokz3W0LzebWW0GXmFk5PmWv+fg0dOXfSo8uMzb5ni+CuQ==}
    dependencies:
      '@ckeditor/ckeditor5-core': 41.4.2
      '@ckeditor/ckeditor5-ui': 41.4.2
      '@ckeditor/ckeditor5-utils': 41.4.2
    dev: true

  /@ckeditor/ckeditor5-paste-from-office@41.4.2:
    resolution: {integrity: sha512-jby5YQ2QowGdDCshPq5Ej11wTFcBZP2dYhQTu6fRZRc+mdihKCILxh0rwBgBOCCf+buflx8RYp/WKd76Kcuq5g==}
    dependencies:
      ckeditor5: 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-remove-format@41.4.2:
    resolution: {integrity: sha512-XlCIvIETcWn6/6jfPhVzSqkXZ6fnU0iqqNlyKF67dStfc6vVc6Ut31P+f84SwAJA8ay553OUNyY14YZcoP2tLg==}
    dependencies:
      ckeditor5: 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@ckeditor/ckeditor5-restricted-editing@41.4.2:
    resolution: {integrity: sha512-t34VNBZbxO07nEazAKECXcRgH5VrPbrTJW0iZO0/w/yPHUAPZ8ejcdEuohr7cLS3TCHE09biFc1lNPLas/xK5w==}
    dependencies:
      ckeditor5: 41.4.2
    dev: true

  /@ckeditor/ckeditor5-select-all@41.4.2:
    resolution: {integrity: sha512-zC0wS0IggFDvk1wDB/SregfejLJk62In+i7P0otOaySg5tFfkJqT3OycplbPqIn3D1UCpIIz4KJzRl66PEVI7g==}
    dependencies:
      '@ckeditor/ckeditor5-core': 41.4.2
      '@ckeditor/ckeditor5-ui': 41.4.2
      '@ckeditor/ckeditor5-utils': 41.4.2
    dev: true

  /@ckeditor/ckeditor5-show-blocks@41.4.2:
    resolution: {integrity: sha512-0mKErojbxNr8Xbx5OjDLdciU3Onwn33h5IMU2j6imcwqORLzyXgU9ENhwwfw6Roeu8Guvi6hEVKBW6GE1UIYIQ==}
    dependencies:
      '@ckeditor/ckeditor5-ui': 41.4.2
      ckeditor5: 41.4.2
    dev: true

  /@ckeditor/ckeditor5-source-editing@41.4.2:
    resolution: {integrity: sha512-TnBJLLEU5dckalm8KZP/xC0kLMeNDVTrWUp8iCLcmLoe9xlt/wIO8VzLVPW+WjgzSZ7Yq+vrzHaCyJRVxuDsBQ==}
    dependencies:
      '@ckeditor/ckeditor5-theme-lark': 41.4.2
      ckeditor5: 41.4.2
    dev: true

  /@ckeditor/ckeditor5-special-characters@41.4.2:
    resolution: {integrity: sha512-OicpKzkYqyTjPRGZf6xMYQnuUCAZ4QS2H1MAEH5xTiwYv+eqR/enC/m9FxCEs2Z3DlO9DIjVnoBxe2qUCSxRBQ==}
    dependencies:
      ckeditor5: 41.4.2
    dev: true

  /@ckeditor/ckeditor5-style@41.4.2:
    resolution: {integrity: sha512-q39mtg1kBrmJ8XA7XbOy4HhVzrICvt0KS484d5c3NaX7JetwapAM/QfWDGfMToMukzFcntaGt0be5Bwja0LJSw==}
    dependencies:
      ckeditor5: 41.4.2
      lodash-es: 4.17.21
    dev: true

  /@ckeditor/ckeditor5-table@41.4.2:
    resolution: {integrity: sha512-wDn1UUaKHcrscmTYj2PwCFbj9FOXFfhk4JbCepyGFQt31YyaOKBzAyZaJQ7E38wJq7a4afac3MwUDk+j1X5FDw==}
    dependencies:
      '@ckeditor/ckeditor5-ui': 41.4.2
      ckeditor5: 41.4.2
      lodash-es: 4.17.21
    dev: true

  /@ckeditor/ckeditor5-theme-lark@41.4.2:
    resolution: {integrity: sha512-rzFSAhdPMD2QylJDwgGniiBoCuHWQAQIEKDtMbQ4FH+/7JiCfKgUsnZxqhDPJwQyV1MWVz4wmXK/1RKqHohOvg==}
    dependencies:
      '@ckeditor/ckeditor5-ui': 41.4.2
    dev: true

  /@ckeditor/ckeditor5-typing@41.4.2:
    resolution: {integrity: sha512-dXP+uNl+jkfrSIqMNai2yakR/3JqJ9g0M9WwwnV5vzbEOKD4YKP5+ixvqKb39dwLCLZ4mGpJaX+rjNXBExjSIw==}
    dependencies:
      '@ckeditor/ckeditor5-core': 41.4.2
      '@ckeditor/ckeditor5-engine': 41.4.2
      '@ckeditor/ckeditor5-utils': 41.4.2
      lodash-es: 4.17.21
    dev: true

  /@ckeditor/ckeditor5-ui@41.4.2:
    resolution: {integrity: sha512-wvRbDXJN8PmaWyB0H487DjvdH2ayMyN52+WLkZlVbhX9ICb1sf5XnLz4v/wXeQ4W8JbWdsg2FZIDDQDeXjvyJw==}
    dependencies:
      '@ckeditor/ckeditor5-core': 41.4.2
      '@ckeditor/ckeditor5-utils': 41.4.2
      color-convert: 2.0.1
      color-parse: 1.4.2
      lodash-es: 4.17.21
      vanilla-colorful: 0.7.2
    dev: true

  /@ckeditor/ckeditor5-undo@41.4.2:
    resolution: {integrity: sha512-mJMoALRWAaFg9Jgu+ufSGR/cUGCawMcz7Iwr5TBdrICmIckxx0DxPwWCPoTgI1laBZtRy/QctO2gQ4H+FYbfUw==}
    dependencies:
      '@ckeditor/ckeditor5-core': 41.4.2
      '@ckeditor/ckeditor5-engine': 41.4.2
      '@ckeditor/ckeditor5-ui': 41.4.2
    dev: true

  /@ckeditor/ckeditor5-upload@41.4.2:
    resolution: {integrity: sha512-dCNQhZw9QcgGUKlYD8STpgNanNp7ILPMRNoDFW9NWHRKsUpjGMYIU3dsE4f08hkA/bckJ9yBaZc7a0LavOrncw==}
    dependencies:
      '@ckeditor/ckeditor5-core': 41.4.2
      '@ckeditor/ckeditor5-utils': 41.4.2
    dev: true

  /@ckeditor/ckeditor5-utils@41.4.2:
    resolution: {integrity: sha512-VgLr2eLVggyhDqa7H8JUxpnOLTZ0R/YuDZ6ENVUumd9q4VrpNs94ZK0Y/Shp7UmuHQ/sTth+PWTsi+t5KwYqeQ==}
    dependencies:
      lodash-es: 4.17.21

  /@ckeditor/ckeditor5-watchdog@41.4.2:
    resolution: {integrity: sha512-u17Y8XHhyDHaShQei7WuZ0th8DgKo56YfJqRdZautHKnPJ32r+O97uTcGfBpsobhZbJ6Ss3tUwebve3Obv2K/w==}
    dependencies:
      lodash-es: 4.17.21
    dev: true

  /@ckeditor/ckeditor5-widget@41.4.2:
    resolution: {integrity: sha512-hpM9Ti2iFvBBIPAESJp3bOY4SR6fzF3V5t46CpVDStLJdqwnQOuZ8Nv1dqzZZWCuK+EByAbY14pgfYM92nNHrQ==}
    dependencies:
      '@ckeditor/ckeditor5-core': 41.4.2
      '@ckeditor/ckeditor5-engine': 41.4.2
      '@ckeditor/ckeditor5-enter': 41.4.2
      '@ckeditor/ckeditor5-typing': 41.4.2
      '@ckeditor/ckeditor5-ui': 41.4.2
      '@ckeditor/ckeditor5-utils': 41.4.2
      lodash-es: 4.17.21
    dev: true

  /@ckeditor/ckeditor5-word-count@41.4.2:
    resolution: {integrity: sha512-XuCLL97FotJ9QfuCZOhW7V2XHfVXkplIDpwgnH4HnLjtMLGFVZbyb0k9pEJk3Kp+F8qQbfWDIPFzaNKRDKqtRA==}
    dependencies:
      ckeditor5: 41.4.2
      lodash-es: 4.17.21
    dev: true

  /@colors/colors@1.5.0:
    resolution: {integrity: sha512-ooWCrlZP11i8GImSjTHYHLkvFDP48nS4+204nGb1RiX/WXYHmJA2III9/e2DWVabCESdW7hBAEzHRqUn9OUVvQ==}
    engines: {node: '>=0.1.90'}
    dev: true

  /@csstools/selector-specificity@2.2.0(postcss-selector-parser@6.1.2):
    resolution: {integrity: sha512-+OJ9konv95ClSTOJCmMZqpd5+YGsB2S+x6w3E1oaM8UuR5j8nTNHYSz8c9BEPGDOCMQYIEEGlVPj/VY64iTbGw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss-selector-parser: ^6.0.10
    dependencies:
      postcss-selector-parser: 6.1.2
    dev: true

  /@discoveryjs/json-ext@0.5.7:
    resolution: {integrity: sha512-dBVuXR082gk3jsFp7Rd/JI4kytwGHecnCoTtXFb7DB6CNHp4rg5k1bhg0nWdLGLnOV71lmDzGQaLMy8iPLY0pw==}
    engines: {node: '>=10.0.0'}
    dev: true

  /@es-joy/jsdoccomment@0.40.1:
    resolution: {integrity: sha512-YORCdZSusAlBrFpZ77pJjc5r1bQs5caPWtAu+WWmiSo+8XaUzseapVrfAtiRFbQWnrBxxLLEwF6f6ZG/UgCQCg==}
    engines: {node: '>=16'}
    dependencies:
      comment-parser: 1.4.0
      esquery: 1.6.0
      jsdoc-type-pratt-parser: 4.0.0
    dev: true

  /@esbuild/android-arm64@0.17.19:
    resolution: {integrity: sha512-KBMWvEZooR7+kzY0BtbTQn0OAYY7CsiydT63pVEaPtVYF0hXbUaOyZog37DKxK7NF3XacBJOpYT4adIJh+avxA==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/android-arm@0.17.19:
    resolution: {integrity: sha512-rIKddzqhmav7MSmoFCmDIb6e2W57geRsM94gV2l38fzhXMwq7hZoClug9USI2pFRGL06f4IOPHHpFNOkWieR8A==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/android-x64@0.17.19:
    resolution: {integrity: sha512-uUTTc4xGNDT7YSArp/zbtmbhO0uEEK9/ETW29Wk1thYUJBz3IVnvgEiEwEa9IeLyvnpKrWK64Utw2bgUmDveww==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/darwin-arm64@0.17.19:
    resolution: {integrity: sha512-80wEoCfF/hFKM6WE1FyBHc9SfUblloAWx6FJkFWTWiCoht9Mc0ARGEM47e67W9rI09YoUxJL68WHfDRYEAvOhg==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/darwin-x64@0.17.19:
    resolution: {integrity: sha512-IJM4JJsLhRYr9xdtLytPLSH9k/oxR3boaUIYiHkAawtwNOXKE8KoU8tMvryogdcT8AU+Bflmh81Xn6Q0vTZbQw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/freebsd-arm64@0.17.19:
    resolution: {integrity: sha512-pBwbc7DufluUeGdjSU5Si+P3SoMF5DQ/F/UmTSb8HXO80ZEAJmrykPyzo1IfNbAoaqw48YRpv8shwd1NoI0jcQ==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/freebsd-x64@0.17.19:
    resolution: {integrity: sha512-4lu+n8Wk0XlajEhbEffdy2xy53dpR06SlzvhGByyg36qJw6Kpfk7cp45DR/62aPH9mtJRmIyrXAS5UWBrJT6TQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-arm64@0.17.19:
    resolution: {integrity: sha512-ct1Tg3WGwd3P+oZYqic+YZF4snNl2bsnMKRkb3ozHmnM0dGWuxcPTTntAF6bOP0Sp4x0PjSF+4uHQ1xvxfRKqg==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-arm@0.17.19:
    resolution: {integrity: sha512-cdmT3KxjlOQ/gZ2cjfrQOtmhG4HJs6hhvm3mWSRDPtZ/lP5oe8FWceS10JaSJC13GBd4eH/haHnqf7hhGNLerA==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-ia32@0.17.19:
    resolution: {integrity: sha512-w4IRhSy1VbsNxHRQpeGCHEmibqdTUx61Vc38APcsRbuVgK0OPEnQ0YD39Brymn96mOx48Y2laBQGqgZ0j9w6SQ==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-loong64@0.17.19:
    resolution: {integrity: sha512-2iAngUbBPMq439a+z//gE+9WBldoMp1s5GWsUSgqHLzLJ9WoZLZhpwWuym0u0u/4XmZ3gpHmzV84PonE+9IIdQ==}
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-mips64el@0.17.19:
    resolution: {integrity: sha512-LKJltc4LVdMKHsrFe4MGNPp0hqDFA1Wpt3jE1gEyM3nKUvOiO//9PheZZHfYRfYl6AwdTH4aTcXSqBerX0ml4A==}
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-ppc64@0.17.19:
    resolution: {integrity: sha512-/c/DGybs95WXNS8y3Ti/ytqETiW7EU44MEKuCAcpPto3YjQbyK3IQVKfF6nbghD7EcLUGl0NbiL5Rt5DMhn5tg==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-riscv64@0.17.19:
    resolution: {integrity: sha512-FC3nUAWhvFoutlhAkgHf8f5HwFWUL6bYdvLc/TTuxKlvLi3+pPzdZiFKSWz/PF30TB1K19SuCxDTI5KcqASJqA==}
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-s390x@0.17.19:
    resolution: {integrity: sha512-IbFsFbxMWLuKEbH+7sTkKzL6NJmG2vRyy6K7JJo55w+8xDk7RElYn6xvXtDW8HCfoKBFK69f3pgBJSUSQPr+4Q==}
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-x64@0.17.19:
    resolution: {integrity: sha512-68ngA9lg2H6zkZcyp22tsVt38mlhWde8l3eJLWkyLrp4HwMUr3c1s/M2t7+kHIhvMjglIBrFpncX1SzMckomGw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/netbsd-x64@0.17.19:
    resolution: {integrity: sha512-CwFq42rXCR8TYIjIfpXCbRX0rp1jo6cPIUPSaWwzbVI4aOfX96OXY8M6KNmtPcg7QjYeDmN+DD0Wp3LaBOLf4Q==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/openbsd-x64@0.17.19:
    resolution: {integrity: sha512-cnq5brJYrSZ2CF6c35eCmviIN3k3RczmHz8eYaVlNasVqsNY+JKohZU5MKmaOI+KkllCdzOKKdPs762VCPC20g==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/sunos-x64@0.17.19:
    resolution: {integrity: sha512-vCRT7yP3zX+bKWFeP/zdS6SqdWB8OIpaRq/mbXQxTGHnIxspRtigpkUcDMlSCOejlHowLqII7K2JKevwyRP2rg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-arm64@0.17.19:
    resolution: {integrity: sha512-yYx+8jwowUstVdorcMdNlzklLYhPxjniHWFKgRqH7IFlUEa0Umu3KuYplf1HUZZ422e3NU9F4LGb+4O0Kdcaag==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-ia32@0.17.19:
    resolution: {integrity: sha512-eggDKanJszUtCdlVs0RB+h35wNlb5v4TWEkq4vZcmVt5u/HiDZrTXe2bWFQUez3RgNHwx/x4sk5++4NSSicKkw==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-x64@0.17.19:
    resolution: {integrity: sha512-lAhycmKnVOuRYNtRtatQR1LPQf2oYCkRGkSFnseDAKPl8lu5SOsK/e1sXe5a0Pc5kHIHe6P2I/ilntNv2xf3cA==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@eslint-community/eslint-utils@4.4.0(eslint@7.32.0):
    resolution: {integrity: sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
    dependencies:
      eslint: 7.32.0
      eslint-visitor-keys: 3.4.3
    dev: true

  /@eslint-community/regexpp@4.11.0:
    resolution: {integrity: sha512-G/M/tIiMrTAxEWRfLfQJMmGNX28IxBg4PBz8XqQhqUHLFI6TL2htpIB1iQCj144V5ee/JaKyT9/WZ0MGZWfA7A==}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}
    dev: true

  /@eslint/eslintrc@0.4.3:
    resolution: {integrity: sha512-J6KFFz5QCYUJq3pf0mjEcCJVERbzv71PUIDczuh9JkwGEzced6CO5ADLHB1rbf/+oPBtoPfMYNOpGDzCANlbXw==}
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      ajv: 6.12.6
      debug: 4.3.6
      espree: 7.3.1
      globals: 13.24.0
      ignore: 4.0.6
      import-fresh: 3.3.0
      js-yaml: 3.14.1
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@gar/promisify@1.1.3:
    resolution: {integrity: sha512-k2Ty1JcVojjJFwrg/ThKi2ujJ7XNLYaFGNB/bWT9wGR+oSMJHMa5w+CUq6p/pVrKeNNgA7pCqEcjSnHVoqJQFw==}
    dev: true

  /@humanwhocodes/config-array@0.5.0:
    resolution: {integrity: sha512-FagtKFz74XrTl7y6HCzQpwDfXP0yhxe9lHLD1UZxjvZIcbyRz8zTFF/yYNfSfzU414eDwZ1SrO0Qvtyf+wFMQg==}
    engines: {node: '>=10.10.0'}
    deprecated: Use @eslint/config-array instead
    dependencies:
      '@humanwhocodes/object-schema': 1.2.1
      debug: 4.3.6
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@humanwhocodes/object-schema@1.2.1:
    resolution: {integrity: sha512-ZnQMnLV4e7hDlUvw8H+U8ASL02SS2Gn6+9Ac3wGGLIe7+je2AeAOxPY+izIPJDfFDb7eDjev0Us8MO1iFRN8hA==}
    deprecated: Use @eslint/object-schema instead
    dev: true

  /@isaacs/cliui@8.0.2:
    resolution: {integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==}
    engines: {node: '>=12'}
    dependencies:
      string-width: 5.1.2
      string-width-cjs: /string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: 1.0.0
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: /wrap-ansi@7.0.0
    dev: true

  /@istanbuljs/load-nyc-config@1.1.0:
    resolution: {integrity: sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ==}
    engines: {node: '>=8'}
    dependencies:
      camelcase: 5.3.1
      find-up: 4.1.0
      get-package-type: 0.1.0
      js-yaml: 3.14.1
      resolve-from: 5.0.0
    dev: true

  /@istanbuljs/schema@0.1.3:
    resolution: {integrity: sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==}
    engines: {node: '>=8'}
    dev: true

  /@jridgewell/gen-mapping@0.3.5:
    resolution: {integrity: sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25
    dev: true

  /@jridgewell/resolve-uri@3.1.2:
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}
    dev: true

  /@jridgewell/set-array@1.2.1:
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==}
    engines: {node: '>=6.0.0'}
    dev: true

  /@jridgewell/source-map@0.3.6:
    resolution: {integrity: sha512-1ZJTZebgqllO79ue2bm3rIGud/bOe0pP5BjSRCRxxYkEZS8STV7zN84UBbiYu7jy+eCKSnVIUgoWWE/tt+shMQ==}
    dependencies:
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25
    dev: true

  /@jridgewell/sourcemap-codec@1.5.0:
    resolution: {integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==}
    dev: true

  /@jridgewell/trace-mapping@0.3.25:
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==}
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0
    dev: true

  /@nodelib/fs.scandir@2.1.5:
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0
    dev: true

  /@nodelib/fs.stat@2.0.5:
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}
    dev: true

  /@nodelib/fs.walk@1.2.8:
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.17.1
    dev: true

  /@npmcli/fs@1.1.1:
    resolution: {integrity: sha512-8KG5RD0GVP4ydEzRn/I4BNDuxDtqVbOdm8675T49OIG/NGhaK0pjPX7ZcDlvKYbA+ulvVK3ztfcF4uBdOxuJbQ==}
    dependencies:
      '@gar/promisify': 1.1.3
      semver: 7.6.3
    dev: true

  /@npmcli/move-file@1.1.2:
    resolution: {integrity: sha512-1SUf/Cg2GzGDyaf15aR9St9TWlb+XvbZXWpDx8YKs7MLzMH/BCeopv+y9vzrzgkfykCGuWOlSu3mZhj2+FQcrg==}
    engines: {node: '>=10'}
    deprecated: This functionality has been moved to @npmcli/fs
    dependencies:
      mkdirp: 1.0.4
      rimraf: 3.0.2
    dev: true

  /@octokit/auth-token@3.0.4:
    resolution: {integrity: sha512-TWFX7cZF2LXoCvdmJWY7XVPi74aSY0+FfBZNSXEXFkMpjcqsQwDSYVv5FhRFaI0V1ECnwbz4j59T/G+rXNWaIQ==}
    engines: {node: '>= 14'}
    dev: true

  /@octokit/core@4.2.4:
    resolution: {integrity: sha512-rYKilwgzQ7/imScn3M9/pFfUf4I1AZEH3KhyJmtPdE2zfaXAn2mFfUy4FbKewzc2We5y/LlKLj36fWJLKC2SIQ==}
    engines: {node: '>= 14'}
    dependencies:
      '@octokit/auth-token': 3.0.4
      '@octokit/graphql': 5.0.6
      '@octokit/request': 6.2.8
      '@octokit/request-error': 3.0.3
      '@octokit/types': 9.3.2
      before-after-hook: 2.2.3
      universal-user-agent: 6.0.1
    transitivePeerDependencies:
      - encoding
    dev: true

  /@octokit/endpoint@7.0.6:
    resolution: {integrity: sha512-5L4fseVRUsDFGR00tMWD/Trdeeihn999rTMGRMC1G/Ldi1uWlWJzI98H4Iak5DB/RVvQuyMYKqSK/R6mbSOQyg==}
    engines: {node: '>= 14'}
    dependencies:
      '@octokit/types': 9.3.2
      is-plain-object: 5.0.0
      universal-user-agent: 6.0.1
    dev: true

  /@octokit/graphql@5.0.6:
    resolution: {integrity: sha512-Fxyxdy/JH0MnIB5h+UQ3yCoh1FG4kWXfFKkpWqjZHw/p+Kc8Y44Hu/kCgNBT6nU1shNumEchmW/sUO1JuQnPcw==}
    engines: {node: '>= 14'}
    dependencies:
      '@octokit/request': 6.2.8
      '@octokit/types': 9.3.2
      universal-user-agent: 6.0.1
    transitivePeerDependencies:
      - encoding
    dev: true

  /@octokit/openapi-types@18.1.1:
    resolution: {integrity: sha512-VRaeH8nCDtF5aXWnjPuEMIYf1itK/s3JYyJcWFJT8X9pSNnBtriDf7wlEWsGuhPLl4QIH4xM8fqTXDwJ3Mu6sw==}
    dev: true

  /@octokit/plugin-paginate-rest@6.1.2(@octokit/core@4.2.4):
    resolution: {integrity: sha512-qhrmtQeHU/IivxucOV1bbI/xZyC/iOBhclokv7Sut5vnejAIAEXVcGQeRpQlU39E0WwK9lNvJHphHri/DB6lbQ==}
    engines: {node: '>= 14'}
    peerDependencies:
      '@octokit/core': '>=4'
    dependencies:
      '@octokit/core': 4.2.4
      '@octokit/tsconfig': 1.0.2
      '@octokit/types': 9.3.2
    dev: true

  /@octokit/plugin-request-log@1.0.4(@octokit/core@4.2.4):
    resolution: {integrity: sha512-mLUsMkgP7K/cnFEw07kWqXGF5LKrOkD+lhCrKvPHXWDywAwuDUeDwWBpc69XK3pNX0uKiVt8g5z96PJ6z9xCFA==}
    peerDependencies:
      '@octokit/core': '>=3'
    dependencies:
      '@octokit/core': 4.2.4
    dev: true

  /@octokit/plugin-rest-endpoint-methods@7.2.3(@octokit/core@4.2.4):
    resolution: {integrity: sha512-I5Gml6kTAkzVlN7KCtjOM+Ruwe/rQppp0QU372K1GP7kNOYEKe8Xn5BW4sE62JAHdwpq95OQK/qGNyKQMUzVgA==}
    engines: {node: '>= 14'}
    peerDependencies:
      '@octokit/core': '>=3'
    dependencies:
      '@octokit/core': 4.2.4
      '@octokit/types': 10.0.0
    dev: true

  /@octokit/request-error@3.0.3:
    resolution: {integrity: sha512-crqw3V5Iy2uOU5Np+8M/YexTlT8zxCfI+qu+LxUB7SZpje4Qmx3mub5DfEKSO8Ylyk0aogi6TYdf6kxzh2BguQ==}
    engines: {node: '>= 14'}
    dependencies:
      '@octokit/types': 9.3.2
      deprecation: 2.3.1
      once: 1.4.0
    dev: true

  /@octokit/request@6.2.8:
    resolution: {integrity: sha512-ow4+pkVQ+6XVVsekSYBzJC0VTVvh/FCTUUgTsboGq+DTeWdyIFV8WSCdo0RIxk6wSkBTHqIK1mYuY7nOBXOchw==}
    engines: {node: '>= 14'}
    dependencies:
      '@octokit/endpoint': 7.0.6
      '@octokit/request-error': 3.0.3
      '@octokit/types': 9.3.2
      is-plain-object: 5.0.0
      node-fetch: 2.7.0
      universal-user-agent: 6.0.1
    transitivePeerDependencies:
      - encoding
    dev: true

  /@octokit/rest@19.0.13:
    resolution: {integrity: sha512-/EzVox5V9gYGdbAI+ovYj3nXQT1TtTHRT+0eZPcuC05UFSWO3mdO9UY1C0i2eLF9Un1ONJkAk+IEtYGAC+TahA==}
    engines: {node: '>= 14'}
    dependencies:
      '@octokit/core': 4.2.4
      '@octokit/plugin-paginate-rest': 6.1.2(@octokit/core@4.2.4)
      '@octokit/plugin-request-log': 1.0.4(@octokit/core@4.2.4)
      '@octokit/plugin-rest-endpoint-methods': 7.2.3(@octokit/core@4.2.4)
    transitivePeerDependencies:
      - encoding
    dev: true

  /@octokit/tsconfig@1.0.2:
    resolution: {integrity: sha512-I0vDR0rdtP8p2lGMzvsJzbhdOWy405HcGovrspJ8RRibHnyRgggUSNO5AIox5LmqiwmatHKYsvj6VGFHkqS7lA==}
    dev: true

  /@octokit/types@10.0.0:
    resolution: {integrity: sha512-Vm8IddVmhCgU1fxC1eyinpwqzXPEYu0NrYzD3YZjlGjyftdLBTeqNblRC0jmJmgxbJIsQlyogVeGnrNaaMVzIg==}
    dependencies:
      '@octokit/openapi-types': 18.1.1
    dev: true

  /@octokit/types@9.3.2:
    resolution: {integrity: sha512-D4iHGTdAnEEVsB8fl95m1hiz7D5YiRdQ9b/OEb3BYRVwbLsGHcRVPz+u+BgRLNk0Q0/4iZCBqDN96j2XNxfXrA==}
    dependencies:
      '@octokit/openapi-types': 18.1.1
    dev: true

  /@one-ini/wasm@0.1.1:
    resolution: {integrity: sha512-XuySG1E38YScSJoMlqovLru4KTUNSjgVTIjyh7qMX6aNN5HY5Ct5LhRJdxO79JtTzKfzV/bnWpz+zquYrISsvw==}
    dev: true

  /@pkgjs/parseargs@0.11.0:
    resolution: {integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==}
    engines: {node: '>=14'}
    requiresBuild: true
    dev: true
    optional: true

  /@sinonjs/commons@1.8.6:
    resolution: {integrity: sha512-Ky+XkAkqPZSm3NLBeUng77EBQl3cmeJhITaGHdYH8kjVB+aun3S4XBRti2zt17mtt0mIUDiNxYeoJm6drVvBJQ==}
    dependencies:
      type-detect: 4.0.8
    dev: true

  /@sinonjs/commons@3.0.1:
    resolution: {integrity: sha512-K3mCHKQ9sVh8o1C9cxkwxaOmXoAMlDxC1mYyHrjqOWEcBjYr76t96zL2zlj5dUGZ3HSw240X1qgH3Mjf1yJWpQ==}
    dependencies:
      type-detect: 4.0.8
    dev: true

  /@sinonjs/fake-timers@11.3.1:
    resolution: {integrity: sha512-EVJO7nW5M/F5Tur0Rf2z/QoMo+1Ia963RiMtapiQrEWvY0iBUvADo8Beegwjpnle5BHkyHuoxSTW3jF43H1XRA==}
    dependencies:
      '@sinonjs/commons': 3.0.1
    dev: true

  /@sinonjs/fake-timers@9.1.2:
    resolution: {integrity: sha512-BPS4ynJW/o92PUR4wgriz2Ud5gpST5vz6GQfMixEDK0Z8ZCUv2M7SkBLykH56T++Xs+8ln9zTGbOvNGIe02/jw==}
    dependencies:
      '@sinonjs/commons': 1.8.6
    dev: true

  /@sinonjs/samsam@6.1.3:
    resolution: {integrity: sha512-nhOb2dWPeb1sd3IQXL/dVPnKHDOAFfvichtBf4xV00/rU1QbPCQqKMbvIheIjqwVjh7qIgf2AHTHi391yMOMpQ==}
    dependencies:
      '@sinonjs/commons': 1.8.6
      lodash.get: 4.4.2
      type-detect: 4.1.0
    dev: true

  /@sinonjs/text-encoding@0.7.3:
    resolution: {integrity: sha512-DE427ROAphMQzU4ENbliGYrBSYPXF+TtLg9S8vzeA+OF4ZKzoDdzfL8sxuMUGS/lgRhM6j1URSk9ghf7Xo1tyA==}
    dev: true

  /@socket.io/component-emitter@3.1.2:
    resolution: {integrity: sha512-9BCxFwvbGg/RsZK9tjXd8s4UcwR0MWeFQ1XEKIQVVvAGJyINdrqKMcTRyLoK8Rse1GjzLV9cwjWV1olXRWEXVA==}
    dev: true

  /@tootallnate/once@1.1.2:
    resolution: {integrity: sha512-RbzJvlNzmRq5c3O09UipeuXno4tA1FE6ikOjxZK0tuxVv3412l64l5t1W5pj4+rJq9vpkm/kwiR07aZXnsKPxw==}
    engines: {node: '>= 6'}
    dev: true

  /@trysound/sax@0.2.0:
    resolution: {integrity: sha512-L7z9BgrNEcYyUYtF+HaEfiS5ebkh9jXqbszz7pC0hRBPaatV0XjSD3+eHrpqFemQfgwiFF0QPIarnIihIDn7OA==}
    engines: {node: '>=10.13.0'}
    dev: true

  /@types/cookie@0.4.1:
    resolution: {integrity: sha512-XW/Aa8APYr6jSVVA1y/DEIZX0/GMKLEVekNG727R8cs56ahETkRAy/3DR7+fJyh7oUgGwNQaRfXCun0+KbWY7Q==}
    dev: true

  /@types/cors@2.8.17:
    resolution: {integrity: sha512-8CGDvrBj1zgo2qE+oS3pOCyYNqCPryMWY2bGfwA0dcfopWGgxs+78df0Rs3rc9THP4JkOhLsAa+15VdpAqkcUA==}
    dependencies:
      '@types/node': 22.5.1
    dev: true

  /@types/estree@1.0.5:
    resolution: {integrity: sha512-/kYRxGDLWzHOB7q+wtSUQlFrtcdUccpfy+X+9iMBpHK8QLLhx2wIPYuS5DYtR9Wa/YlZAbIovy7qVdB1Aq6Lyw==}
    dev: true

  /@types/glob@7.2.0:
    resolution: {integrity: sha512-ZUxbzKl0IfJILTS6t7ip5fQQM/J3TJYubDm3nMbgubNNYS62eXeUpoLUC8/7fJNiFYHTrGPQn7hspDUzIHX3UA==}
    dependencies:
      '@types/minimatch': 5.1.2
      '@types/node': 22.5.1
    dev: true

  /@types/json-schema@7.0.15:
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}
    dev: true

  /@types/lodash-es@4.17.12:
    resolution: {integrity: sha512-0NgftHUcV4v34VhXm8QBSftKVXtbkBG3ViCjs6+eJ5a6y6Mi/jiFGPc1sC7QK+9BFhWrURE3EOggmWaSxL9OzQ==}
    dependencies:
      '@types/lodash': 4.17.7
    dev: true

  /@types/lodash@4.17.7:
    resolution: {integrity: sha512-8wTvZawATi/lsmNu10/j2hk1KEP0IvjubqPE3cu1Xz7xfXXt5oCq3SNUz4fMIP4XGF9Ky+Ue2tBA3hcS7LSBlA==}
    dev: true

  /@types/minimatch@5.1.2:
    resolution: {integrity: sha512-K0VQKziLUWkVKiRVrx4a40iPaxTUefQmjtkQofBkYRcoaaL/8rhwDWww9qWbrgicNOgnpIsMxyNIUM4+n6dUIA==}
    dev: true

  /@types/minimist@1.2.5:
    resolution: {integrity: sha512-hov8bUuiLiyFPGyFPE1lwWhmzYbirOXQNNo40+y3zow8aFVTeyn3VWL0VFFfdNddA8S4Vf0Tc062rzyNr7Paag==}
    dev: true

  /@types/node@22.5.1:
    resolution: {integrity: sha512-KkHsxej0j9IW1KKOOAA/XBA0z08UFSrRQHErzEfA3Vgq57eXIMYboIlHJuYIfd+lwCQjtKqUu3UnmKbtUc9yRw==}
    dependencies:
      undici-types: 6.19.8
    dev: true

  /@types/normalize-package-data@2.4.4:
    resolution: {integrity: sha512-37i+OaWTh9qeK4LSHPsyRC7NahnGotNuZvjLSgcPzblpHB3rrCJxAOgI5gCdKm7coonsaX1Of0ILiTcnZjbfxA==}
    dev: true

  /@types/parse-json@4.0.2:
    resolution: {integrity: sha512-dISoDXWWQwUquiKsyZ4Ng+HX2KsPL7LyHKHQwgGFEA3IaKac4Obd+h2a/a6waisAoepJlBcx9paWqjA8/HVjCw==}
    dev: true

  /@types/semver@7.5.8:
    resolution: {integrity: sha512-I8EUhyrgfLrcTkzV3TSsGyl1tSuPrEDzr0yd5m90UgNxQkyDXULk3b6MlQqTCpZpNtWe1K0hzclnZkTcLBe2UQ==}
    dev: true

  /@typescript-eslint/eslint-plugin@5.62.0(@typescript-eslint/parser@5.62.0)(eslint@7.32.0)(typescript@4.9.5):
    resolution: {integrity: sha512-TiZzBSJja/LbhNPvk6yc0JrX9XqhQ0hdh6M2svYfsHGejaKFIAGd9MQ+ERIMzLGlN/kZoYIgdxFV0PuljTKXag==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      '@typescript-eslint/parser': ^5.0.0
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@eslint-community/regexpp': 4.11.0
      '@typescript-eslint/parser': 5.62.0(eslint@7.32.0)(typescript@4.9.5)
      '@typescript-eslint/scope-manager': 5.62.0
      '@typescript-eslint/type-utils': 5.62.0(eslint@7.32.0)(typescript@4.9.5)
      '@typescript-eslint/utils': 5.62.0(eslint@7.32.0)(typescript@4.9.5)
      debug: 4.3.6
      eslint: 7.32.0
      graphemer: 1.4.0
      ignore: 5.3.2
      natural-compare-lite: 1.4.0
      semver: 7.6.3
      tsutils: 3.21.0(typescript@4.9.5)
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/parser@5.62.0(eslint@7.32.0)(typescript@4.9.5):
    resolution: {integrity: sha512-VlJEV0fOQ7BExOsHYAGrgbEiZoi8D+Bl2+f6V2RrXerRSylnp+ZBHmPvaIa8cz0Ajx7WO7Z5RqfgYg7ED1nRhA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/scope-manager': 5.62.0
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/typescript-estree': 5.62.0(typescript@4.9.5)
      debug: 4.3.6
      eslint: 7.32.0
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/scope-manager@5.62.0:
    resolution: {integrity: sha512-VXuvVvZeQCQb5Zgf4HAxc04q5j+WrNAtNh9OwCsCgpKqESMTu3tF/jhZ3xG6T4NZwWl65Bg8KuS2uEvhSfLl0w==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/visitor-keys': 5.62.0
    dev: true

  /@typescript-eslint/type-utils@5.62.0(eslint@7.32.0)(typescript@4.9.5):
    resolution: {integrity: sha512-xsSQreu+VnfbqQpW5vnCJdq1Z3Q0U31qiWmRhr98ONQmcp/yhiPJFPq8MXiJVLiksmOKSjIldZzkebzHuCGzew==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: '*'
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/typescript-estree': 5.62.0(typescript@4.9.5)
      '@typescript-eslint/utils': 5.62.0(eslint@7.32.0)(typescript@4.9.5)
      debug: 4.3.6
      eslint: 7.32.0
      tsutils: 3.21.0(typescript@4.9.5)
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/types@5.62.0:
    resolution: {integrity: sha512-87NVngcbVXUahrRTqIK27gD2t5Cu1yuCXxbLcFtCzZGlfyVWWh8mLHkoxzjsB6DDNnvdL+fW8MiwPEJyGJQDgQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dev: true

  /@typescript-eslint/typescript-estree@5.62.0(typescript@4.9.5):
    resolution: {integrity: sha512-CmcQ6uY7b9y694lKdRB8FEel7JbU/40iSAPomu++SjLMntB+2Leay2LO6i8VnJk58MtE9/nQSFIH6jpyRWyYzA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/visitor-keys': 5.62.0
      debug: 4.3.6
      globby: 11.1.0
      is-glob: 4.0.3
      semver: 7.6.3
      tsutils: 3.21.0(typescript@4.9.5)
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/utils@5.62.0(eslint@7.32.0)(typescript@4.9.5):
    resolution: {integrity: sha512-n8oxjeb5aIbPFEtmQxQYOLI0i9n5ySBEY/ZEHHZqKQSFnxio1rv6dthascc9dLuwrL0RC5mPCxB7vnAVGAYWAQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      '@eslint-community/eslint-utils': 4.4.0(eslint@7.32.0)
      '@types/json-schema': 7.0.15
      '@types/semver': 7.5.8
      '@typescript-eslint/scope-manager': 5.62.0
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/typescript-estree': 5.62.0(typescript@4.9.5)
      eslint: 7.32.0
      eslint-scope: 5.1.1
      semver: 7.6.3
    transitivePeerDependencies:
      - supports-color
      - typescript
    dev: true

  /@typescript-eslint/visitor-keys@5.62.0:
    resolution: {integrity: sha512-07ny+LHRzQXepkGg6w0mFY41fVUNBrL2Roj/++7V1txKugfjm/Ci/qSND03r2RhlJhJYMcTn9AhhSSqQp0Ysyw==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      '@typescript-eslint/types': 5.62.0
      eslint-visitor-keys: 3.4.3
    dev: true

  /@ungap/promise-all-settled@1.1.2:
    resolution: {integrity: sha512-sL/cEvJWAnClXw0wHk85/2L0G6Sj8UB0Ctc1TEMbKSsmpRosqhwj9gWgFRZSrBr2f9tiXISwNhCPmlfqUqyb9Q==}
    dev: true

  /@vue/compiler-core@3.4.38:
    resolution: {integrity: sha512-8IQOTCWnLFqfHzOGm9+P8OPSEDukgg3Huc92qSG49if/xI2SAwLHQO2qaPQbjCWPBcQoO1WYfXfTACUrWV3c5A==}
    dependencies:
      '@babel/parser': 7.25.4
      '@vue/shared': 3.4.38
      entities: 4.5.0
      estree-walker: 2.0.2
      source-map-js: 1.2.0
    dev: true

  /@vue/compiler-dom@3.4.38:
    resolution: {integrity: sha512-Osc/c7ABsHXTsETLgykcOwIxFktHfGSUDkb05V61rocEfsFDcjDLH/IHJSNJP+/Sv9KeN2Lx1V6McZzlSb9EhQ==}
    dependencies:
      '@vue/compiler-core': 3.4.38
      '@vue/shared': 3.4.38
    dev: true

  /@vue/compiler-sfc@3.4.38:
    resolution: {integrity: sha512-s5QfZ+9PzPh3T5H4hsQDJtI8x7zdJaew/dCGgqZ2630XdzaZ3AD8xGZfBqpT8oaD/p2eedd+pL8tD5vvt5ZYJQ==}
    dependencies:
      '@babel/parser': 7.25.4
      '@vue/compiler-core': 3.4.38
      '@vue/compiler-dom': 3.4.38
      '@vue/compiler-ssr': 3.4.38
      '@vue/shared': 3.4.38
      estree-walker: 2.0.2
      magic-string: 0.30.11
      postcss: 8.4.41
      source-map-js: 1.2.0
    dev: true

  /@vue/compiler-ssr@3.4.38:
    resolution: {integrity: sha512-YXznKFQ8dxYpAz9zLuVvfcXhc31FSPFDcqr0kyujbOwNhlmaNvL2QfIy+RZeJgSn5Fk54CWoEUeW+NVBAogGaw==}
    dependencies:
      '@vue/compiler-dom': 3.4.38
      '@vue/shared': 3.4.38
    dev: true

  /@vue/reactivity@3.4.38:
    resolution: {integrity: sha512-4vl4wMMVniLsSYYeldAKzbk72+D3hUnkw9z8lDeJacTxAkXeDAP1uE9xr2+aKIN0ipOL8EG2GPouVTH6yF7Gnw==}
    dependencies:
      '@vue/shared': 3.4.38
    dev: true

  /@vue/runtime-core@3.4.38:
    resolution: {integrity: sha512-21z3wA99EABtuf+O3IhdxP0iHgkBs1vuoCAsCKLVJPEjpVqvblwBnTj42vzHRlWDCyxu9ptDm7sI2ZMcWrQqlA==}
    dependencies:
      '@vue/reactivity': 3.4.38
      '@vue/shared': 3.4.38
    dev: true

  /@vue/runtime-dom@3.4.38:
    resolution: {integrity: sha512-afZzmUreU7vKwKsV17H1NDThEEmdYI+GCAK/KY1U957Ig2NATPVjCROv61R19fjZNzMmiU03n79OMnXyJVN0UA==}
    dependencies:
      '@vue/reactivity': 3.4.38
      '@vue/runtime-core': 3.4.38
      '@vue/shared': 3.4.38
      csstype: 3.1.3
    dev: true

  /@vue/server-renderer@3.4.38(vue@3.4.38):
    resolution: {integrity: sha512-NggOTr82FbPEkkUvBm4fTGcwUY8UuTsnWC/L2YZBmvaQ4C4Jl/Ao4HHTB+l7WnFCt5M/dN3l0XLuyjzswGYVCA==}
    peerDependencies:
      vue: 3.4.38
    dependencies:
      '@vue/compiler-ssr': 3.4.38
      '@vue/shared': 3.4.38
      vue: 3.4.38(typescript@4.9.5)
    dev: true

  /@vue/shared@3.4.38:
    resolution: {integrity: sha512-q0xCiLkuWWQLzVrecPb0RMsNWyxICOjPrcrwxTUEHb1fsnvni4dcuyG7RT/Ie7VPTvnjzIaWzRMUBsrqNj/hhw==}
    dev: true

  /@vue/test-utils@2.4.6:
    resolution: {integrity: sha512-FMxEjOpYNYiFe0GkaHsnJPXFHxQ6m4t8vI/ElPGpMWxZKpmRvQ33OIrvRXemy6yha03RxhOlQuy+gZMC3CQSow==}
    dependencies:
      js-beautify: 1.15.1
      vue-component-type-helpers: 2.0.29
    dev: true

  /@webassemblyjs/ast@1.12.1:
    resolution: {integrity: sha512-EKfMUOPRRUTy5UII4qJDGPpqfwjOmZ5jeGFwid9mnoqIFK+e0vqoi1qH56JpmZSzEL53jKnNzScdmftJyG5xWg==}
    dependencies:
      '@webassemblyjs/helper-numbers': 1.11.6
      '@webassemblyjs/helper-wasm-bytecode': 1.11.6
    dev: true

  /@webassemblyjs/floating-point-hex-parser@1.11.6:
    resolution: {integrity: sha512-ejAj9hfRJ2XMsNHk/v6Fu2dGS+i4UaXBXGemOfQ/JfQ6mdQg/WXtwleQRLLS4OvfDhv8rYnVwH27YJLMyYsxhw==}
    dev: true

  /@webassemblyjs/helper-api-error@1.11.6:
    resolution: {integrity: sha512-o0YkoP4pVu4rN8aTJgAyj9hC2Sv5UlkzCHhxqWj8butaLvnpdc2jOwh4ewE6CX0txSfLn/UYaV/pheS2Txg//Q==}
    dev: true

  /@webassemblyjs/helper-buffer@1.12.1:
    resolution: {integrity: sha512-nzJwQw99DNDKr9BVCOZcLuJJUlqkJh+kVzVl6Fmq/tI5ZtEyWT1KZMyOXltXLZJmDtvLCDgwsyrkohEtopTXCw==}
    dev: true

  /@webassemblyjs/helper-numbers@1.11.6:
    resolution: {integrity: sha512-vUIhZ8LZoIWHBohiEObxVm6hwP034jwmc9kuq5GdHZH0wiLVLIPcMCdpJzG4C11cHoQ25TFIQj9kaVADVX7N3g==}
    dependencies:
      '@webassemblyjs/floating-point-hex-parser': 1.11.6
      '@webassemblyjs/helper-api-error': 1.11.6
      '@xtuc/long': 4.2.2
    dev: true

  /@webassemblyjs/helper-wasm-bytecode@1.11.6:
    resolution: {integrity: sha512-sFFHKwcmBprO9e7Icf0+gddyWYDViL8bpPjJJl0WHxCdETktXdmtWLGVzoHbqUcY4Be1LkNfwTmXOJUFZYSJdA==}
    dev: true

  /@webassemblyjs/helper-wasm-section@1.12.1:
    resolution: {integrity: sha512-Jif4vfB6FJlUlSbgEMHUyk1j234GTNG9dBJ4XJdOySoj518Xj0oGsNi59cUQF4RRMS9ouBUxDDdyBVfPTypa5g==}
    dependencies:
      '@webassemblyjs/ast': 1.12.1
      '@webassemblyjs/helper-buffer': 1.12.1
      '@webassemblyjs/helper-wasm-bytecode': 1.11.6
      '@webassemblyjs/wasm-gen': 1.12.1
    dev: true

  /@webassemblyjs/ieee754@1.11.6:
    resolution: {integrity: sha512-LM4p2csPNvbij6U1f19v6WR56QZ8JcHg3QIJTlSwzFcmx6WSORicYj6I63f9yU1kEUtrpG+kjkiIAkevHpDXrg==}
    dependencies:
      '@xtuc/ieee754': 1.2.0
    dev: true

  /@webassemblyjs/leb128@1.11.6:
    resolution: {integrity: sha512-m7a0FhE67DQXgouf1tbN5XQcdWoNgaAuoULHIfGFIEVKA6tu/edls6XnIlkmS6FrXAquJRPni3ZZKjw6FSPjPQ==}
    dependencies:
      '@xtuc/long': 4.2.2
    dev: true

  /@webassemblyjs/utf8@1.11.6:
    resolution: {integrity: sha512-vtXf2wTQ3+up9Zsg8sa2yWiQpzSsMyXj0qViVP6xKGCUT8p8YJ6HqI7l5eCnWx1T/FYdsv07HQs2wTFbbof/RA==}
    dev: true

  /@webassemblyjs/wasm-edit@1.12.1:
    resolution: {integrity: sha512-1DuwbVvADvS5mGnXbE+c9NfA8QRcZ6iKquqjjmR10k6o+zzsRVesil54DKexiowcFCPdr/Q0qaMgB01+SQ1u6g==}
    dependencies:
      '@webassemblyjs/ast': 1.12.1
      '@webassemblyjs/helper-buffer': 1.12.1
      '@webassemblyjs/helper-wasm-bytecode': 1.11.6
      '@webassemblyjs/helper-wasm-section': 1.12.1
      '@webassemblyjs/wasm-gen': 1.12.1
      '@webassemblyjs/wasm-opt': 1.12.1
      '@webassemblyjs/wasm-parser': 1.12.1
      '@webassemblyjs/wast-printer': 1.12.1
    dev: true

  /@webassemblyjs/wasm-gen@1.12.1:
    resolution: {integrity: sha512-TDq4Ojh9fcohAw6OIMXqiIcTq5KUXTGRkVxbSo1hQnSy6lAM5GSdfwWeSxpAo0YzgsgF182E/U0mDNhuA0tW7w==}
    dependencies:
      '@webassemblyjs/ast': 1.12.1
      '@webassemblyjs/helper-wasm-bytecode': 1.11.6
      '@webassemblyjs/ieee754': 1.11.6
      '@webassemblyjs/leb128': 1.11.6
      '@webassemblyjs/utf8': 1.11.6
    dev: true

  /@webassemblyjs/wasm-opt@1.12.1:
    resolution: {integrity: sha512-Jg99j/2gG2iaz3hijw857AVYekZe2SAskcqlWIZXjji5WStnOpVoat3gQfT/Q5tb2djnCjBtMocY/Su1GfxPBg==}
    dependencies:
      '@webassemblyjs/ast': 1.12.1
      '@webassemblyjs/helper-buffer': 1.12.1
      '@webassemblyjs/wasm-gen': 1.12.1
      '@webassemblyjs/wasm-parser': 1.12.1
    dev: true

  /@webassemblyjs/wasm-parser@1.12.1:
    resolution: {integrity: sha512-xikIi7c2FHXysxXe3COrVUPSheuBtpcfhbpFj4gmu7KRLYOzANztwUU0IbsqvMqzuNK2+glRGWCEqZo1WCLyAQ==}
    dependencies:
      '@webassemblyjs/ast': 1.12.1
      '@webassemblyjs/helper-api-error': 1.11.6
      '@webassemblyjs/helper-wasm-bytecode': 1.11.6
      '@webassemblyjs/ieee754': 1.11.6
      '@webassemblyjs/leb128': 1.11.6
      '@webassemblyjs/utf8': 1.11.6
    dev: true

  /@webassemblyjs/wast-printer@1.12.1:
    resolution: {integrity: sha512-+X4WAlOisVWQMikjbcvY2e0rwPsKQ9F688lksZhBcPycBBuii3O7m8FACbDMWDojpAqvjIncrG8J0XHKyQfVeA==}
    dependencies:
      '@webassemblyjs/ast': 1.12.1
      '@xtuc/long': 4.2.2
    dev: true

  /@webpack-cli/configtest@1.2.0(webpack-cli@4.10.0)(webpack@5.94.0):
    resolution: {integrity: sha512-4FB8Tj6xyVkyqjj1OaTqCjXYULB9FMkqQ8yGrZjRDrYh0nOE+7Lhs45WioWQQMV+ceFlE368Ukhe6xdvJM9Egg==}
    peerDependencies:
      webpack: 4.x.x || 5.x.x
      webpack-cli: 4.x.x
    dependencies:
      webpack: 5.94.0(webpack-cli@4.10.0)
      webpack-cli: 4.10.0(webpack@5.94.0)
    dev: true

  /@webpack-cli/info@1.5.0(webpack-cli@4.10.0):
    resolution: {integrity: sha512-e8tSXZpw2hPl2uMJY6fsMswaok5FdlGNRTktvFk2sD8RjH0hE2+XistawJx1vmKteh4NmGmNUrp+Tb2w+udPcQ==}
    peerDependencies:
      webpack-cli: 4.x.x
    dependencies:
      envinfo: 7.13.0
      webpack-cli: 4.10.0(webpack@5.94.0)
    dev: true

  /@webpack-cli/serve@1.7.0(webpack-cli@4.10.0):
    resolution: {integrity: sha512-oxnCNGj88fL+xzV+dacXs44HcDwf1ovs3AuEzvP7mqXw7fQntqIhQ1BRmynh4qEKQSSSRSWVyXRjmTbZIX9V2Q==}
    peerDependencies:
      webpack-cli: 4.x.x
      webpack-dev-server: '*'
    peerDependenciesMeta:
      webpack-dev-server:
        optional: true
    dependencies:
      webpack-cli: 4.10.0(webpack@5.94.0)
    dev: true

  /@xtuc/ieee754@1.2.0:
    resolution: {integrity: sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==}
    dev: true

  /@xtuc/long@4.2.2:
    resolution: {integrity: sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==}
    dev: true

  /JSONStream@1.3.5:
    resolution: {integrity: sha512-E+iruNOY8VV9s4JEbe1aNEm6MiszPRr/UfcHMz0TQh1BXSxHK+ASV1R6W4HpjBhSeS+54PIsAMCBmwD06LLsqQ==}
    hasBin: true
    dependencies:
      jsonparse: 1.3.1
      through: 2.3.8
    dev: true

  /abab@2.0.6:
    resolution: {integrity: sha512-j2afSsaIENvHZN2B8GOpF566vZ5WVk5opAiMTvWgaQT8DkbOqsTfvNAvHoRGU2zzP8cPoqys+xHTRDWW8L+/BA==}
    deprecated: Use your platform's native atob() and btoa() methods instead
    dev: true

  /abbrev@2.0.0:
    resolution: {integrity: sha512-6/mh1E2u2YgEsCHdY0Yx5oW+61gZU+1vXaoiHHrpKeuRNNgFvS+/jrwHiQhB5apAf5oB7UB7E19ol2R2LKH8hQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}
    dev: true

  /accepts@1.3.8:
    resolution: {integrity: sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3
    dev: true

  /acorn-globals@6.0.0:
    resolution: {integrity: sha512-ZQl7LOWaF5ePqqcX4hLuv/bLXYQNfNWw2c0/yX/TsPRKamzHcTGQnlCjHT3TsmkOUVEPS3crCxiPfdzE/Trlhg==}
    dependencies:
      acorn: 7.4.1
      acorn-walk: 7.2.0
    dev: true

  /acorn-import-attributes@1.9.5(acorn@8.12.1):
    resolution: {integrity: sha512-n02Vykv5uA3eHGM/Z2dQrcD56kL8TyDb2p1+0P83PClMnC/nc+anbQRhIOWnSq4Ke/KvDPrY3C9hDtC/A3eHnQ==}
    peerDependencies:
      acorn: ^8
    dependencies:
      acorn: 8.12.1
    dev: true

  /acorn-jsx@5.3.2(acorn@7.4.1):
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      acorn: 7.4.1
    dev: true

  /acorn-jsx@5.3.2(acorn@8.12.1):
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      acorn: 8.12.1
    dev: true

  /acorn-walk@7.2.0:
    resolution: {integrity: sha512-OPdCF6GsMIP+Az+aWfAAOEt2/+iVDKE7oy6lJ098aoe59oAmK76qV6Gw60SbZ8jHuG2wH058GF4pLFbYamYrVA==}
    engines: {node: '>=0.4.0'}
    dev: true

  /acorn@7.4.1:
    resolution: {integrity: sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A==}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: true

  /acorn@8.12.1:
    resolution: {integrity: sha512-tcpGyI9zbizT9JbV6oYE477V6mTlXvvi0T0G3SNIYE2apm/G5huBa1+K89VGeovbg+jycCrfhl3ADxErOuO6Jg==}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: true

  /agent-base@6.0.2:
    resolution: {integrity: sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==}
    engines: {node: '>= 6.0.0'}
    dependencies:
      debug: 4.3.6
    transitivePeerDependencies:
      - supports-color
    dev: true

  /aggregate-error@3.1.0:
    resolution: {integrity: sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==}
    engines: {node: '>=8'}
    dependencies:
      clean-stack: 2.2.0
      indent-string: 4.0.0
    dev: true

  /ajv-formats@2.1.1(ajv@8.17.1):
    resolution: {integrity: sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==}
    peerDependencies:
      ajv: ^8.0.0
    peerDependenciesMeta:
      ajv:
        optional: true
    dependencies:
      ajv: 8.17.1
    dev: true

  /ajv-keywords@3.5.2(ajv@6.12.6):
    resolution: {integrity: sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==}
    peerDependencies:
      ajv: ^6.9.1
    dependencies:
      ajv: 6.12.6
    dev: true

  /ajv-keywords@5.1.0(ajv@8.17.1):
    resolution: {integrity: sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw==}
    peerDependencies:
      ajv: ^8.8.2
    dependencies:
      ajv: 8.17.1
      fast-deep-equal: 3.1.3
    dev: true

  /ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1
    dev: true

  /ajv@8.17.1:
    resolution: {integrity: sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==}
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.1
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2
    dev: true

  /ansi-colors@4.1.1:
    resolution: {integrity: sha512-JoX0apGbHaUJBNl6yF+p6JAFYZ666/hhCGKN5t9QFjbJQKUU/g8MNbFDbvfrgKXvI1QpZplPOnwIo99lX/AAmA==}
    engines: {node: '>=6'}
    dev: true

  /ansi-colors@4.1.3:
    resolution: {integrity: sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw==}
    engines: {node: '>=6'}
    dev: true

  /ansi-escapes@4.3.2:
    resolution: {integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==}
    engines: {node: '>=8'}
    dependencies:
      type-fest: 0.21.3
    dev: true

  /ansi-escapes@5.0.0:
    resolution: {integrity: sha512-5GFMVX8HqE/TB+FuBJGuO5XG0WrsA6ptUqoODaT/n9mmUaZFkqnBueB4leqGBCmrUHnCnC4PCZTCd0E7QQ83bA==}
    engines: {node: '>=12'}
    dependencies:
      type-fest: 1.4.0
    dev: true

  /ansi-regex@3.0.1:
    resolution: {integrity: sha512-+O9Jct8wf++lXxxFc4hc8LsjaSq0HFzzL7cVsw8pRDIPdjKD2mT4ytDZlLuSBZ4cLKZFXIrMGO7DbQCtMJJMKw==}
    engines: {node: '>=4'}
    dev: true

  /ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}
    dev: true

  /ansi-regex@6.0.1:
    resolution: {integrity: sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA==}
    engines: {node: '>=12'}
    dev: true

  /ansi-styles@3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}
    dependencies:
      color-convert: 1.9.3
    dev: true

  /ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}
    dependencies:
      color-convert: 2.0.1
    dev: true

  /ansi-styles@6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==}
    engines: {node: '>=12'}
    dev: true

  /anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1
    dev: true

  /argparse@1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==}
    dependencies:
      sprintf-js: 1.0.3
    dev: true

  /argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}
    dev: true

  /array-ify@1.0.0:
    resolution: {integrity: sha512-c5AMf34bKdvPhQ7tBGhqkgKNUzMr4WUs+WDtC2ZUGOUncbxKMTvqxYctiseW3+L4bA8ec+GcZ6/A/FW4m8ukng==}
    dev: true

  /array-union@2.1.0:
    resolution: {integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==}
    engines: {node: '>=8'}
    dev: true

  /arrify@1.0.1:
    resolution: {integrity: sha512-3CYzex9M9FGQjCGMGyi6/31c8GJbgb0qGyrx5HWxPd0aCwh4cB2YjMb2Xf9UuoogrMrlO9cTqnB5rI5GHZTcUA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /asn1@0.2.6:
    resolution: {integrity: sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==}
    dependencies:
      safer-buffer: 2.1.2
    dev: true

  /assert-plus@1.0.0:
    resolution: {integrity: sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==}
    engines: {node: '>=0.8'}
    dev: true

  /assertion-error@1.1.0:
    resolution: {integrity: sha512-jgsaNduz+ndvGyFt3uSuWqvy4lCnIJiovtouQN5JZHOKCS2QuhEdbcQHFhVksz2N2U9hXJo8odG7ETyWlEeuDw==}
    dev: true

  /astral-regex@2.0.0:
    resolution: {integrity: sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ==}
    engines: {node: '>=8'}
    dev: true

  /asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}
    dev: true

  /at-least-node@1.0.0:
    resolution: {integrity: sha512-+q/t7Ekv1EDY2l6Gda6LLiX14rU9TV20Wa3ofeQmwPFZbOMo9DXrLbOjFaaclkXKWidIaopwAObQDqwWtGUjqg==}
    engines: {node: '>= 4.0.0'}
    dev: true

  /aws-sign2@0.7.0:
    resolution: {integrity: sha512-08kcGqnYf/YmjoRhfxyu+CLxBjUtHLXLXX/vUfx9l2LYzG3c1m61nrpyFUZI6zeS+Li/wWMMidD9KgrqtGq3mA==}
    dev: true

  /aws4@1.13.1:
    resolution: {integrity: sha512-u5w79Rd7SU4JaIlA/zFqG+gOiuq25q5VLyZ8E+ijJeILuTxVzZgp2CaGw/UTw6pXYN9XMO9yiqj/nEHmhTG5CA==}
    dev: true

  /babel-loader@8.3.0(@babel/core@7.25.2)(webpack@5.94.0):
    resolution: {integrity: sha512-H8SvsMF+m9t15HNLMipppzkC+Y2Yq+v3SonZyU70RBL/h1gxPkH08Ot8pEE9Z4Kd+czyWJClmFS8qzIP9OZ04Q==}
    engines: {node: '>= 8.9'}
    peerDependencies:
      '@babel/core': ^7.0.0
      webpack: '>=2'
    dependencies:
      '@babel/core': 7.25.2
      find-cache-dir: 3.3.2
      loader-utils: 2.0.4
      make-dir: 3.1.0
      schema-utils: 2.7.1
      webpack: 5.94.0(webpack-cli@4.10.0)
    dev: true

  /babel-plugin-istanbul@6.1.1:
    resolution: {integrity: sha512-Y1IQok9821cC9onCx5otgFfRm7Lm+I+wwxOx738M/WLPZ9Q42m4IG5W0FNX8WLL2gYMZo3JkuXIH2DOpWM+qwA==}
    engines: {node: '>=8'}
    dependencies:
      '@babel/helper-plugin-utils': 7.24.8
      '@istanbuljs/load-nyc-config': 1.1.0
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-instrument: 5.2.1
      test-exclude: 6.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}
    dev: true

  /base64id@2.0.0:
    resolution: {integrity: sha512-lGe34o6EHj9y3Kts9R4ZYs/Gr+6N7MCaMlIFA3F1R2O5/m7K06AxfSeO5530PEERE6/WyEg3lsuyw4GHlPZHog==}
    engines: {node: ^4.5.0 || >= 5.9}
    dev: true

  /bcrypt-pbkdf@1.0.2:
    resolution: {integrity: sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w==}
    dependencies:
      tweetnacl: 0.14.5
    dev: true

  /before-after-hook@2.2.3:
    resolution: {integrity: sha512-NzUnlZexiaH/46WDhANlyR2bXRopNg4F/zuSA3OpZnllCUgRaOF2znDioDWrmbNVsuZk6l9pMquQB38cfBZwkQ==}
    dev: true

  /big.js@5.2.2:
    resolution: {integrity: sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==}
    dev: true

  /binary-extensions@2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==}
    engines: {node: '>=8'}
    dev: true

  /blurhash@2.0.5:
    resolution: {integrity: sha512-cRygWd7kGBQO3VEhPiTgq4Wc43ctsM+o46urrmPOiuAe+07fzlSB9OJVdpgDL0jPqXUVQ9ht7aq7kxOeJHRK+w==}
    dev: true

  /body-parser@1.20.2:
    resolution: {integrity: sha512-ml9pReCu3M61kGlqoTm2umSXTlRTuGTx0bfYj+uIUKKYycG5NtSbeetV3faSU6R7ajOPw0g/J1PvK4qNy7s5bA==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.5
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      on-finished: 2.4.1
      qs: 6.11.0
      raw-body: 2.5.2
      type-is: 1.6.18
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /boolbase@1.0.0:
    resolution: {integrity: sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==}
    dev: true

  /brace-expansion@1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1
    dev: true

  /brace-expansion@2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}
    dependencies:
      balanced-match: 1.0.2
    dev: true

  /braces@3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}
    dependencies:
      fill-range: 7.1.1
    dev: true

  /browser-process-hrtime@1.0.0:
    resolution: {integrity: sha512-9o5UecI3GhkpM6DrXr69PblIuWxPKk9Y0jHBRhdocZ2y7YECBFCsHm79Pr3OyR2AvjhDkabFJaDJMYRazHgsow==}
    dev: true

  /browser-stdout@1.3.1:
    resolution: {integrity: sha512-qhAVI1+Av2X7qelOfAIYwXONood6XlZE/fXaBSmW/T5SzLAmCgzi+eiWE7fUvbHaeNBQH13UftjpXxsfLkMpgw==}
    dev: true

  /browserslist@4.23.3:
    resolution: {integrity: sha512-btwCFJVjI4YWDNfau8RhZ+B1Q/VLoUITrm3RlP6y1tYGWIOa+InuYiRGXUBXo8nA1qKmHMyLB/iVQg5TT4eFoA==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: 1.0.30001653
      electron-to-chromium: 1.5.13
      node-releases: 2.0.18
      update-browserslist-db: 1.1.0(browserslist@4.23.3)
    dev: true

  /buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}
    dev: true

  /bytes@3.1.2:
    resolution: {integrity: sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==}
    engines: {node: '>= 0.8'}
    dev: true

  /cacache@15.3.0:
    resolution: {integrity: sha512-VVdYzXEn+cnbXpFgWs5hTT7OScegHVmLhJIR8Ufqk3iFD6A6j5iSX1KuBTfNEv4tdJWE2PzA6IVFtcLC7fN9wQ==}
    engines: {node: '>= 10'}
    dependencies:
      '@npmcli/fs': 1.1.1
      '@npmcli/move-file': 1.1.2
      chownr: 2.0.0
      fs-minipass: 2.1.0
      glob: 7.2.3
      infer-owner: 1.0.4
      lru-cache: 6.0.0
      minipass: 3.3.6
      minipass-collect: 1.0.2
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      mkdirp: 1.0.4
      p-map: 4.0.0
      promise-inflight: 1.0.1
      rimraf: 3.0.2
      ssri: 8.0.1
      tar: 6.2.1
      unique-filename: 1.1.1
    transitivePeerDependencies:
      - bluebird
    dev: true

  /call-bind@1.0.7:
    resolution: {integrity: sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      set-function-length: 1.2.2
    dev: true

  /callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}
    dev: true

  /camelcase-css@2.0.1:
    resolution: {integrity: sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==}
    engines: {node: '>= 6'}
    dev: true

  /camelcase-keys@6.2.2:
    resolution: {integrity: sha512-YrwaA0vEKazPBkn0ipTiMpSajYDSe+KjQfrjhcBMxJt/znbvlHd8Pw/Vamaz5EB4Wfhs3SUR3Z9mwRu/P3s3Yg==}
    engines: {node: '>=8'}
    dependencies:
      camelcase: 5.3.1
      map-obj: 4.3.0
      quick-lru: 4.0.1
    dev: true

  /camelcase@5.3.1:
    resolution: {integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==}
    engines: {node: '>=6'}
    dev: true

  /camelcase@6.3.0:
    resolution: {integrity: sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==}
    engines: {node: '>=10'}
    dev: true

  /caniuse-api@3.0.0:
    resolution: {integrity: sha512-bsTwuIg/BZZK/vreVTYYbSWoe2F+71P7K5QGEX+pT250DZbfU1MQ5prOKpPR+LL6uWKK3KMwMCAS74QB3Um1uw==}
    dependencies:
      browserslist: 4.23.3
      caniuse-lite: 1.0.30001653
      lodash.memoize: 4.1.2
      lodash.uniq: 4.5.0
    dev: true

  /caniuse-lite@1.0.30001653:
    resolution: {integrity: sha512-XGWQVB8wFQ2+9NZwZ10GxTYC5hk0Fa+q8cSkr0tgvMhYhMHP/QC+WTgrePMDBWiWc/pV+1ik82Al20XOK25Gcw==}
    dev: true

  /caseless@0.12.0:
    resolution: {integrity: sha512-4tYFyifaFfGacoiObjJegolkwSU4xQNGbVgUiNYVUxbQ2x2lUsFvY4hVgVzGiIe6WLOPqycWXA40l+PWsxthUw==}
    dev: true

  /chai@4.5.0:
    resolution: {integrity: sha512-RITGBfijLkBddZvnn8jdqoTypxvqbOLYQkGGxXzeFjVHvudaPw0HNFD9x928/eUwYWd2dPCugVqspGALTZZQKw==}
    engines: {node: '>=4'}
    dependencies:
      assertion-error: 1.1.0
      check-error: 1.0.3
      deep-eql: 4.1.4
      get-func-name: 2.0.2
      loupe: 2.3.7
      pathval: 1.1.1
      type-detect: 4.1.0
    dev: true

  /chalk@2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==}
    engines: {node: '>=4'}
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0
    dev: true

  /chalk@3.0.0:
    resolution: {integrity: sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg==}
    engines: {node: '>=8'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0
    dev: true

  /chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0
    dev: true

  /chardet@0.7.0:
    resolution: {integrity: sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==}
    dev: true

  /check-error@1.0.3:
    resolution: {integrity: sha512-iKEoDYaRmd1mxM90a2OEfWhjsjPpYPuQ+lMYsoxB126+t8fw7ySEO48nmDg5COTjxDI65/Y2OWpeEHk3ZOe8zg==}
    dependencies:
      get-func-name: 2.0.2
    dev: true

  /chokidar@3.5.3:
    resolution: {integrity: sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==}
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3
    dev: true

  /chokidar@3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==}
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3
    dev: true

  /chownr@2.0.0:
    resolution: {integrity: sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==}
    engines: {node: '>=10'}
    dev: true

  /chrome-trace-event@1.0.4:
    resolution: {integrity: sha512-rNjApaLzuwaOTjCiT8lSDdGN1APCiqkChLMJxJPWLunPAt5fy8xgU9/jNOchV84wfIxrA0lRQB7oCT8jrn/wrQ==}
    engines: {node: '>=6.0'}
    dev: true

  /ckeditor5@41.4.2:
    resolution: {integrity: sha512-90k7d3R1B7x3muHOKKOGIomFsSQRG1sPuRHdN6J7WmKZH+BrMQgRkUs66xVRhNjrLPmewwJYdQI42Sb1cA1ILQ==}
    dependencies:
      '@ckeditor/ckeditor5-adapter-ckfinder': 41.4.2
      '@ckeditor/ckeditor5-alignment': 41.4.2
      '@ckeditor/ckeditor5-autoformat': 41.4.2
      '@ckeditor/ckeditor5-autosave': 41.4.2
      '@ckeditor/ckeditor5-basic-styles': 41.4.2
      '@ckeditor/ckeditor5-block-quote': 41.4.2
      '@ckeditor/ckeditor5-build-balloon': 41.4.2
      '@ckeditor/ckeditor5-build-balloon-block': 41.4.2
      '@ckeditor/ckeditor5-build-classic': 41.4.2
      '@ckeditor/ckeditor5-build-decoupled-document': 41.4.2
      '@ckeditor/ckeditor5-build-inline': 41.4.2
      '@ckeditor/ckeditor5-build-multi-root': 41.4.2
      '@ckeditor/ckeditor5-ckbox': 41.4.2
      '@ckeditor/ckeditor5-ckfinder': 41.4.2
      '@ckeditor/ckeditor5-clipboard': 41.4.2
      '@ckeditor/ckeditor5-cloud-services': 41.4.2
      '@ckeditor/ckeditor5-code-block': 41.4.2
      '@ckeditor/ckeditor5-core': 41.4.2
      '@ckeditor/ckeditor5-easy-image': 41.4.2
      '@ckeditor/ckeditor5-editor-balloon': 41.4.2
      '@ckeditor/ckeditor5-editor-classic': 41.4.2
      '@ckeditor/ckeditor5-editor-decoupled': 41.4.2
      '@ckeditor/ckeditor5-editor-inline': 41.4.2
      '@ckeditor/ckeditor5-editor-multi-root': 41.4.2
      '@ckeditor/ckeditor5-engine': 41.4.2
      '@ckeditor/ckeditor5-enter': 41.4.2
      '@ckeditor/ckeditor5-essentials': 41.4.2
      '@ckeditor/ckeditor5-find-and-replace': 41.4.2
      '@ckeditor/ckeditor5-font': 41.4.2
      '@ckeditor/ckeditor5-heading': 41.4.2
      '@ckeditor/ckeditor5-highlight': 41.4.2
      '@ckeditor/ckeditor5-horizontal-line': 41.4.2
      '@ckeditor/ckeditor5-html-embed': 41.4.2
      '@ckeditor/ckeditor5-html-support': 41.4.2
      '@ckeditor/ckeditor5-image': 41.4.2
      '@ckeditor/ckeditor5-indent': 41.4.2
      '@ckeditor/ckeditor5-language': 41.4.2
      '@ckeditor/ckeditor5-link': 41.4.2
      '@ckeditor/ckeditor5-list': 41.4.2
      '@ckeditor/ckeditor5-markdown-gfm': 41.4.2
      '@ckeditor/ckeditor5-media-embed': 41.4.2
      '@ckeditor/ckeditor5-mention': 41.4.2
      '@ckeditor/ckeditor5-minimap': 41.4.2
      '@ckeditor/ckeditor5-page-break': 41.4.2
      '@ckeditor/ckeditor5-paragraph': 41.4.2
      '@ckeditor/ckeditor5-paste-from-office': 41.4.2
      '@ckeditor/ckeditor5-remove-format': 41.4.2
      '@ckeditor/ckeditor5-restricted-editing': 41.4.2
      '@ckeditor/ckeditor5-select-all': 41.4.2
      '@ckeditor/ckeditor5-show-blocks': 41.4.2
      '@ckeditor/ckeditor5-source-editing': 41.4.2
      '@ckeditor/ckeditor5-special-characters': 41.4.2
      '@ckeditor/ckeditor5-style': 41.4.2
      '@ckeditor/ckeditor5-table': 41.4.2
      '@ckeditor/ckeditor5-theme-lark': 41.4.2
      '@ckeditor/ckeditor5-typing': 41.4.2
      '@ckeditor/ckeditor5-ui': 41.4.2
      '@ckeditor/ckeditor5-undo': 41.4.2
      '@ckeditor/ckeditor5-upload': 41.4.2
      '@ckeditor/ckeditor5-utils': 41.4.2
      '@ckeditor/ckeditor5-watchdog': 41.4.2
      '@ckeditor/ckeditor5-widget': 41.4.2
      '@ckeditor/ckeditor5-word-count': 41.4.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /clean-stack@2.2.0:
    resolution: {integrity: sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==}
    engines: {node: '>=6'}
    dev: true

  /cli-columns@4.0.0:
    resolution: {integrity: sha512-XW2Vg+w+L9on9wtwKpyzluIPCWXjaBahI7mTcYjx+BVIYD9c3yqcv/yKC7CmdCZat4rq2yiE1UMSJC5ivKfMtQ==}
    engines: {node: '>= 10'}
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: true

  /cli-cursor@3.1.0:
    resolution: {integrity: sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==}
    engines: {node: '>=8'}
    dependencies:
      restore-cursor: 3.1.0
    dev: true

  /cli-cursor@4.0.0:
    resolution: {integrity: sha512-VGtlMu3x/4DOtIUwEkRezxUZ2lBacNJCHash0N0WeZDBS+7Ux1dm3XWAgWYxLJFMMdOeXMHXorshEFhbMSGelg==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dependencies:
      restore-cursor: 4.0.0
    dev: true

  /cli-spinners@2.9.2:
    resolution: {integrity: sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==}
    engines: {node: '>=6'}
    dev: true

  /cli-table@0.3.11:
    resolution: {integrity: sha512-IqLQi4lO0nIB4tcdTpN4LCB9FI3uqrJZK7RC515EnhZ6qBaglkIgICb1wjeAqpdoOabm1+SuQtkXIPdYC93jhQ==}
    engines: {node: '>= 0.2.0'}
    dependencies:
      colors: 1.0.3
    dev: true

  /cli-truncate@2.1.0:
    resolution: {integrity: sha512-n8fOixwDD6b/ObinzTrp1ZKFzbgvKZvuz/TvejnLn1aQfC6r52XEx85FmuC+3HI+JM7coBRXUvNqEU2PHVrHpg==}
    engines: {node: '>=8'}
    dependencies:
      slice-ansi: 3.0.0
      string-width: 4.2.3
    dev: true

  /cli-truncate@3.1.0:
    resolution: {integrity: sha512-wfOBkjXteqSnI59oPcJkcPl/ZmwvMMOj340qUIY1SKZCv0B9Cf4D4fAucRkIKQmsIuYK3x1rrgU7MeGRruiuiA==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dependencies:
      slice-ansi: 5.0.0
      string-width: 5.1.2
    dev: true

  /cli-width@3.0.0:
    resolution: {integrity: sha512-FxqpkPPwu1HjuN93Omfm4h8uIanXofW0RxVEW3k5RKx+mJJYSthzNhp32Kzxxy3YAEZ/Dc/EWN1vZRY0+kOhbw==}
    engines: {node: '>= 10'}
    dev: true

  /cliui@7.0.4:
    resolution: {integrity: sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==}
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0
    dev: true

  /clone-deep@4.0.1:
    resolution: {integrity: sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ==}
    engines: {node: '>=6'}
    dependencies:
      is-plain-object: 2.0.4
      kind-of: 6.0.3
      shallow-clone: 3.0.1
    dev: true

  /color-convert@1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}
    dependencies:
      color-name: 1.1.3
    dev: true

  /color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: 1.1.4
    dev: true

  /color-name@1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==}
    dev: true

  /color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}
    dev: true

  /color-parse@1.4.2:
    resolution: {integrity: sha512-RI7s49/8yqDj3fECFZjUI1Yi0z/Gq1py43oNJivAIIDSyJiOZLfYCRQEgn8HEVAj++PcRe8AnL2XF0fRJ3BTnA==}
    dependencies:
      color-name: 1.1.4
    dev: true

  /colord@2.9.3:
    resolution: {integrity: sha512-jeC1axXpnb0/2nn/Y1LPuLdgXBLH7aDcHu4KEKfqw3CUhX7ZpfBSlPKyqXE6btIgEzfWtrX3/tyBCaCvXvMkOw==}
    dev: true

  /colorette@2.0.20:
    resolution: {integrity: sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==}
    dev: true

  /colors@1.0.3:
    resolution: {integrity: sha512-pFGrxThWcWQ2MsAz6RtgeWe4NK2kUE1WfsrvvlctdII745EW9I0yflqhe7++M5LEc7bV2c/9/5zc8sFcpL0Drw==}
    engines: {node: '>=0.1.90'}
    dev: true

  /combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}
    dependencies:
      delayed-stream: 1.0.0
    dev: true

  /commander@10.0.1:
    resolution: {integrity: sha512-y4Mg2tXshplEbSGzx7amzPwKKOCGuoSRP/CjEdwwk0FOGlUbq6lKuoyDZTNZkmxHdJtp54hdfY/JUrdL7Xfdug==}
    engines: {node: '>=14'}
    dev: true

  /commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}
    dev: true

  /commander@6.2.1:
    resolution: {integrity: sha512-U7VdrJFnJgo4xjrHpTzu0yrHPGImdsmD95ZlgYSEajAn2JKzDhDTPG9kBTefmObL2w/ngeZnilk+OV9CG3d7UA==}
    engines: {node: '>= 6'}
    dev: true

  /commander@7.2.0:
    resolution: {integrity: sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==}
    engines: {node: '>= 10'}
    dev: true

  /comment-parser@1.4.0:
    resolution: {integrity: sha512-QLyTNiZ2KDOibvFPlZ6ZngVsZ/0gYnE6uTXi5aoDg8ed3AkJAz4sEje3Y8a29hQ1s6A99MZXe47fLAXQ1rTqaw==}
    engines: {node: '>= 12.0.0'}
    dev: true

  /commondir@1.0.1:
    resolution: {integrity: sha512-W9pAhw0ja1Edb5GVdIF1mjZw/ASI0AlShXM83UUGe2DVr5TdAPEA1OA8m/g8zWp9x6On7gqufY+FatDbC3MDQg==}
    dev: true

  /compare-func@2.0.0:
    resolution: {integrity: sha512-zHig5N+tPWARooBnb0Zx1MFcdfpyJrfTJ3Y5L+IFvUm8rM74hHz66z0gw0x4tijh5CorKkKUCnW82R2vmpeCRA==}
    dependencies:
      array-ify: 1.0.0
      dot-prop: 5.3.0
    dev: true

  /concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}
    dev: true

  /concat-stream@2.0.0:
    resolution: {integrity: sha512-MWufYdFw53ccGjCA+Ol7XJYpAlW6/prSMzuPOTRnJGcGzuhLn4Scrz7qf6o8bROZ514ltazcIFJZevcfbo0x7A==}
    engines: {'0': node >= 6.0}
    dependencies:
      buffer-from: 1.1.2
      inherits: 2.0.4
      readable-stream: 3.6.2
      typedarray: 0.0.6
    dev: true

  /config-chain@1.1.13:
    resolution: {integrity: sha512-qj+f8APARXHrM0hraqXYb2/bOVSV4PvJQlNZ/DVj0QrmNM2q2euizkeuVckQ57J+W0mRH6Hvi+k50M4Jul2VRQ==}
    dependencies:
      ini: 1.3.8
      proto-list: 1.2.4
    dev: true

  /connect@3.7.0:
    resolution: {integrity: sha512-ZqRXc+tZukToSNmh5C2iWMSoV3X1YUcPbqEM4DkEG5tNQXrQUZCNVGGv3IuicnkMtPfGf3Xtp8WCXs295iQ1pQ==}
    engines: {node: '>= 0.10.0'}
    dependencies:
      debug: 2.6.9
      finalhandler: 1.1.2
      parseurl: 1.3.3
      utils-merge: 1.0.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /content-type@1.0.5:
    resolution: {integrity: sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==}
    engines: {node: '>= 0.6'}
    dev: true

  /conventional-changelog-writer@6.0.1:
    resolution: {integrity: sha512-359t9aHorPw+U+nHzUXHS5ZnPBOizRxfQsWT5ZDHBfvfxQOAik+yfuhKXG66CN5LEWPpMNnIMHUTCKeYNprvHQ==}
    engines: {node: '>=14'}
    hasBin: true
    dependencies:
      conventional-commits-filter: 3.0.0
      dateformat: 3.0.3
      handlebars: 4.7.8
      json-stringify-safe: 5.0.1
      meow: 8.1.2
      semver: 7.6.3
      split: 1.0.1
    dev: true

  /conventional-commits-filter@3.0.0:
    resolution: {integrity: sha512-1ymej8b5LouPx9Ox0Dw/qAO2dVdfpRFq28e5Y0jJEU8ZrLdy0vOSkkIInwmxErFGhg6SALro60ZrwYFVTUDo4Q==}
    engines: {node: '>=14'}
    dependencies:
      lodash.ismatch: 4.4.0
      modify-values: 1.0.1
    dev: true

  /conventional-commits-parser@4.0.0:
    resolution: {integrity: sha512-WRv5j1FsVM5FISJkoYMR6tPk07fkKT0UodruX4je86V4owk451yjXAKzKAPOs9l7y59E2viHUS9eQ+dfUA9NSg==}
    engines: {node: '>=14'}
    hasBin: true
    dependencies:
      JSONStream: 1.3.5
      is-text-path: 1.0.1
      meow: 8.1.2
      split2: 3.2.2
    dev: true

  /convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}
    dev: true

  /cookie@0.4.2:
    resolution: {integrity: sha512-aSWTXFzaKWkvHO1Ny/s+ePFpvKsPnjc551iI41v3ny/ow6tBG5Vd+FuqGNhh1LxOmVzOlGUriIlOaokOvhaStA==}
    engines: {node: '>= 0.6'}
    dev: true

  /core-util-is@1.0.2:
    resolution: {integrity: sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ==}
    dev: true

  /cors@2.8.5:
    resolution: {integrity: sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==}
    engines: {node: '>= 0.10'}
    dependencies:
      object-assign: 4.1.1
      vary: 1.1.2
    dev: true

  /cosmiconfig@7.1.0:
    resolution: {integrity: sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==}
    engines: {node: '>=10'}
    dependencies:
      '@types/parse-json': 4.0.2
      import-fresh: 3.3.0
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2
    dev: true

  /coveralls@3.1.1:
    resolution: {integrity: sha512-+dxnG2NHncSD1NrqbSM3dn/lE57O6Qf/koe9+I7c+wzkqRmEvcp0kgJdxKInzYzkICKkFMZsX3Vct3++tsF9ww==}
    engines: {node: '>=6'}
    hasBin: true
    dependencies:
      js-yaml: 3.14.1
      lcov-parse: 1.0.0
      log-driver: 1.2.7
      minimist: 1.2.8
      request: 2.88.2
    dev: true

  /cross-spawn@7.0.3:
    resolution: {integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==}
    engines: {node: '>= 8'}
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2
    dev: true

  /css-declaration-sorter@7.2.0(postcss@8.4.41):
    resolution: {integrity: sha512-h70rUM+3PNFuaBDTLe8wF/cdWu+dOZmb7pJt8Z2sedYbAcQVQV/tEchueg3GWxwqS0cxtbxmaHEdkNACqcvsow==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.0.9
    dependencies:
      postcss: 8.4.41
    dev: true

  /css-loader@5.2.7(webpack@5.94.0):
    resolution: {integrity: sha512-Q7mOvpBNBG7YrVGMxRxcBJZFL75o+cH2abNASdibkj/fffYD8qWbInZrD0S9ccI6vZclF3DsHE7njGlLtaHbhg==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.27.0 || ^5.0.0
    dependencies:
      icss-utils: 5.1.0(postcss@8.4.41)
      loader-utils: 2.0.4
      postcss: 8.4.41
      postcss-modules-extract-imports: 3.1.0(postcss@8.4.41)
      postcss-modules-local-by-default: 4.0.5(postcss@8.4.41)
      postcss-modules-scope: 3.2.0(postcss@8.4.41)
      postcss-modules-values: 4.0.0(postcss@8.4.41)
      postcss-value-parser: 4.2.0
      schema-utils: 3.3.0
      semver: 7.6.3
      webpack: 5.94.0(webpack-cli@4.10.0)
    dev: true

  /css-select@5.1.0:
    resolution: {integrity: sha512-nwoRF1rvRRnnCqqY7updORDsuqKzqYJ28+oSMaJMMgOauh3fvwHqMS7EZpIPqK8GL+g9mKxF1vP/ZjSeNjEVHg==}
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 5.0.3
      domutils: 3.1.0
      nth-check: 2.1.1
    dev: true

  /css-tree@2.2.1:
    resolution: {integrity: sha512-OA0mILzGc1kCOCSJerOeqDxDQ4HOh+G8NbOJFOTgOCzpw7fCBubk0fEyxp8AgOL/jvLgYA/uV0cMbe43ElF1JA==}
    engines: {node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0, npm: '>=7.0.0'}
    dependencies:
      mdn-data: 2.0.28
      source-map-js: 1.2.0
    dev: true

  /css-tree@2.3.1:
    resolution: {integrity: sha512-6Fv1DV/TYw//QF5IzQdqsNDjx/wc8TrMBZsqjL9eW01tWb7R7k/mq+/VXfJCl7SoD5emsJop9cOByJZfs8hYIw==}
    engines: {node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0}
    dependencies:
      mdn-data: 2.0.30
      source-map-js: 1.2.0
    dev: true

  /css-what@6.1.0:
    resolution: {integrity: sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==}
    engines: {node: '>= 6'}
    dev: true

  /cssesc@3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /cssnano-preset-default@6.1.2(postcss@8.4.41):
    resolution: {integrity: sha512-1C0C+eNaeN8OcHQa193aRgYexyJtU8XwbdieEjClw+J9d94E41LwT6ivKH0WT+fYwYWB0Zp3I3IZ7tI/BbUbrg==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      browserslist: 4.23.3
      css-declaration-sorter: 7.2.0(postcss@8.4.41)
      cssnano-utils: 4.0.2(postcss@8.4.41)
      postcss: 8.4.41
      postcss-calc: 9.0.1(postcss@8.4.41)
      postcss-colormin: 6.1.0(postcss@8.4.41)
      postcss-convert-values: 6.1.0(postcss@8.4.41)
      postcss-discard-comments: 6.0.2(postcss@8.4.41)
      postcss-discard-duplicates: 6.0.3(postcss@8.4.41)
      postcss-discard-empty: 6.0.3(postcss@8.4.41)
      postcss-discard-overridden: 6.0.2(postcss@8.4.41)
      postcss-merge-longhand: 6.0.5(postcss@8.4.41)
      postcss-merge-rules: 6.1.1(postcss@8.4.41)
      postcss-minify-font-values: 6.1.0(postcss@8.4.41)
      postcss-minify-gradients: 6.0.3(postcss@8.4.41)
      postcss-minify-params: 6.1.0(postcss@8.4.41)
      postcss-minify-selectors: 6.0.4(postcss@8.4.41)
      postcss-normalize-charset: 6.0.2(postcss@8.4.41)
      postcss-normalize-display-values: 6.0.2(postcss@8.4.41)
      postcss-normalize-positions: 6.0.2(postcss@8.4.41)
      postcss-normalize-repeat-style: 6.0.2(postcss@8.4.41)
      postcss-normalize-string: 6.0.2(postcss@8.4.41)
      postcss-normalize-timing-functions: 6.0.2(postcss@8.4.41)
      postcss-normalize-unicode: 6.1.0(postcss@8.4.41)
      postcss-normalize-url: 6.0.2(postcss@8.4.41)
      postcss-normalize-whitespace: 6.0.2(postcss@8.4.41)
      postcss-ordered-values: 6.0.2(postcss@8.4.41)
      postcss-reduce-initial: 6.1.0(postcss@8.4.41)
      postcss-reduce-transforms: 6.0.2(postcss@8.4.41)
      postcss-svgo: 6.0.3(postcss@8.4.41)
      postcss-unique-selectors: 6.0.4(postcss@8.4.41)
    dev: true

  /cssnano-utils@4.0.2(postcss@8.4.41):
    resolution: {integrity: sha512-ZR1jHg+wZ8o4c3zqf1SIUSTIvm/9mU343FMR6Obe/unskbvpGhZOo1J6d/r8D1pzkRQYuwbcH3hToOuoA2G7oQ==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.41
    dev: true

  /cssnano@6.1.2(postcss@8.4.41):
    resolution: {integrity: sha512-rYk5UeX7VAM/u0lNqewCdasdtPK81CgX8wJFLEIXHbV2oldWRgJAsZrdhRXkV1NJzA2g850KiFm9mMU2HxNxMA==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      cssnano-preset-default: 6.1.2(postcss@8.4.41)
      lilconfig: 3.1.2
      postcss: 8.4.41
    dev: true

  /csso@5.0.5:
    resolution: {integrity: sha512-0LrrStPOdJj+SPCCrGhzryycLjwcgUSHBtxNA8aIDxf0GLsRh1cKYhB00Gd1lDOS4yGH69+SNn13+TWbVHETFQ==}
    engines: {node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0, npm: '>=7.0.0'}
    dependencies:
      css-tree: 2.2.1
    dev: true

  /cssom@0.3.8:
    resolution: {integrity: sha512-b0tGHbfegbhPJpxpiBPU2sCkigAqtM9O121le6bbOlgyV+NyGyCmVfJ6QW9eRjz8CpNfWEOYBIMIGRYkLwsIYg==}
    dev: true

  /cssom@0.4.4:
    resolution: {integrity: sha512-p3pvU7r1MyyqbTk+WbNJIgJjG2VmTIaB10rI93LzVPrmDJKkzKYMtxxyAvQXR/NS6otuzveI7+7BBq3SjBS2mw==}
    dev: true

  /cssstyle@2.3.0:
    resolution: {integrity: sha512-AZL67abkUzIuvcHqk7c09cezpGNcxUxU4Ioi/05xHk4DQeTkWmGYftIE6ctU6AEt+Gn4n1lDStOtj7FKycP71A==}
    engines: {node: '>=8'}
    dependencies:
      cssom: 0.3.8
    dev: true

  /csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}
    dev: true

  /custom-event@1.0.1:
    resolution: {integrity: sha512-GAj5FOq0Hd+RsCGVJxZuKaIDXDf3h6GQoNEjFgbLLI/trgtavwUbSnZ5pVfg27DVCaWjIohryS0JFwIJyT2cMg==}
    dev: true

  /dargs@7.0.0:
    resolution: {integrity: sha512-2iy1EkLdlBzQGvbweYRFxmFath8+K7+AKB0TlhHWkNuH+TmovaMH/Wp7V7R4u7f4SnX3OgLsU9t1NI9ioDnUpg==}
    engines: {node: '>=8'}
    dev: true

  /dashdash@1.14.1:
    resolution: {integrity: sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g==}
    engines: {node: '>=0.10'}
    dependencies:
      assert-plus: 1.0.0
    dev: true

  /data-urls@2.0.0:
    resolution: {integrity: sha512-X5eWTSXO/BJmpdIKCRuKUgSCgAN0OwliVK3yPKbwIWU1Tdw5BRajxlzMidvh+gwko9AfQ9zIj52pzF91Q3YAvQ==}
    engines: {node: '>=10'}
    dependencies:
      abab: 2.0.6
      whatwg-mimetype: 2.3.0
      whatwg-url: 8.7.0
    dev: true

  /date-fns@2.30.0:
    resolution: {integrity: sha512-fnULvOpxnC5/Vg3NCiWelDsLiUc9bRwAPs/+LfTLNvetFCtCTN+yQz15C/fs4AwX1R9K5GLtLfn8QW+dWisaAw==}
    engines: {node: '>=0.11'}
    dependencies:
      '@babel/runtime': 7.25.4
    dev: true

  /date-format@4.0.14:
    resolution: {integrity: sha512-39BOQLs9ZjKh0/patS9nrT8wc3ioX3/eA/zgbKNopnF2wCqJEoxywwwElATYvRsXdnOxA/OQeQoFZ3rFjVajhg==}
    engines: {node: '>=4.0'}
    dev: true

  /dateformat@3.0.3:
    resolution: {integrity: sha512-jyCETtSl3VMZMWeRo7iY1FL19ges1t55hMo5yaam4Jrsm5EPL89UQkoQRyiI+Yf4k8r2ZpdngkV8hr1lIdjb3Q==}
    dev: true

  /debug@2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.0.0
    dev: true

  /debug@4.3.3(supports-color@8.1.1):
    resolution: {integrity: sha512-/zxw5+vh1Tfv+4Qn7a5nsbcJKPaSvCDhojn6FEl9vupwK2VCSDtEiEtqr8DFtzYFOdz63LBkxec7DYuc2jon6Q==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.2
      supports-color: 8.1.1
    dev: true

  /debug@4.3.6:
    resolution: {integrity: sha512-O/09Bd4Z1fBrU4VzkhFqVgpPzaGbw6Sm9FEkBT1A/YBXQFGuuSxa1dN2nxgxS34JmKXqYx8CZAwEVoJFImUXIg==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.2
    dev: true

  /decamelize-keys@1.1.1:
    resolution: {integrity: sha512-WiPxgEirIV0/eIOMcnFBA3/IJZAZqKnwAwWyvvdi4lsr1WCN22nhdf/3db3DoZcUjTV2SqfzIwNyp6y2xs3nmg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      decamelize: 1.2.0
      map-obj: 1.0.1
    dev: true

  /decamelize@1.2.0:
    resolution: {integrity: sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /decamelize@4.0.0:
    resolution: {integrity: sha512-9iE1PgSik9HeIIw2JO94IidnE3eBoQrFJ3w7sFuzSX4DpmZ3v5sZpUiV5Swcf6mQEF+Y0ru8Neo+p+nyh2J+hQ==}
    engines: {node: '>=10'}
    dev: true

  /decimal.js@10.4.3:
    resolution: {integrity: sha512-VBBaLc1MgL5XpzgIP7ny5Z6Nx3UrRkIViUkPUdtl9aya5amy3De1gsUUSB1g3+3sExYNjCAsAznmukyxCb1GRA==}
    dev: true

  /dedent@0.7.0:
    resolution: {integrity: sha512-Q6fKUPqnAHAyhiUgFU7BUzLiv0kd8saH9al7tnu5Q/okj6dnupxyTgFIBjVzJATdfIAm9NAsvXNzjaKa+bxVyA==}
    dev: true

  /deep-eql@4.1.4:
    resolution: {integrity: sha512-SUwdGfqdKOwxCPeVYjwSyRpJ7Z+fhpwIAtmCUdZIWZ/YP5R9WAsyuSgpLVDi9bjWoN2LXHNss/dk3urXtdQxGg==}
    engines: {node: '>=6'}
    dependencies:
      type-detect: 4.1.0
    dev: true

  /deep-is@0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}
    dev: true

  /define-data-property@1.1.4:
    resolution: {integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      gopd: 1.0.1
    dev: true

  /del@5.1.0:
    resolution: {integrity: sha512-wH9xOVHnczo9jN2IW68BabcecVPxacIA3g/7z6vhSU/4stOKQzeCRK0yD0A24WiAAUJmmVpWqrERcTxnLo3AnA==}
    engines: {node: '>=8'}
    dependencies:
      globby: 10.0.2
      graceful-fs: 4.2.11
      is-glob: 4.0.3
      is-path-cwd: 2.2.0
      is-path-inside: 3.0.3
      p-map: 3.0.0
      rimraf: 3.0.2
      slash: 3.0.0
    dev: true

  /delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}
    dev: true

  /depd@2.0.0:
    resolution: {integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==}
    engines: {node: '>= 0.8'}
    dev: true

  /deprecation@2.3.1:
    resolution: {integrity: sha512-xmHIy4F3scKVwMsQ4WnVaS8bHOx0DmVwRywosKhaILI0ywMDWPtBSku2HNxRvF7jtwDRsoEwYQSfbxj8b7RlJQ==}
    dev: true

  /destroy@1.2.0:
    resolution: {integrity: sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}
    dev: true

  /di@0.0.1:
    resolution: {integrity: sha512-uJaamHkagcZtHPqCIHZxnFrXlunQXgBOsZSUOWwFw31QJCAbyTBoHMW75YOTur5ZNx8pIeAKgf6GWIgaqqiLhA==}
    dev: true

  /diff@5.0.0:
    resolution: {integrity: sha512-/VTCrvm5Z0JGty/BWHljh+BAiw3IK+2j87NGMu8Nwc/f48WoDAC395uomO9ZD117ZOBaHmkX1oyLvkVM/aIT3w==}
    engines: {node: '>=0.3.1'}
    dev: true

  /diff@5.2.0:
    resolution: {integrity: sha512-uIFDxqpRZGZ6ThOk84hEfqWoHx2devRFvpTZcTHur85vImfaxUbTW9Ryh4CpCuDnToOP1CEtXKIgytHBPVff5A==}
    engines: {node: '>=0.3.1'}
    dev: true

  /dir-glob@3.0.1:
    resolution: {integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==}
    engines: {node: '>=8'}
    dependencies:
      path-type: 4.0.0
    dev: true

  /doctrine@3.0.0:
    resolution: {integrity: sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==}
    engines: {node: '>=6.0.0'}
    dependencies:
      esutils: 2.0.3
    dev: true

  /dom-serialize@2.2.1:
    resolution: {integrity: sha512-Yra4DbvoW7/Z6LBN560ZwXMjoNOSAN2wRsKFGc4iBeso+mpIA6qj1vfdf9HpMaKAqG6wXTy+1SYEzmNpKXOSsQ==}
    dependencies:
      custom-event: 1.0.1
      ent: 2.2.1
      extend: 3.0.2
      void-elements: 2.0.1
    dev: true

  /dom-serializer@2.0.0:
    resolution: {integrity: sha512-wIkAryiqt/nV5EQKqQpo3SToSOV9J0DnbJqwK7Wv/Trc92zIAYZ4FlMu+JPFW1DfGFt81ZTCGgDEabffXeLyJg==}
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      entities: 4.5.0
    dev: true

  /domelementtype@2.3.0:
    resolution: {integrity: sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==}
    dev: true

  /domexception@2.0.1:
    resolution: {integrity: sha512-yxJ2mFy/sibVQlu5qHjOkf9J3K6zgmCxgJ94u2EdvDOV09H+32LtRswEcUsmUWN72pVLOEnTSRaIVVzVQgS0dg==}
    engines: {node: '>=8'}
    deprecated: Use your platform's native DOMException instead
    dependencies:
      webidl-conversions: 5.0.0
    dev: true

  /domhandler@5.0.3:
    resolution: {integrity: sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==}
    engines: {node: '>= 4'}
    dependencies:
      domelementtype: 2.3.0
    dev: true

  /domutils@3.1.0:
    resolution: {integrity: sha512-H78uMmQtI2AhgDJjWeQmHwJJ2bLPD3GMmO7Zja/ZZh84wkm+4ut+IUnUdRa8uCGX88DiVx1j6FRe1XfxEgjEZA==}
    dependencies:
      dom-serializer: 2.0.0
      domelementtype: 2.3.0
      domhandler: 5.0.3
    dev: true

  /dot-prop@5.3.0:
    resolution: {integrity: sha512-QM8q3zDe58hqUqjraQOmzZ1LIH9SWQJTlEKCH4kJ2oQvLZk7RbQXvtDM2XEq3fwkV9CCvvH4LA0AV+ogFsBM2Q==}
    engines: {node: '>=8'}
    dependencies:
      is-obj: 2.0.0
    dev: true

  /eastasianwidth@0.2.0:
    resolution: {integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==}
    dev: true

  /ecc-jsbn@0.1.2:
    resolution: {integrity: sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw==}
    dependencies:
      jsbn: 0.1.1
      safer-buffer: 2.1.2
    dev: true

  /editorconfig@1.0.4:
    resolution: {integrity: sha512-L9Qe08KWTlqYMVvMcTIvMAdl1cDUubzRNYL+WfA4bLDMHe4nemKkpmYzkznE1FwLKu0EEmy6obgQKzMJrg4x9Q==}
    engines: {node: '>=14'}
    hasBin: true
    dependencies:
      '@one-ini/wasm': 0.1.1
      commander: 10.0.1
      minimatch: 9.0.1
      semver: 7.6.3
    dev: true

  /ee-first@1.1.1:
    resolution: {integrity: sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==}
    dev: true

  /electron-to-chromium@1.5.13:
    resolution: {integrity: sha512-lbBcvtIJ4J6sS4tb5TLp1b4LyfCdMkwStzXPyAgVgTRAsep4bvrAGaBOP7ZJtQMNJpSQ9SqG4brWOroNaQtm7Q==}
    dev: true

  /emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}
    dev: true

  /emoji-regex@9.2.2:
    resolution: {integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==}
    dev: true

  /emojis-list@3.0.0:
    resolution: {integrity: sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==}
    engines: {node: '>= 4'}
    dev: true

  /encodeurl@1.0.2:
    resolution: {integrity: sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==}
    engines: {node: '>= 0.8'}
    dev: true

  /end-of-stream@1.4.4:
    resolution: {integrity: sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==}
    dependencies:
      once: 1.4.0
    dev: true

  /engine.io-parser@5.2.3:
    resolution: {integrity: sha512-HqD3yTBfnBxIrbnM1DoD6Pcq8NECnh8d4As1Qgh0z5Gg3jRRIqijury0CL3ghu/edArpUYiYqQiDUQBIs4np3Q==}
    engines: {node: '>=10.0.0'}
    dev: true

  /engine.io@6.5.5:
    resolution: {integrity: sha512-C5Pn8Wk+1vKBoHghJODM63yk8MvrO9EWZUfkAt5HAqIgPE4/8FF0PEGHXtEd40l223+cE5ABWuPzm38PHFXfMA==}
    engines: {node: '>=10.2.0'}
    dependencies:
      '@types/cookie': 0.4.1
      '@types/cors': 2.8.17
      '@types/node': 22.5.1
      accepts: 1.3.8
      base64id: 2.0.0
      cookie: 0.4.2
      cors: 2.8.5
      debug: 4.3.6
      engine.io-parser: 5.2.3
      ws: 8.17.1
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate
    dev: true

  /enhanced-resolve@5.17.1:
    resolution: {integrity: sha512-LMHl3dXhTcfv8gM4kEzIUeTQ+7fpdA0l2tUf34BddXPkz2A5xJ5L/Pchd5BL6rdccM9QGvu0sWZzK1Z1t4wwyg==}
    engines: {node: '>=10.13.0'}
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.1
    dev: true

  /enquirer@2.4.1:
    resolution: {integrity: sha512-rRqJg/6gd538VHvR3PSrdRBb/1Vy2YfzHqzvbhGIQpDRKIa4FgV/54b5Q1xYSxOOwKvjXweS26E0Q+nAMwp2pQ==}
    engines: {node: '>=8.6'}
    dependencies:
      ansi-colors: 4.1.3
      strip-ansi: 6.0.1
    dev: true

  /ent@2.2.1:
    resolution: {integrity: sha512-QHuXVeZx9d+tIQAz/XztU0ZwZf2Agg9CcXcgE1rurqvdBeDBrpSwjl8/6XUqMg7tw2Y7uAdKb2sRv+bSEFqQ5A==}
    engines: {node: '>= 0.4'}
    dependencies:
      punycode: 1.4.1
    dev: true

  /entities@4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==}
    engines: {node: '>=0.12'}
    dev: true

  /envinfo@7.13.0:
    resolution: {integrity: sha512-cvcaMr7KqXVh4nyzGTVqTum+gAiL265x5jUWQIDLq//zOGbW+gSW/C+OWLleY/rs9Qole6AZLMXPbtIFQbqu+Q==}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /error-ex@1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==}
    dependencies:
      is-arrayish: 0.2.1
    dev: true

  /es-define-property@1.0.0:
    resolution: {integrity: sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      get-intrinsic: 1.2.4
    dev: true

  /es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}
    dev: true

  /es-module-lexer@1.5.4:
    resolution: {integrity: sha512-MVNK56NiMrOwitFB7cqDwq0CQutbw+0BvLshJSse0MUNU+y1FC3bUS/AQg7oUng+/wKrrki7JfmwtVHkVfPLlw==}
    dev: true

  /esbuild-loader@3.0.1(webpack@5.94.0):
    resolution: {integrity: sha512-aZfGybqTeuyCd4AsVvWOOfkhIuN+wfZFjMyh3gyQEU1Uvsl8L6vye9HqP93iRa0iTA+6Jclap514PJIC3cLnMA==}
    peerDependencies:
      webpack: ^4.40.0 || ^5.0.0
    dependencies:
      esbuild: 0.17.19
      get-tsconfig: 4.7.6
      loader-utils: 2.0.4
      webpack: 5.94.0(webpack-cli@4.10.0)
      webpack-sources: 1.4.3
    dev: true

  /esbuild@0.17.19:
    resolution: {integrity: sha512-XQ0jAPFkK/u3LcVRcvVHQcTIqD6E2H1fvZMA5dQPSOWb3suUbWbfbRf94pjc0bNzRYLfIrDRQXr7X+LHIm5oHw==}
    engines: {node: '>=12'}
    hasBin: true
    requiresBuild: true
    optionalDependencies:
      '@esbuild/android-arm': 0.17.19
      '@esbuild/android-arm64': 0.17.19
      '@esbuild/android-x64': 0.17.19
      '@esbuild/darwin-arm64': 0.17.19
      '@esbuild/darwin-x64': 0.17.19
      '@esbuild/freebsd-arm64': 0.17.19
      '@esbuild/freebsd-x64': 0.17.19
      '@esbuild/linux-arm': 0.17.19
      '@esbuild/linux-arm64': 0.17.19
      '@esbuild/linux-ia32': 0.17.19
      '@esbuild/linux-loong64': 0.17.19
      '@esbuild/linux-mips64el': 0.17.19
      '@esbuild/linux-ppc64': 0.17.19
      '@esbuild/linux-riscv64': 0.17.19
      '@esbuild/linux-s390x': 0.17.19
      '@esbuild/linux-x64': 0.17.19
      '@esbuild/netbsd-x64': 0.17.19
      '@esbuild/openbsd-x64': 0.17.19
      '@esbuild/sunos-x64': 0.17.19
      '@esbuild/win32-arm64': 0.17.19
      '@esbuild/win32-ia32': 0.17.19
      '@esbuild/win32-x64': 0.17.19
    dev: true

  /escalade@3.1.2:
    resolution: {integrity: sha512-ErCHMCae19vR8vQGe50xIsVomy19rg6gFu3+r3jkEO46suLMWBksvVyoGgQV+jOfl84ZSOSlmv6Gxa89PmTGmA==}
    engines: {node: '>=6'}
    dev: true

  /escape-html@1.0.3:
    resolution: {integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==}
    dev: true

  /escape-string-regexp@1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==}
    engines: {node: '>=0.8.0'}
    dev: true

  /escape-string-regexp@4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    engines: {node: '>=10'}
    dev: true

  /escodegen@2.1.0:
    resolution: {integrity: sha512-2NlIDTwUWJN0mRPQOdtQBzbUHvdGY2P1VXSyU83Q3xKxM7WHX2Ql8dKq782Q9TgQUNOLEzEYu9bzLNj1q88I5w==}
    engines: {node: '>=6.0'}
    hasBin: true
    dependencies:
      esprima: 4.0.1
      estraverse: 5.3.0
      esutils: 2.0.3
    optionalDependencies:
      source-map: 0.6.1
    dev: true

  /eslint-config-ckeditor5@5.3.2(eslint@7.32.0):
    resolution: {integrity: sha512-40zDbEf9zKnZNoIOhN+mMIuqRtYs4caL3ILRj/yVkiwR/bHWDoOQzhTJGnU1ZToiQ6y9W/Kv0WaZcNfSiVXkZA==}
    peerDependencies:
      eslint: '>=7.0.0'
    dependencies:
      '@typescript-eslint/eslint-plugin': 5.62.0(@typescript-eslint/parser@5.62.0)(eslint@7.32.0)(typescript@4.9.5)
      '@typescript-eslint/parser': 5.62.0(eslint@7.32.0)(typescript@4.9.5)
      eslint: 7.32.0
      eslint-plugin-ckeditor5-rules: 5.3.2(eslint@7.32.0)(typescript@4.9.5)
      eslint-plugin-mocha: 7.0.1(eslint@7.32.0)
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color
    dev: true

  /eslint-plugin-ckeditor5-rules@5.3.2(eslint@7.32.0)(typescript@4.9.5):
    resolution: {integrity: sha512-GmaRT93JljrD8FH2LQvlwOVBwqM5q1/VWnlwRlnA8JWZy5EuOUInZKfN7y7Tt9j70cm5qqQNjMfTsnkp6oi6yw==}
    peerDependencies:
      eslint: '>=7.0.0'
    dependencies:
      '@es-joy/jsdoccomment': 0.40.1
      '@typescript-eslint/parser': 5.62.0(eslint@7.32.0)(typescript@4.9.5)
      enhanced-resolve: 5.17.1
      eslint: 7.32.0
      fs-extra: 11.2.0
      resolve.exports: 2.0.2
      upath: 2.0.1
      validate-npm-package-name: 5.0.1
    transitivePeerDependencies:
      - supports-color
      - typescript
    dev: true

  /eslint-plugin-mocha@7.0.1(eslint@7.32.0):
    resolution: {integrity: sha512-zkQRW9UigRaayGm/pK9TD5RjccKXSgQksNtpsXbG9b6L5I+jNx7m98VUbZ4w1H1ArlNA+K7IOH+z8TscN6sOYg==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      eslint: '>=7.0.0'
    dependencies:
      eslint: 7.32.0
      eslint-utils: 2.1.0
      ramda: 0.27.2
    dev: true

  /eslint-plugin-vue@9.27.0(eslint@7.32.0):
    resolution: {integrity: sha512-5Dw3yxEyuBSXTzT5/Ge1X5kIkRTQ3nvBn/VwPwInNiZBSJOO/timWMUaflONnFBzU6NhB68lxnCda7ULV5N7LA==}
    engines: {node: ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.2.0 || ^7.0.0 || ^8.0.0 || ^9.0.0
    dependencies:
      '@eslint-community/eslint-utils': 4.4.0(eslint@7.32.0)
      eslint: 7.32.0
      globals: 13.24.0
      natural-compare: 1.4.0
      nth-check: 2.1.1
      postcss-selector-parser: 6.1.2
      semver: 7.6.3
      vue-eslint-parser: 9.4.3(eslint@7.32.0)
      xml-name-validator: 4.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /eslint-scope@5.1.1:
    resolution: {integrity: sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==}
    engines: {node: '>=8.0.0'}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0
    dev: true

  /eslint-scope@7.2.2:
    resolution: {integrity: sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0
    dev: true

  /eslint-utils@2.1.0:
    resolution: {integrity: sha512-w94dQYoauyvlDc43XnGB8lU3Zt713vNChgt4EWwhXAP2XkBvndfxF0AgIqKOOasjPIPzj9JqgwkwbCYD0/V3Zg==}
    engines: {node: '>=6'}
    dependencies:
      eslint-visitor-keys: 1.3.0
    dev: true

  /eslint-visitor-keys@1.3.0:
    resolution: {integrity: sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ==}
    engines: {node: '>=4'}
    dev: true

  /eslint-visitor-keys@2.1.0:
    resolution: {integrity: sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==}
    engines: {node: '>=10'}
    dev: true

  /eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dev: true

  /eslint@7.32.0:
    resolution: {integrity: sha512-VHZ8gX+EDfz+97jGcgyGCyRia/dPOd6Xh9yPv8Bl1+SoaIwD+a/vlrOmGRUyOYu7MwUhc7CxqeaDZU13S4+EpA==}
    engines: {node: ^10.12.0 || >=12.0.0}
    hasBin: true
    dependencies:
      '@babel/code-frame': 7.12.11
      '@eslint/eslintrc': 0.4.3
      '@humanwhocodes/config-array': 0.5.0
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.3
      debug: 4.3.6
      doctrine: 3.0.0
      enquirer: 2.4.1
      escape-string-regexp: 4.0.0
      eslint-scope: 5.1.1
      eslint-utils: 2.1.0
      eslint-visitor-keys: 2.1.0
      espree: 7.3.1
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 6.0.1
      functional-red-black-tree: 1.0.1
      glob-parent: 5.1.2
      globals: 13.24.0
      ignore: 4.0.6
      import-fresh: 3.3.0
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      js-yaml: 3.14.1
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.4.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
      progress: 2.0.3
      regexpp: 3.2.0
      semver: 7.6.3
      strip-ansi: 6.0.1
      strip-json-comments: 3.1.1
      table: 6.8.2
      text-table: 0.2.0
      v8-compile-cache: 2.4.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /espree@7.3.1:
    resolution: {integrity: sha512-v3JCNCE64umkFpmkFGqzVKsOT0tN1Zr+ueqLZfpV1Ob8e+CEgPWa+OxCoGH3tnhimMKIaBm4m/vaRpJ/krRz2g==}
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      acorn: 7.4.1
      acorn-jsx: 5.3.2(acorn@7.4.1)
      eslint-visitor-keys: 1.3.0
    dev: true

  /espree@9.6.1:
    resolution: {integrity: sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      acorn: 8.12.1
      acorn-jsx: 5.3.2(acorn@8.12.1)
      eslint-visitor-keys: 3.4.3
    dev: true

  /esprima@4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /esquery@1.6.0:
    resolution: {integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==}
    engines: {node: '>=0.10'}
    dependencies:
      estraverse: 5.3.0
    dev: true

  /esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}
    dependencies:
      estraverse: 5.3.0
    dev: true

  /estraverse@4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==}
    engines: {node: '>=4.0'}
    dev: true

  /estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}
    dev: true

  /estree-walker@2.0.2:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==}
    dev: true

  /esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}
    dev: true

  /eventemitter3@4.0.7:
    resolution: {integrity: sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==}
    dev: true

  /eventemitter3@5.0.1:
    resolution: {integrity: sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==}
    dev: true

  /events@3.3.0:
    resolution: {integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==}
    engines: {node: '>=0.8.x'}
    dev: true

  /execa@4.1.0:
    resolution: {integrity: sha512-j5W0//W7f8UxAn8hXVnwG8tLwdiUy4FJLcSupCg6maBYZDpyBvTApK7KyuI4bKj8KOh1r2YH+6ucuYtJv1bTZA==}
    engines: {node: '>=10'}
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 5.2.0
      human-signals: 1.1.1
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0
    dev: true

  /extend@3.0.2:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==}
    dev: true

  /external-editor@3.1.0:
    resolution: {integrity: sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==}
    engines: {node: '>=4'}
    dependencies:
      chardet: 0.7.0
      iconv-lite: 0.4.24
      tmp: 0.0.33
    dev: true

  /extsprintf@1.3.0:
    resolution: {integrity: sha512-11Ndz7Nv+mvAC1j0ktTa7fAb0vLyGGX+rMHNBYQviQDGU0Hw7lhctJANqbPhu9nV9/izT/IntTgZ7Im/9LJs9g==}
    engines: {'0': node >=0.6.0}
    dev: true

  /fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}
    dev: true

  /fast-glob@3.3.2:
    resolution: {integrity: sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==}
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8
    dev: true

  /fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}
    dev: true

  /fast-levenshtein@2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}
    dev: true

  /fast-uri@3.0.1:
    resolution: {integrity: sha512-MWipKbbYiYI0UC7cl8m/i/IWTqfC8YXsqjzybjddLsFjStroQzsHXkc73JutMvBiXmOvapk+axIl79ig5t55Bw==}
    dev: true

  /fastest-levenshtein@1.0.16:
    resolution: {integrity: sha512-eRnCtTTtGZFpQCwhJiUOuxPQWRXVKYDn0b2PeHfXL6/Zi53SLAzAHfVhVWK2AryC/WH05kGfxhFIPvTF0SXQzg==}
    engines: {node: '>= 4.9.1'}
    dev: true

  /fastq@1.17.1:
    resolution: {integrity: sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==}
    dependencies:
      reusify: 1.0.4
    dev: true

  /figures@3.2.0:
    resolution: {integrity: sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg==}
    engines: {node: '>=8'}
    dependencies:
      escape-string-regexp: 1.0.5
    dev: true

  /file-entry-cache@6.0.1:
    resolution: {integrity: sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==}
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      flat-cache: 3.2.0
    dev: true

  /fill-range@7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: 5.0.1
    dev: true

  /finalhandler@1.1.2:
    resolution: {integrity: sha512-aAWcW57uxVNrQZqFXjITpW3sIUQmHGG3qSb9mUah9MgMC4NeWhNOlNjXEYq3HjRAvL6arUviZGGJsBg6z0zsWA==}
    engines: {node: '>= 0.8'}
    dependencies:
      debug: 2.6.9
      encodeurl: 1.0.2
      escape-html: 1.0.3
      on-finished: 2.3.0
      parseurl: 1.3.3
      statuses: 1.5.0
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /find-cache-dir@3.3.2:
    resolution: {integrity: sha512-wXZV5emFEjrridIgED11OoUKLxiYjAcqot/NJdAkOhlJ+vGzwhOAfcG5OX1jP+S0PcjEn8bdMJv+g2jwQ3Onig==}
    engines: {node: '>=8'}
    dependencies:
      commondir: 1.0.1
      make-dir: 3.1.0
      pkg-dir: 4.2.0
    dev: true

  /find-up@4.1.0:
    resolution: {integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==}
    engines: {node: '>=8'}
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0
    dev: true

  /find-up@5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==}
    engines: {node: '>=10'}
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0
    dev: true

  /flat-cache@3.2.0:
    resolution: {integrity: sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==}
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      flatted: 3.3.1
      keyv: 4.5.4
      rimraf: 3.0.2
    dev: true

  /flat@5.0.2:
    resolution: {integrity: sha512-b6suED+5/3rTpUBdG1gupIl8MPFCAMA0QXwmljLhvCUKcUvdE4gWky9zpuGCcXHOsz4J9wPGNWq6OKpmIzz3hQ==}
    hasBin: true
    dev: true

  /flatted@3.3.1:
    resolution: {integrity: sha512-X8cqMLLie7KsNUDSdzeN8FYK9rEt4Dt67OsG/DNGnYTSDBG4uFAJFBnUeiV+zCVAvwFy56IjM9sH51jVaEhNxw==}
    dev: true

  /follow-redirects@1.15.6:
    resolution: {integrity: sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true
    dev: true

  /foreground-child@3.3.0:
    resolution: {integrity: sha512-Ld2g8rrAyMYFXBhEqMz8ZAHBi4J4uS1i/CxGMDnjyFWddMXLVcDp051DZfu+t7+ab7Wv6SMqpWmyFIj5UbfFvg==}
    engines: {node: '>=14'}
    dependencies:
      cross-spawn: 7.0.3
      signal-exit: 4.1.0
    dev: true

  /forever-agent@0.6.1:
    resolution: {integrity: sha512-j0KLYPhm6zeac4lz3oJ3o65qvgQCcPubiyotZrXqEaG4hNagNYO8qdlUrX5vwqv9ohqeT/Z3j6+yW067yWWdUw==}
    dev: true

  /form-data@2.3.3:
    resolution: {integrity: sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==}
    engines: {node: '>= 0.12'}
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35
    dev: true

  /form-data@3.0.1:
    resolution: {integrity: sha512-RHkBKtLWUVwd7SqRIvCZMEvAMoGUp0XU+seQiZejj0COz3RI3hWP4sCv3gZWWLjJTd7rGwcsF5eKZGii0r/hbg==}
    engines: {node: '>= 6'}
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35
    dev: true

  /fs-extra@11.2.0:
    resolution: {integrity: sha512-PmDi3uwK5nFuXh7XDTlVnS17xJS7vW36is2+w3xcv8SVxiB4NyATf4ctkVY5bkSjX0Y4nbvZCq1/EjtEyr9ktw==}
    engines: {node: '>=14.14'}
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1
    dev: true

  /fs-extra@8.1.0:
    resolution: {integrity: sha512-yhlQgA6mnOJUKOsRUFsgJdQCvkKhcz8tlZG5HBQfReYZy46OwLcY+Zia0mtdHsOo9y/hP+CxMN0TU9QxoOtG4g==}
    engines: {node: '>=6 <7 || >=8'}
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 4.0.0
      universalify: 0.1.2
    dev: true

  /fs-extra@9.1.0:
    resolution: {integrity: sha512-hcg3ZmepS30/7BSFqRvoo3DOMQu7IjqxO5nCDt+zM9XWjb33Wg7ziNT+Qvqbuc3+gWpzO02JubVyk2G4Zvo1OQ==}
    engines: {node: '>=10'}
    dependencies:
      at-least-node: 1.0.0
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1
    dev: true

  /fs-minipass@2.1.0:
    resolution: {integrity: sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.6
    dev: true

  /fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}
    dev: true

  /fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}
    dev: true

  /functional-red-black-tree@1.0.1:
    resolution: {integrity: sha512-dsKNQNdj6xA3T+QlADDA7mOSlX0qiMINjn0cgr+eGHGsbSHzTabcIogz2+p/iqP1Xs6EP/sS2SbqH+brGTbq0g==}
    dev: true

  /gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}
    dev: true

  /get-caller-file@2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}
    dev: true

  /get-func-name@2.0.2:
    resolution: {integrity: sha512-8vXOvuE167CtIc3OyItco7N/dpRtBbYOsPsXCz7X/PMnlGjYjSGuZJgM1Y7mmew7BKf9BqvLX2tnOVy1BBUsxQ==}
    dev: true

  /get-intrinsic@1.2.4:
    resolution: {integrity: sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2
      has-proto: 1.0.3
      has-symbols: 1.0.3
      hasown: 2.0.2
    dev: true

  /get-own-enumerable-property-symbols@3.0.2:
    resolution: {integrity: sha512-I0UBV/XOz1XkIJHEUDMZAbzCThU/H8DxmSfmdGcKPnVhu2VfFqr34jr9777IyaTYvxjedWhqVIilEDsCdP5G6g==}
    dev: true

  /get-package-type@0.1.0:
    resolution: {integrity: sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q==}
    engines: {node: '>=8.0.0'}
    dev: true

  /get-stream@5.2.0:
    resolution: {integrity: sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA==}
    engines: {node: '>=8'}
    dependencies:
      pump: 3.0.0
    dev: true

  /get-tsconfig@4.7.6:
    resolution: {integrity: sha512-ZAqrLlu18NbDdRaHq+AKXzAmqIUPswPWKUchfytdAjiRFnCe5ojG2bstg6mRiZabkKfCoL/e98pbBELIV/YCeA==}
    dependencies:
      resolve-pkg-maps: 1.0.0
    dev: true

  /getpass@0.1.7:
    resolution: {integrity: sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng==}
    dependencies:
      assert-plus: 1.0.0
    dev: true

  /git-raw-commits@3.0.0:
    resolution: {integrity: sha512-b5OHmZ3vAgGrDn/X0kS+9qCfNKWe4K/jFnhwzVWWg0/k5eLa3060tZShrRg8Dja5kPc+YjS0Gc6y7cRr44Lpjw==}
    engines: {node: '>=14'}
    hasBin: true
    dependencies:
      dargs: 7.0.0
      meow: 8.1.2
      split2: 3.2.2
    dev: true

  /glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}
    dependencies:
      is-glob: 4.0.3
    dev: true

  /glob-to-regexp@0.4.1:
    resolution: {integrity: sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==}
    dev: true

  /glob@10.4.5:
    resolution: {integrity: sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==}
    hasBin: true
    dependencies:
      foreground-child: 3.3.0
      jackspeak: 3.4.3
      minimatch: 9.0.5
      minipass: 7.1.2
      package-json-from-dist: 1.0.0
      path-scurry: 1.11.1
    dev: true

  /glob@7.2.0:
    resolution: {integrity: sha512-lmLf6gtyrPq8tTjSmrO94wBeQbFR3HbLHbuyD69wuyQkImp2hWqMGB47OX65FBkPffO641IP9jWa1z4ivqG26Q==}
    deprecated: Glob versions prior to v9 are no longer supported
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1
    dev: true

  /glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}
    deprecated: Glob versions prior to v9 are no longer supported
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1
    dev: true

  /globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}
    dev: true

  /globals@13.24.0:
    resolution: {integrity: sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==}
    engines: {node: '>=8'}
    dependencies:
      type-fest: 0.20.2
    dev: true

  /globby@10.0.2:
    resolution: {integrity: sha512-7dUi7RvCoT/xast/o/dLN53oqND4yk0nsHkhRgn9w65C4PofCLOoJ39iSOg+qVDdWQPIEj+eszMHQ+aLVwwQSg==}
    engines: {node: '>=8'}
    dependencies:
      '@types/glob': 7.2.0
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.2
      glob: 7.2.3
      ignore: 5.3.2
      merge2: 1.4.1
      slash: 3.0.0
    dev: true

  /globby@11.1.0:
    resolution: {integrity: sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==}
    engines: {node: '>=10'}
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.2
      ignore: 5.3.2
      merge2: 1.4.1
      slash: 3.0.0
    dev: true

  /gopd@1.0.1:
    resolution: {integrity: sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==}
    dependencies:
      get-intrinsic: 1.2.4
    dev: true

  /graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}
    dev: true

  /graphemer@1.4.0:
    resolution: {integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==}
    dev: true

  /growl@1.10.5:
    resolution: {integrity: sha512-qBr4OuELkhPenW6goKVXiv47US3clb3/IbuWF9KNKEijAy9oeHxU9IgzjvJhHkUzhaj7rOUD7+YGWqUjLp5oSA==}
    engines: {node: '>=4.x'}
    dev: true

  /handlebars@4.7.8:
    resolution: {integrity: sha512-vafaFqs8MZkRrSX7sFVUdo3ap/eNiLnb4IakshzvP56X5Nr1iGKAIqdX6tMlm6HcNRIkr6AxO5jFEoJzzpT8aQ==}
    engines: {node: '>=0.4.7'}
    hasBin: true
    dependencies:
      minimist: 1.2.8
      neo-async: 2.6.2
      source-map: 0.6.1
      wordwrap: 1.0.0
    optionalDependencies:
      uglify-js: 3.19.2
    dev: true

  /har-schema@2.0.0:
    resolution: {integrity: sha512-Oqluz6zhGX8cyRaTQlFMPw80bSJVG2x/cFb8ZPhUILGgHka9SsokCCOQgpveePerqidZOrT14ipqfJb7ILcW5Q==}
    engines: {node: '>=4'}
    dev: true

  /har-validator@5.1.5:
    resolution: {integrity: sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w==}
    engines: {node: '>=6'}
    deprecated: this library is no longer supported
    dependencies:
      ajv: 6.12.6
      har-schema: 2.0.0
    dev: true

  /hard-rejection@2.1.0:
    resolution: {integrity: sha512-VIZB+ibDhx7ObhAe7OVtoEbuP4h/MuOTHJ+J8h/eBXotJYl0fBgR72xDFCKgIh22OJZIOVNxBMWuhAr10r8HdA==}
    engines: {node: '>=6'}
    dev: true

  /has-flag@3.0.0:
    resolution: {integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==}
    engines: {node: '>=4'}
    dev: true

  /has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}
    dev: true

  /has-property-descriptors@1.0.2:
    resolution: {integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==}
    dependencies:
      es-define-property: 1.0.0
    dev: true

  /has-proto@1.0.3:
    resolution: {integrity: sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==}
    engines: {node: '>= 0.4'}
    dev: true

  /has-symbols@1.0.3:
    resolution: {integrity: sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==}
    engines: {node: '>= 0.4'}
    dev: true

  /hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      function-bind: 1.1.2
    dev: true

  /he@1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==}
    hasBin: true
    dev: true

  /hosted-git-info@2.8.9:
    resolution: {integrity: sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw==}
    dev: true

  /hosted-git-info@4.1.0:
    resolution: {integrity: sha512-kyCuEOWjJqZuDbRHzL8V93NzQhwIB71oFWSyzVo+KPZI+pnQPPxucdkrOZvkLRnrf5URsQM+IJ09Dw29cRALIA==}
    engines: {node: '>=10'}
    dependencies:
      lru-cache: 6.0.0
    dev: true

  /html-encoding-sniffer@2.0.1:
    resolution: {integrity: sha512-D5JbOMBIR/TVZkubHT+OyT2705QvogUW4IBn6nHd756OwieSF9aDYFj4dv6HHEVGYbHaLETa3WggZYWWMyy3ZQ==}
    engines: {node: '>=10'}
    dependencies:
      whatwg-encoding: 1.0.5
    dev: true

  /html-escaper@2.0.2:
    resolution: {integrity: sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==}
    dev: true

  /http-errors@2.0.0:
    resolution: {integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==}
    engines: {node: '>= 0.8'}
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1
    dev: true

  /http-proxy-agent@4.0.1:
    resolution: {integrity: sha512-k0zdNgqWTGA6aeIRVpvfVob4fL52dTfaehylg0Y4UvSySvOq/Y+BOyPrgpUrA7HylqvU8vIZGsRuXmspskV0Tg==}
    engines: {node: '>= 6'}
    dependencies:
      '@tootallnate/once': 1.1.2
      agent-base: 6.0.2
      debug: 4.3.6
    transitivePeerDependencies:
      - supports-color
    dev: true

  /http-proxy@1.18.1:
    resolution: {integrity: sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==}
    engines: {node: '>=8.0.0'}
    dependencies:
      eventemitter3: 4.0.7
      follow-redirects: 1.15.6
      requires-port: 1.0.0
    transitivePeerDependencies:
      - debug
    dev: true

  /http-signature@1.2.0:
    resolution: {integrity: sha512-CAbnr6Rz4CYQkLYUtSNXxQPUH2gK8f3iWexVlsnMeD+GjlsQ0Xsy1cOX+mN3dtxYomRy21CiOzU8Uhw6OwncEQ==}
    engines: {node: '>=0.8', npm: '>=1.3.7'}
    dependencies:
      assert-plus: 1.0.0
      jsprim: 1.4.2
      sshpk: 1.18.0
    dev: true

  /https-proxy-agent@5.0.1:
    resolution: {integrity: sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==}
    engines: {node: '>= 6'}
    dependencies:
      agent-base: 6.0.2
      debug: 4.3.6
    transitivePeerDependencies:
      - supports-color
    dev: true

  /human-signals@1.1.1:
    resolution: {integrity: sha512-SEQu7vl8KjNL2eoGBLF3+wAjpsNfA9XMlXAYj/3EdaNfAlxKthD1xjEQfGOUhllCGGJVNY34bRr6lPINhNjyZw==}
    engines: {node: '>=8.12.0'}
    dev: true

  /husky@8.0.3:
    resolution: {integrity: sha512-+dQSyqPh4x1hlO1swXBiNb2HzTDN1I2IGLQx1GrBuiqFJfoMrnZWwVmatvSiO+Iz8fBUnf+lekwNo4c2LlXItg==}
    engines: {node: '>=14'}
    hasBin: true
    dev: true

  /iconv-lite@0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: 2.1.2
    dev: true

  /icss-utils@5.1.0(postcss@8.4.41):
    resolution: {integrity: sha512-soFhflCVWLfRNOPU3iv5Z9VUdT44xFRbzjLsEzSr5AQmgqPMTHdU3PMT1Cf1ssx8fLNJDA1juftYl+PUcv3MqA==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      postcss: 8.4.41
    dev: true

  /ignore@4.0.6:
    resolution: {integrity: sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg==}
    engines: {node: '>= 4'}
    dev: true

  /ignore@5.3.2:
    resolution: {integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==}
    engines: {node: '>= 4'}
    dev: true

  /import-fresh@3.3.0:
    resolution: {integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==}
    engines: {node: '>=6'}
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0
    dev: true

  /import-local@3.2.0:
    resolution: {integrity: sha512-2SPlun1JUPWoM6t3F0dw0FkCF/jWY8kttcY4f599GLTSjh2OCuuhdTkJQsEcZzBqbXZGKMK2OqW1oZsjtf/gQA==}
    engines: {node: '>=8'}
    hasBin: true
    dependencies:
      pkg-dir: 4.2.0
      resolve-cwd: 3.0.0
    dev: true

  /imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}
    dev: true

  /indent-string@4.0.0:
    resolution: {integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==}
    engines: {node: '>=8'}
    dev: true

  /infer-owner@1.0.4:
    resolution: {integrity: sha512-IClj+Xz94+d7irH5qRyfJonOdfTzuDaifE6ZPWfx0N0+/ATZCbuTPq2prFl526urkQd90WyUKIh1DfBQ2hMz9A==}
    dev: true

  /inflight@1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2
    dev: true

  /inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}
    dev: true

  /ini@1.3.8:
    resolution: {integrity: sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==}
    dev: true

  /inquirer@7.3.3:
    resolution: {integrity: sha512-JG3eIAj5V9CwcGvuOmoo6LB9kbAYT8HXffUl6memuszlwDC/qvFAJw49XJ5NROSFNPxp3iQg1GqkFhaY/CR0IA==}
    engines: {node: '>=8.0.0'}
    dependencies:
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-width: 3.0.0
      external-editor: 3.1.0
      figures: 3.2.0
      lodash: 4.17.21
      mute-stream: 0.0.8
      run-async: 2.4.1
      rxjs: 6.6.7
      string-width: 4.2.3
      strip-ansi: 6.0.1
      through: 2.3.8
    dev: true

  /interpret@1.4.0:
    resolution: {integrity: sha512-agE4QfB2Lkp9uICn7BAqoscw4SZP9kTE2hxiFI3jBPmXJfdqiahTbUuKGsMoN2GtqL9AxhYioAcVvgsb1HvRbA==}
    engines: {node: '>= 0.10'}
    dev: true

  /interpret@2.2.0:
    resolution: {integrity: sha512-Ju0Bz/cEia55xDwUWEa8+olFpCiQoypjnQySseKtmjNrnps3P+xfpUmGr90T7yjlVJmOtybRvPXhKMbHr+fWnw==}
    engines: {node: '>= 0.10'}
    dev: true

  /is-arrayish@0.2.1:
    resolution: {integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==}
    dev: true

  /is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}
    dependencies:
      binary-extensions: 2.3.0
    dev: true

  /is-core-module@2.15.1:
    resolution: {integrity: sha512-z0vtXSwucUJtANQWldhbtbt7BnL0vxiFjIdDLAatwhDYty2bad6s+rijD6Ri4YuYJubLzIJLUidCh09e1djEVQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      hasown: 2.0.2
    dev: true

  /is-docker@2.2.1:
    resolution: {integrity: sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==}
    engines: {node: '>=8'}
    hasBin: true
    dev: true

  /is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}
    dev: true

  /is-fullwidth-code-point@4.0.0:
    resolution: {integrity: sha512-O4L094N2/dZ7xqVdrXhh9r1KODPJpFms8B5sGdJLPy664AgvXsreZUyCQQNItZRDlYug4xStLjNp/sz3HvBowQ==}
    engines: {node: '>=12'}
    dev: true

  /is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1
    dev: true

  /is-interactive@1.0.0:
    resolution: {integrity: sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==}
    engines: {node: '>=8'}
    dev: true

  /is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}
    dev: true

  /is-obj@1.0.1:
    resolution: {integrity: sha512-l4RyHgRqGN4Y3+9JHVrNqO+tN0rV5My76uW5/nuO4K1b6vw5G8d/cmFjP9tRfEsdhZNt0IFdZuK/c2Vr4Nb+Qg==}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-obj@2.0.0:
    resolution: {integrity: sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w==}
    engines: {node: '>=8'}
    dev: true

  /is-path-cwd@2.2.0:
    resolution: {integrity: sha512-w942bTcih8fdJPJmQHFzkS76NEP8Kzzvmw92cXsazb8intwLqPibPPdXf4ANdKV3rYMuuQYGIWtvz9JilB3NFQ==}
    engines: {node: '>=6'}
    dev: true

  /is-path-inside@3.0.3:
    resolution: {integrity: sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==}
    engines: {node: '>=8'}
    dev: true

  /is-plain-obj@1.1.0:
    resolution: {integrity: sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg==}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-plain-obj@2.1.0:
    resolution: {integrity: sha512-YWnfyRwxL/+SsrWYfOpUtz5b3YD+nyfkHvjbcanzk8zgyO4ASD67uVMRt8k5bM4lLMDnXfriRhOpemw+NfT1eA==}
    engines: {node: '>=8'}
    dev: true

  /is-plain-object@2.0.4:
    resolution: {integrity: sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1
    dev: true

  /is-plain-object@5.0.0:
    resolution: {integrity: sha512-VRSzKkbMm5jMDoKLbltAkFQ5Qr7VDiTFGXxYFXXowVj387GeGNOCsOH6Msy00SGZ3Fp84b1Naa1psqgcCIEP5Q==}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-potential-custom-element-name@1.0.1:
    resolution: {integrity: sha512-bCYeRA2rVibKZd+s2625gGnGF/t7DSqDs4dP7CrLA1m7jKWz6pps0LpYLJN8Q64HtmPKJ1hrN3nzPNKFEKOUiQ==}
    dev: true

  /is-regexp@1.0.0:
    resolution: {integrity: sha512-7zjFAPO4/gwyQAAgRRmqeEeyIICSdmCqa3tsVHMdBzaXXRiqopZL4Cyghg/XulGWrtABTpbnYYzzIRffLkP4oA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-stream@2.0.1:
    resolution: {integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==}
    engines: {node: '>=8'}
    dev: true

  /is-text-path@1.0.1:
    resolution: {integrity: sha512-xFuJpne9oFz5qDaodwmmG08e3CawH/2ZV8Qqza1Ko7Sk8POWbkRdwIoAWVhqvq0XeUzANEhKo2n0IXUGBm7A/w==}
    engines: {node: '>=0.10.0'}
    dependencies:
      text-extensions: 1.9.0
    dev: true

  /is-typedarray@1.0.0:
    resolution: {integrity: sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA==}
    dev: true

  /is-unicode-supported@0.1.0:
    resolution: {integrity: sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==}
    engines: {node: '>=10'}
    dev: true

  /is-wsl@2.2.0:
    resolution: {integrity: sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==}
    engines: {node: '>=8'}
    dependencies:
      is-docker: 2.2.1
    dev: true

  /isbinaryfile@4.0.10:
    resolution: {integrity: sha512-iHrqe5shvBUcFbmZq9zOQHBoeOhZJu6RQGrDpBgenUm/Am+F3JM2MgQj+rK3Z601fzrL5gLZWtAPH2OBaSVcyw==}
    engines: {node: '>= 8.0.0'}
    dev: true

  /isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}
    dev: true

  /isobject@3.0.1:
    resolution: {integrity: sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==}
    engines: {node: '>=0.10.0'}
    dev: true

  /isstream@0.1.2:
    resolution: {integrity: sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g==}
    dev: true

  /istanbul-lib-coverage@3.2.2:
    resolution: {integrity: sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg==}
    engines: {node: '>=8'}
    dev: true

  /istanbul-lib-instrument@5.2.1:
    resolution: {integrity: sha512-pzqtp31nLv/XFOzXGuvhCb8qhjmTVo5vjVk19XE4CRlSWz0KoeJ3bw9XsA7nOp9YBf4qHjwBxkDzKcME/J29Yg==}
    engines: {node: '>=8'}
    dependencies:
      '@babel/core': 7.25.2
      '@babel/parser': 7.25.4
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-coverage: 3.2.2
      semver: 7.6.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /istanbul-lib-report@3.0.1:
    resolution: {integrity: sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==}
    engines: {node: '>=10'}
    dependencies:
      istanbul-lib-coverage: 3.2.2
      make-dir: 4.0.0
      supports-color: 7.2.0
    dev: true

  /istanbul-lib-source-maps@4.0.1:
    resolution: {integrity: sha512-n3s8EwkdFIJCG3BPKBYvskgXGoy88ARzvegkitk60NxRdwltLOTaH7CUiMRXvwYorl0Q712iEjcWB+fK/MrWVw==}
    engines: {node: '>=10'}
    dependencies:
      debug: 4.3.6
      istanbul-lib-coverage: 3.2.2
      source-map: 0.6.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /istanbul-reports@3.1.7:
    resolution: {integrity: sha512-BewmUXImeuRk2YY0PVbxgKAysvhRPUQE0h5QRM++nVWyubKGV0l8qQ5op8+B2DOmwSe63Jivj0BjkPQVf8fP5g==}
    engines: {node: '>=8'}
    dependencies:
      html-escaper: 2.0.2
      istanbul-lib-report: 3.0.1
    dev: true

  /jackspeak@3.4.3:
    resolution: {integrity: sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==}
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0
    dev: true

  /javascript-stringify@1.6.0:
    resolution: {integrity: sha512-fnjC0up+0SjEJtgmmG+teeel68kutkvzfctO/KxE3qJlbunkJYAshgH3boU++gSBHP8z5/r0ts0qRIrHf0RTQQ==}
    dev: true

  /jest-worker@26.6.2:
    resolution: {integrity: sha512-KWYVV1c4i+jbMpaBC+U++4Va0cp8OisU185o73T1vo99hqi7w8tSJfUXYswwqqrjzwxa6KpRK54WhPvwf5w6PQ==}
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/node': 22.5.1
      merge-stream: 2.0.0
      supports-color: 7.2.0
    dev: true

  /jest-worker@27.5.1:
    resolution: {integrity: sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==}
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/node': 22.5.1
      merge-stream: 2.0.0
      supports-color: 8.1.1
    dev: true

  /js-beautify@1.15.1:
    resolution: {integrity: sha512-ESjNzSlt/sWE8sciZH8kBF8BPlwXPwhR6pWKAw8bw4Bwj+iZcnKW6ONWUutJ7eObuBZQpiIb8S7OYspWrKt7rA==}
    engines: {node: '>=14'}
    hasBin: true
    dependencies:
      config-chain: 1.1.13
      editorconfig: 1.0.4
      glob: 10.4.5
      js-cookie: 3.0.5
      nopt: 7.2.1
    dev: true

  /js-cookie@3.0.5:
    resolution: {integrity: sha512-cEiJEAEoIbWfCZYKWhVwFuvPX1gETRYPw6LlaTKoxD3s2AkXzkCjnp6h0V77ozyqj0jakteJ4YqDJT830+lVGw==}
    engines: {node: '>=14'}
    dev: true

  /js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}
    dev: true

  /js-yaml@3.14.1:
    resolution: {integrity: sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==}
    hasBin: true
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1
    dev: true

  /js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true
    dependencies:
      argparse: 2.0.1
    dev: true

  /jsbn@0.1.1:
    resolution: {integrity: sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg==}
    dev: true

  /jsdoc-type-pratt-parser@4.0.0:
    resolution: {integrity: sha512-YtOli5Cmzy3q4dP26GraSOeAhqecewG04hoO8DY56CH4KJ9Fvv5qKWUCCo3HZob7esJQHCv6/+bnTy72xZZaVQ==}
    engines: {node: '>=12.0.0'}
    dev: true

  /jsdom@16.7.0:
    resolution: {integrity: sha512-u9Smc2G1USStM+s/x1ru5Sxrl6mPYCbByG1U/hUmqaVsm4tbNyS7CicOSRyuGQYZhTu0h84qkZZQ/I+dzizSVw==}
    engines: {node: '>=10'}
    peerDependencies:
      canvas: ^2.5.0
    peerDependenciesMeta:
      canvas:
        optional: true
    dependencies:
      abab: 2.0.6
      acorn: 8.12.1
      acorn-globals: 6.0.0
      cssom: 0.4.4
      cssstyle: 2.3.0
      data-urls: 2.0.0
      decimal.js: 10.4.3
      domexception: 2.0.1
      escodegen: 2.1.0
      form-data: 3.0.1
      html-encoding-sniffer: 2.0.1
      http-proxy-agent: 4.0.1
      https-proxy-agent: 5.0.1
      is-potential-custom-element-name: 1.0.1
      nwsapi: 2.2.12
      parse5: 6.0.1
      saxes: 5.0.1
      symbol-tree: 3.2.4
      tough-cookie: 4.1.4
      w3c-hr-time: 1.0.2
      w3c-xmlserializer: 2.0.0
      webidl-conversions: 6.1.0
      whatwg-encoding: 1.0.5
      whatwg-mimetype: 2.3.0
      whatwg-url: 8.7.0
      ws: 7.5.10
      xml-name-validator: 3.0.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate
    dev: true

  /jsesc@2.5.2:
    resolution: {integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /json-buffer@3.0.1:
    resolution: {integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==}
    dev: true

  /json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==}
    dev: true

  /json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}
    dev: true

  /json-schema-traverse@1.0.0:
    resolution: {integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==}
    dev: true

  /json-schema@0.4.0:
    resolution: {integrity: sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==}
    dev: true

  /json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==}
    dev: true

  /json-stringify-safe@5.0.1:
    resolution: {integrity: sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA==}
    dev: true

  /json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true
    dev: true

  /jsonfile@4.0.0:
    resolution: {integrity: sha512-m6F1R3z8jjlf2imQHS2Qez5sjKWQzbuuhuJ/FKYFRZvPE3PuHcSMVZzfsLhGVOkfd20obL5SWEBew5ShlquNxg==}
    optionalDependencies:
      graceful-fs: 4.2.11
    dev: true

  /jsonfile@6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==}
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11
    dev: true

  /jsonparse@1.3.1:
    resolution: {integrity: sha512-POQXvpdL69+CluYsillJ7SUhKvytYjW9vG/GKpnf+xP8UWgYEM/RaMzHHofbALDiKbbP1W8UEYmgGl39WkPZsg==}
    engines: {'0': node >= 0.2.0}
    dev: true

  /jsprim@1.4.2:
    resolution: {integrity: sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==}
    engines: {node: '>=0.6.0'}
    dependencies:
      assert-plus: 1.0.0
      extsprintf: 1.3.0
      json-schema: 0.4.0
      verror: 1.10.0
    dev: true

  /just-extend@6.2.0:
    resolution: {integrity: sha512-cYofQu2Xpom82S6qD778jBDpwvvy39s1l/hrYij2u9AMdQcGRpaBu6kY4mVhuno5kJVi1DAz4aiphA2WI1/OAw==}
    dev: true

  /karma-chai@0.1.0(chai@4.5.0)(karma@6.4.4):
    resolution: {integrity: sha512-mqKCkHwzPMhgTYca10S90aCEX9+HjVjjrBFAsw36Zj7BlQNbokXXCAe6Ji04VUMsxcY5RLP7YphpfO06XOubdg==}
    peerDependencies:
      chai: '*'
      karma: '>=0.10.9'
    dependencies:
      chai: 4.5.0
      karma: 6.4.4
    dev: true

  /karma-chrome-launcher@3.2.0:
    resolution: {integrity: sha512-rE9RkUPI7I9mAxByQWkGJFXfFD6lE4gC5nPuZdobf/QdTEJI6EU4yIay/cfU/xV4ZxlM5JiTv7zWYgA64NpS5Q==}
    dependencies:
      which: 1.3.1
    dev: true

  /karma-coverage@2.2.1:
    resolution: {integrity: sha512-yj7hbequkQP2qOSb20GuNSIyE//PgJWHwC2IydLE6XRtsnaflv+/OSGNssPjobYUlhVVagy99TQpqUt3vAUG7A==}
    engines: {node: '>=10.0.0'}
    dependencies:
      istanbul-lib-coverage: 3.2.2
      istanbul-lib-instrument: 5.2.1
      istanbul-lib-report: 3.0.1
      istanbul-lib-source-maps: 4.0.1
      istanbul-reports: 3.1.7
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /karma-firefox-launcher@2.1.3:
    resolution: {integrity: sha512-LMM2bseebLbYjODBOVt7TCPP9OI2vZIXCavIXhkO9m+10Uj5l7u/SKoeRmYx8FYHTVGZSpk6peX+3BMHC1WwNw==}
    dependencies:
      is-wsl: 2.2.0
      which: 3.0.1
    dev: true

  /karma-mocha-reporter@2.2.5(karma@6.4.4):
    resolution: {integrity: sha512-Hr6nhkIp0GIJJrvzY8JFeHpQZNseuIakGac4bpw8K1+5F0tLb6l7uvXRa8mt2Z+NVwYgCct4QAfp2R2QP6o00w==}
    peerDependencies:
      karma: '>=0.13'
    dependencies:
      chalk: 2.4.2
      karma: 6.4.4
      log-symbols: 2.2.0
      strip-ansi: 4.0.0
    dev: true

  /karma-mocha@2.0.1:
    resolution: {integrity: sha512-Tzd5HBjm8his2OA4bouAsATYEpZrp9vC7z5E5j4C5Of5Rrs1jY67RAwXNcVmd/Bnk1wgvQRou0zGVLey44G4tQ==}
    dependencies:
      minimist: 1.2.8
    dev: true

  /karma-sinon@1.0.5(karma@6.4.4)(sinon@13.0.2):
    resolution: {integrity: sha512-wrkyAxJmJbn75Dqy17L/8aILJWFm7znd1CE8gkyxTBFnjMSOe2XTJ3P30T8SkxWZHmoHX0SCaUJTDBEoXs25Og==}
    engines: {node: '>= 0.10.0'}
    peerDependencies:
      karma: '>=0.10'
      sinon: '*'
    dependencies:
      karma: 6.4.4
      sinon: 13.0.2
    dev: true

  /karma-sourcemap-loader@0.3.8:
    resolution: {integrity: sha512-zorxyAakYZuBcHRJE+vbrK2o2JXLFWK8VVjiT/6P+ltLBUGUvqTEkUiQ119MGdOrK7mrmxXHZF1/pfT6GgIZ6g==}
    dependencies:
      graceful-fs: 4.2.11
    dev: true

  /karma-webpack@5.0.1(webpack@5.94.0):
    resolution: {integrity: sha512-oo38O+P3W2mSPCSUrQdySSPv1LvPpXP+f+bBimNomS5sW+1V4SuhCuW8TfJzV+rDv921w2fDSDw0xJbPe6U+kQ==}
    engines: {node: '>= 18'}
    peerDependencies:
      webpack: ^5.0.0
    dependencies:
      glob: 7.2.3
      minimatch: 9.0.5
      webpack: 5.94.0(webpack-cli@4.10.0)
      webpack-merge: 4.2.2
    dev: true

  /karma@6.4.4:
    resolution: {integrity: sha512-LrtUxbdvt1gOpo3gxG+VAJlJAEMhbWlM4YrFQgql98FwF7+K8K12LYO4hnDdUkNjeztYrOXEMqgTajSWgmtI/w==}
    engines: {node: '>= 10'}
    hasBin: true
    dependencies:
      '@colors/colors': 1.5.0
      body-parser: 1.20.2
      braces: 3.0.3
      chokidar: 3.6.0
      connect: 3.7.0
      di: 0.0.1
      dom-serialize: 2.2.1
      glob: 7.2.3
      graceful-fs: 4.2.11
      http-proxy: 1.18.1
      isbinaryfile: 4.0.10
      lodash: 4.17.21
      log4js: 6.9.1
      mime: 2.6.0
      minimatch: 3.1.2
      mkdirp: 0.5.6
      qjobs: 1.2.0
      range-parser: 1.2.1
      rimraf: 3.0.2
      socket.io: 4.7.5
      source-map: 0.6.1
      tmp: 0.2.3
      ua-parser-js: 0.7.38
      yargs: 16.2.0
    transitivePeerDependencies:
      - bufferutil
      - debug
      - supports-color
      - utf-8-validate
    dev: true

  /keyv@4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==}
    dependencies:
      json-buffer: 3.0.1
    dev: true

  /kind-of@6.0.3:
    resolution: {integrity: sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==}
    engines: {node: '>=0.10.0'}
    dev: true

  /klona@2.0.6:
    resolution: {integrity: sha512-dhG34DXATL5hSxJbIexCft8FChFXtmskoZYnoPWjXQuebWYCNkVeV3KkGegCK9CP1oswI/vQibS2GY7Em/sJJA==}
    engines: {node: '>= 8'}
    dev: true

  /lcov-parse@1.0.0:
    resolution: {integrity: sha512-aprLII/vPzuQvYZnDRU78Fns9I2Ag3gi4Ipga/hxnVMCZC8DnR2nI7XBqrPoywGfxqIx/DgarGvDJZAD3YBTgQ==}
    hasBin: true
    dev: true

  /levn@0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0
    dev: true

  /lilconfig@3.1.2:
    resolution: {integrity: sha512-eop+wDAvpItUys0FWkHIKeC9ybYrTGbU41U5K7+bttZZeohvnY7M9dZ5kB21GNWiFT2q1OoPTvncPCgSOVO5ow==}
    engines: {node: '>=14'}
    dev: true

  /lines-and-columns@1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==}
    dev: true

  /lint-staged@10.5.4:
    resolution: {integrity: sha512-EechC3DdFic/TdOPgj/RB3FicqE6932LTHCUm0Y2fsD9KGlLB+RwJl2q1IYBIvEsKzDOgn0D4gll+YxG5RsrKg==}
    hasBin: true
    dependencies:
      chalk: 4.1.2
      cli-truncate: 2.1.0
      commander: 6.2.1
      cosmiconfig: 7.1.0
      debug: 4.3.6
      dedent: 0.7.0
      enquirer: 2.4.1
      execa: 4.1.0
      listr2: 3.14.0(enquirer@2.4.1)
      log-symbols: 4.1.0
      micromatch: 4.0.8
      normalize-path: 3.0.0
      please-upgrade-node: 3.2.0
      string-argv: 0.3.1
      stringify-object: 3.3.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /listr2@3.14.0(enquirer@2.4.1):
    resolution: {integrity: sha512-TyWI8G99GX9GjE54cJ+RrNMcIFBfwMPxc3XTFiAYGN4s10hWROGtOg7+O6u6LE3mNkyld7RSLE6nrKBvTfcs3g==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      enquirer: '>= 2.3.0 < 3'
    peerDependenciesMeta:
      enquirer:
        optional: true
    dependencies:
      cli-truncate: 2.1.0
      colorette: 2.0.20
      enquirer: 2.4.1
      log-update: 4.0.0
      p-map: 4.0.0
      rfdc: 1.4.1
      rxjs: 7.8.1
      through: 2.3.8
      wrap-ansi: 7.0.0
    dev: true

  /listr2@6.6.1:
    resolution: {integrity: sha512-+rAXGHh0fkEWdXBmX+L6mmfmXmXvDGEKzkjxO+8mP3+nI/r/CWznVBvsibXdxda9Zz0OW2e2ikphN3OwCT/jSg==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      enquirer: '>= 2.3.0 < 3'
    peerDependenciesMeta:
      enquirer:
        optional: true
    dependencies:
      cli-truncate: 3.1.0
      colorette: 2.0.20
      eventemitter3: 5.0.1
      log-update: 5.0.1
      rfdc: 1.4.1
      wrap-ansi: 8.1.0
    dev: true

  /loader-runner@4.3.0:
    resolution: {integrity: sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==}
    engines: {node: '>=6.11.5'}
    dev: true

  /loader-utils@2.0.4:
    resolution: {integrity: sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw==}
    engines: {node: '>=8.9.0'}
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 2.2.3
    dev: true

  /locate-path@5.0.0:
    resolution: {integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==}
    engines: {node: '>=8'}
    dependencies:
      p-locate: 4.1.0
    dev: true

  /locate-path@6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==}
    engines: {node: '>=10'}
    dependencies:
      p-locate: 5.0.0
    dev: true

  /lodash-es@4.17.21:
    resolution: {integrity: sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==}

  /lodash.get@4.4.2:
    resolution: {integrity: sha512-z+Uw/vLuy6gQe8cfaFWD7p0wVv8fJl3mbzXh33RS+0oW2wvUqiRXiQ69gLWSLpgB5/6sU+r6BlQR0MBILadqTQ==}
    dev: true

  /lodash.ismatch@4.4.0:
    resolution: {integrity: sha512-fPMfXjGQEV9Xsq/8MTSgUf255gawYRbjwMyDbcvDhXgV7enSZA0hynz6vMPnpAb5iONEzBHBPsT+0zes5Z301g==}
    dev: true

  /lodash.memoize@4.1.2:
    resolution: {integrity: sha512-t7j+NzmgnQzTAYXcsHYLgimltOV1MXHtlOWf6GjL9Kj8GK5FInw5JotxvbOs+IvV1/Dzo04/fCGfLVs7aXb4Ag==}
    dev: true

  /lodash.merge@4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}
    dev: true

  /lodash.truncate@4.4.2:
    resolution: {integrity: sha512-jttmRe7bRse52OsWIMDLaXxWqRAmtIUccAQ3garviCqJjafXOfNMO0yMfNpdD6zbGaTU0P5Nz7e7gAT6cKmJRw==}
    dev: true

  /lodash.uniq@4.5.0:
    resolution: {integrity: sha512-xfBaXQd9ryd9dlSDvnvI0lvxfLJlYAZzXomUYzLKtUeOQvOP5piqAWuGtrhWeqaXK9hhoM/iyJc5AV+XfsX3HQ==}
    dev: true

  /lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}
    dev: true

  /log-driver@1.2.7:
    resolution: {integrity: sha512-U7KCmLdqsGHBLeWqYlFA0V0Sl6P08EE1ZrmA9cxjUE0WVqT9qnyVDPz1kzpFEP0jdJuFnasWIfSd7fsaNXkpbg==}
    engines: {node: '>=0.8.6'}
    dev: true

  /log-symbols@2.2.0:
    resolution: {integrity: sha512-VeIAFslyIerEJLXHziedo2basKbMKtTw3vfn5IzG0XTjhAVEJyNHnL2p7vc+wBDSdQuUpNw3M2u6xb9QsAY5Eg==}
    engines: {node: '>=4'}
    dependencies:
      chalk: 2.4.2
    dev: true

  /log-symbols@4.1.0:
    resolution: {integrity: sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==}
    engines: {node: '>=10'}
    dependencies:
      chalk: 4.1.2
      is-unicode-supported: 0.1.0
    dev: true

  /log-update@4.0.0:
    resolution: {integrity: sha512-9fkkDevMefjg0mmzWFBW8YkFP91OrizzkW3diF7CpG+S2EYdy4+TVfGwz1zeF8x7hCx1ovSPTOE9Ngib74qqUg==}
    engines: {node: '>=10'}
    dependencies:
      ansi-escapes: 4.3.2
      cli-cursor: 3.1.0
      slice-ansi: 4.0.0
      wrap-ansi: 6.2.0
    dev: true

  /log-update@5.0.1:
    resolution: {integrity: sha512-5UtUDQ/6edw4ofyljDNcOVJQ4c7OjDro4h3y8e1GQL5iYElYclVHJ3zeWchylvMaKnDbDilC8irOVyexnA/Slw==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dependencies:
      ansi-escapes: 5.0.0
      cli-cursor: 4.0.0
      slice-ansi: 5.0.0
      strip-ansi: 7.1.0
      wrap-ansi: 8.1.0
    dev: true

  /log4js@6.9.1:
    resolution: {integrity: sha512-1somDdy9sChrr9/f4UlzhdaGfDR2c/SaD2a4T7qEkG4jTS57/B3qmnjLYePwQ8cqWnUHZI0iAKxMBpCZICiZ2g==}
    engines: {node: '>=8.0'}
    dependencies:
      date-format: 4.0.14
      debug: 4.3.6
      flatted: 3.3.1
      rfdc: 1.4.1
      streamroller: 3.1.5
    transitivePeerDependencies:
      - supports-color
    dev: true

  /loupe@2.3.7:
    resolution: {integrity: sha512-zSMINGVYkdpYSOBmLi0D1Uo7JU9nVdQKrHxC8eYlV+9YKK9WePqAlL7lSlorG/U2Fw1w0hTBmaa/jrQ3UbPHtA==}
    dependencies:
      get-func-name: 2.0.2
    dev: true

  /lru-cache@10.4.3:
    resolution: {integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==}
    dev: true

  /lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}
    dependencies:
      yallist: 3.1.1
    dev: true

  /lru-cache@6.0.0:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==}
    engines: {node: '>=10'}
    dependencies:
      yallist: 4.0.0
    dev: true

  /magic-string@0.30.11:
    resolution: {integrity: sha512-+Wri9p0QHMy+545hKww7YAu5NyzF8iomPL/RQazugQ9+Ez4Ic3mERMd8ZTX5rfK944j+560ZJi8iAwgak1Ac7A==}
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.0
    dev: true

  /make-dir@3.1.0:
    resolution: {integrity: sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==}
    engines: {node: '>=8'}
    dependencies:
      semver: 7.6.3
    dev: true

  /make-dir@4.0.0:
    resolution: {integrity: sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==}
    engines: {node: '>=10'}
    dependencies:
      semver: 7.6.3
    dev: true

  /map-obj@1.0.1:
    resolution: {integrity: sha512-7N/q3lyZ+LVCp7PzuxrJr4KMbBE2hW7BT7YNia330OFxIf4d3r5zVpicP2650l7CPN6RM9zOJRl3NGpqSiw3Eg==}
    engines: {node: '>=0.10.0'}
    dev: true

  /map-obj@4.3.0:
    resolution: {integrity: sha512-hdN1wVrZbb29eBGiGjJbeP8JbKjq1urkHJ/LIP/NY48MZ1QVXUsQBV1G1zvYFHn1XE06cwjBsOI2K3Ulnj1YXQ==}
    engines: {node: '>=8'}
    dev: true

  /marked@4.0.12:
    resolution: {integrity: sha512-hgibXWrEDNBWgGiK18j/4lkS6ihTe9sxtV4Q1OQppb/0zzyPSzoFANBa5MfsG/zgsWklmNnhm0XACZOH/0HBiQ==}
    engines: {node: '>= 12'}
    hasBin: true
    dev: true

  /mdn-data@2.0.28:
    resolution: {integrity: sha512-aylIc7Z9y4yzHYAJNuESG3hfhC+0Ibp/MAMiaOZgNv4pmEdFyfZhhhny4MNiAfWdBQ1RQ2mfDWmM1x8SvGyp8g==}
    dev: true

  /mdn-data@2.0.30:
    resolution: {integrity: sha512-GaqWWShW4kv/G9IEucWScBx9G1/vsFZZJUO+tD26M8J8z3Kw5RDQjaoZe03YAClgeS/SWPOcb4nkFBTEi5DUEA==}
    dev: true

  /media-typer@0.3.0:
    resolution: {integrity: sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==}
    engines: {node: '>= 0.6'}
    dev: true

  /meow@8.1.2:
    resolution: {integrity: sha512-r85E3NdZ+mpYk1C6RjPFEMSE+s1iZMuHtsHAqY0DT3jZczl0diWUZ8g6oU7h0M9cD2EL+PzaYghhCLzR0ZNn5Q==}
    engines: {node: '>=10'}
    dependencies:
      '@types/minimist': 1.2.5
      camelcase-keys: 6.2.2
      decamelize-keys: 1.1.1
      hard-rejection: 2.1.0
      minimist-options: 4.1.0
      normalize-package-data: 3.0.3
      read-pkg-up: 7.0.1
      redent: 3.0.0
      trim-newlines: 3.0.1
      type-fest: 0.18.1
      yargs-parser: 20.2.9
    dev: true

  /merge-stream@2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}
    dev: true

  /merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}
    dev: true

  /micromatch@4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==}
    engines: {node: '>=8.6'}
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1
    dev: true

  /mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}
    dev: true

  /mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.52.0
    dev: true

  /mime@2.6.0:
    resolution: {integrity: sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg==}
    engines: {node: '>=4.0.0'}
    hasBin: true
    dev: true

  /mimic-fn@2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==}
    engines: {node: '>=6'}
    dev: true

  /min-indent@1.0.1:
    resolution: {integrity: sha512-I9jwMn07Sy/IwOj3zVkVik2JTvgpaykDZEigL6Rx6N9LbMywwUSMtxET+7lVoDLLd3O3IXwJwvuuns8UB/HeAg==}
    engines: {node: '>=4'}
    dev: true

  /mini-css-extract-plugin@2.9.1(webpack@5.94.0):
    resolution: {integrity: sha512-+Vyi+GCCOHnrJ2VPS+6aPoXN2k2jgUzDRhTFLjjTBn23qyXJXkjUWQgTL+mXpF5/A8ixLdCc6kWsoeOjKGejKQ==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^5.0.0
    dependencies:
      schema-utils: 4.2.0
      tapable: 2.2.1
      webpack: 5.94.0(webpack-cli@4.10.0)
    dev: true

  /minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}
    dependencies:
      brace-expansion: 1.1.11
    dev: true

  /minimatch@4.2.1:
    resolution: {integrity: sha512-9Uq1ChtSZO+Mxa/CL1eGizn2vRn3MlLgzhT0Iz8zaY8NdvxvB0d5QdPFmCKf7JKA9Lerx5vRrnwO03jsSfGG9g==}
    engines: {node: '>=10'}
    dependencies:
      brace-expansion: 1.1.11
    dev: true

  /minimatch@9.0.1:
    resolution: {integrity: sha512-0jWhJpD/MdhPXwPuiRkCbfYfSKp2qnn2eOc279qI7f+osl/l+prKSrvhg157zSYvx/1nmgn2NqdT6k2Z7zSH9w==}
    engines: {node: '>=16 || 14 >=14.17'}
    dependencies:
      brace-expansion: 2.0.1
    dev: true

  /minimatch@9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==}
    engines: {node: '>=16 || 14 >=14.17'}
    dependencies:
      brace-expansion: 2.0.1
    dev: true

  /minimist-options@4.1.0:
    resolution: {integrity: sha512-Q4r8ghd80yhO/0j1O3B2BjweX3fiHg9cdOwjJd2J76Q135c+NDxGCqdYKQ1SKBuFfgWbAUzBfvYjPUEeNgqN1A==}
    engines: {node: '>= 6'}
    dependencies:
      arrify: 1.0.1
      is-plain-obj: 1.1.0
      kind-of: 6.0.3
    dev: true

  /minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}
    dev: true

  /minipass-collect@1.0.2:
    resolution: {integrity: sha512-6T6lH0H8OG9kITm/Jm6tdooIbogG9e0tLgpY6mphXSm/A9u8Nq1ryBG+Qspiub9LjWlBPsPS3tWQ/Botq4FdxA==}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.6
    dev: true

  /minipass-flush@1.0.5:
    resolution: {integrity: sha512-JmQSYYpPUqX5Jyn1mXaRwOda1uQ8HP5KAT/oDSLCzt1BYRhQU0/hDtsB1ufZfEEzMZ9aAVmsBw8+FWsIXlClWw==}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.6
    dev: true

  /minipass-pipeline@1.2.4:
    resolution: {integrity: sha512-xuIq7cIOt09RPRJ19gdi4b+RiNvDFYe5JH+ggNvBqGqpQXcru3PcRmOZuHBKWK1Txf9+cQ+HMVN4d6z46LZP7A==}
    engines: {node: '>=8'}
    dependencies:
      minipass: 3.3.6
    dev: true

  /minipass@3.3.6:
    resolution: {integrity: sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==}
    engines: {node: '>=8'}
    dependencies:
      yallist: 4.0.0
    dev: true

  /minipass@5.0.0:
    resolution: {integrity: sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==}
    engines: {node: '>=8'}
    dev: true

  /minipass@7.1.2:
    resolution: {integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==}
    engines: {node: '>=16 || 14 >=14.17'}
    dev: true

  /minizlib@2.1.2:
    resolution: {integrity: sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.6
      yallist: 4.0.0
    dev: true

  /mkdirp@0.5.6:
    resolution: {integrity: sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==}
    hasBin: true
    dependencies:
      minimist: 1.2.8
    dev: true

  /mkdirp@1.0.4:
    resolution: {integrity: sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==}
    engines: {node: '>=10'}
    hasBin: true
    dev: true

  /mocha@9.2.2:
    resolution: {integrity: sha512-L6XC3EdwT6YrIk0yXpavvLkn8h+EU+Y5UcCHKECyMbdUIxyMuZj4bX4U9e1nvnvUUvQVsV2VHQr5zLdcUkhW/g==}
    engines: {node: '>= 12.0.0'}
    hasBin: true
    dependencies:
      '@ungap/promise-all-settled': 1.1.2
      ansi-colors: 4.1.1
      browser-stdout: 1.3.1
      chokidar: 3.5.3
      debug: 4.3.3(supports-color@8.1.1)
      diff: 5.0.0
      escape-string-regexp: 4.0.0
      find-up: 5.0.0
      glob: 7.2.0
      growl: 1.10.5
      he: 1.2.0
      js-yaml: 4.1.0
      log-symbols: 4.1.0
      minimatch: 4.2.1
      ms: 2.1.3
      nanoid: 3.3.1
      serialize-javascript: 6.0.0
      strip-json-comments: 3.1.1
      supports-color: 8.1.1
      which: 2.0.2
      workerpool: 6.2.0
      yargs: 16.2.0
      yargs-parser: 20.2.4
      yargs-unparser: 2.0.0
    dev: true

  /modify-values@1.0.1:
    resolution: {integrity: sha512-xV2bxeN6F7oYjZWTe/YPAy6MN2M+sL4u/Rlm2AHCIVGfo2p1yGmBHQ6vHehl4bRTZBdHu3TSkWdYgkwpYzAGSw==}
    engines: {node: '>=0.10.0'}
    dev: true

  /ms@2.0.0:
    resolution: {integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==}
    dev: true

  /ms@2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==}
    dev: true

  /ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}
    dev: true

  /mute-stream@0.0.8:
    resolution: {integrity: sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA==}
    dev: true

  /nanoid@3.3.1:
    resolution: {integrity: sha512-n6Vs/3KGyxPQd6uO0eH4Bv0ojGSUvuLlIHtC3Y0kEO23YRge8H9x1GCzLn28YX0H66pMkxuaeESFq4tKISKwdw==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true
    dev: true

  /nanoid@3.3.7:
    resolution: {integrity: sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true
    dev: true

  /natural-compare-lite@1.4.0:
    resolution: {integrity: sha512-Tj+HTDSJJKaZnfiuw+iaF9skdPpTo2GtEly5JHnWV/hfv2Qj/9RKsGISQtLh2ox3l5EAGw487hnBee0sIJ6v2g==}
    dev: true

  /natural-compare@1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}
    dev: true

  /negotiator@0.6.3:
    resolution: {integrity: sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==}
    engines: {node: '>= 0.6'}
    dev: true

  /neo-async@2.6.2:
    resolution: {integrity: sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==}
    dev: true

  /nise@5.1.9:
    resolution: {integrity: sha512-qOnoujW4SV6e40dYxJOb3uvuoPHtmLzIk4TFo+j0jPJoC+5Z9xja5qH5JZobEPsa8+YYphMrOSwnrshEhG2qww==}
    dependencies:
      '@sinonjs/commons': 3.0.1
      '@sinonjs/fake-timers': 11.3.1
      '@sinonjs/text-encoding': 0.7.3
      just-extend: 6.2.0
      path-to-regexp: 6.2.2
    dev: true

  /node-fetch@2.7.0:
    resolution: {integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==}
    engines: {node: 4.x || >=6.0.0}
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true
    dependencies:
      whatwg-url: 5.0.0
    dev: true

  /node-releases@2.0.18:
    resolution: {integrity: sha512-d9VeXT4SJ7ZeOqGX6R5EM022wpL+eWPooLI+5UpWn2jCT1aosUQEhQP214x33Wkwx3JQMvIm+tIoVOdodFS40g==}
    dev: true

  /nopt@7.2.1:
    resolution: {integrity: sha512-taM24ViiimT/XntxbPyJQzCG+p4EKOpgD3mxFwW38mGjVUrfERQOeY4EDHjdnptttfHuHQXFx+lTP08Q+mLa/w==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}
    hasBin: true
    dependencies:
      abbrev: 2.0.0
    dev: true

  /normalize-package-data@2.5.0:
    resolution: {integrity: sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==}
    dependencies:
      hosted-git-info: 2.8.9
      resolve: 1.22.8
      semver: 7.6.3
      validate-npm-package-license: 3.0.4
    dev: true

  /normalize-package-data@3.0.3:
    resolution: {integrity: sha512-p2W1sgqij3zMMyRC067Dg16bfzVH+w7hyegmpIvZ4JNjqtGOVAIvLmjBx3yP7YTe9vKJgkoNOPjwQGogDoMXFA==}
    engines: {node: '>=10'}
    dependencies:
      hosted-git-info: 4.1.0
      is-core-module: 2.15.1
      semver: 7.6.3
      validate-npm-package-license: 3.0.4
    dev: true

  /normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /npm-run-path@4.0.1:
    resolution: {integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==}
    engines: {node: '>=8'}
    dependencies:
      path-key: 3.1.1
    dev: true

  /nth-check@2.1.1:
    resolution: {integrity: sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==}
    dependencies:
      boolbase: 1.0.0
    dev: true

  /nwsapi@2.2.12:
    resolution: {integrity: sha512-qXDmcVlZV4XRtKFzddidpfVP4oMSGhga+xdMc25mv8kaLUHtgzCDhUxkrN8exkGdTlLNaXj7CV3GtON7zuGZ+w==}
    dev: true

  /oauth-sign@0.9.0:
    resolution: {integrity: sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==}
    dev: true

  /object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}
    dev: true

  /object-inspect@1.13.2:
    resolution: {integrity: sha512-IRZSRuzJiynemAXPYtPe5BoI/RESNYR7TYm50MC5Mqbd3Jmw5y790sErYw3V6SryFJD64b74qQQs9wn5Bg/k3g==}
    engines: {node: '>= 0.4'}
    dev: true

  /on-finished@2.3.0:
    resolution: {integrity: sha512-ikqdkGAAyf/X/gPhXGvfgAytDZtDbr+bkNUJ0N9h5MI/dmdgCs3l6hoHrcUv41sRKew3jIwrp4qQDXiK99Utww==}
    engines: {node: '>= 0.8'}
    dependencies:
      ee-first: 1.1.1
    dev: true

  /on-finished@2.4.1:
    resolution: {integrity: sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==}
    engines: {node: '>= 0.8'}
    dependencies:
      ee-first: 1.1.1
    dev: true

  /once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}
    dependencies:
      wrappy: 1.0.2
    dev: true

  /onetime@5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==}
    engines: {node: '>=6'}
    dependencies:
      mimic-fn: 2.1.0
    dev: true

  /optionator@0.9.4:
    resolution: {integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5
    dev: true

  /os-tmpdir@1.0.2:
    resolution: {integrity: sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==}
    engines: {node: '>=0.10.0'}
    dev: true

  /p-limit@2.3.0:
    resolution: {integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==}
    engines: {node: '>=6'}
    dependencies:
      p-try: 2.2.0
    dev: true

  /p-limit@3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    engines: {node: '>=10'}
    dependencies:
      yocto-queue: 0.1.0
    dev: true

  /p-locate@4.1.0:
    resolution: {integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==}
    engines: {node: '>=8'}
    dependencies:
      p-limit: 2.3.0
    dev: true

  /p-locate@5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==}
    engines: {node: '>=10'}
    dependencies:
      p-limit: 3.1.0
    dev: true

  /p-map@3.0.0:
    resolution: {integrity: sha512-d3qXVTF/s+W+CdJ5A29wywV2n8CQQYahlgz2bFiA+4eVNJbHJodPZ+/gXwPGh0bOqA+j8S+6+ckmvLGPk1QpxQ==}
    engines: {node: '>=8'}
    dependencies:
      aggregate-error: 3.1.0
    dev: true

  /p-map@4.0.0:
    resolution: {integrity: sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==}
    engines: {node: '>=10'}
    dependencies:
      aggregate-error: 3.1.0
    dev: true

  /p-try@2.2.0:
    resolution: {integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==}
    engines: {node: '>=6'}
    dev: true

  /package-json-from-dist@1.0.0:
    resolution: {integrity: sha512-dATvCeZN/8wQsGywez1mzHtTlP22H8OEfPrVMLNr4/eGa+ijtLn/6M5f0dY8UKNrC2O9UCU6SSoG3qRKnt7STw==}
    dev: true

  /parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}
    dependencies:
      callsites: 3.1.0
    dev: true

  /parse-github-url@1.0.3:
    resolution: {integrity: sha512-tfalY5/4SqGaV/GIGzWyHnFjlpTPTNpENR9Ea2lLldSJ8EWXMsvacWucqY3m3I4YPtas15IxTLQVQ5NSYXPrww==}
    engines: {node: '>= 0.10'}
    hasBin: true
    dev: true

  /parse-json@5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==}
    engines: {node: '>=8'}
    dependencies:
      '@babel/code-frame': 7.24.7
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4
    dev: true

  /parse5@6.0.1:
    resolution: {integrity: sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw==}
    dev: true

  /parseurl@1.3.3:
    resolution: {integrity: sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==}
    engines: {node: '>= 0.8'}
    dev: true

  /path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}
    dev: true

  /path-is-absolute@1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    engines: {node: '>=0.10.0'}
    dev: true

  /path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}
    dev: true

  /path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}
    dev: true

  /path-scurry@1.11.1:
    resolution: {integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==}
    engines: {node: '>=16 || 14 >=14.18'}
    dependencies:
      lru-cache: 10.4.3
      minipass: 7.1.2
    dev: true

  /path-to-regexp@6.2.2:
    resolution: {integrity: sha512-GQX3SSMokngb36+whdpRXE+3f9V8UzyAorlYvOGx87ufGHehNTn5lCxrKtLyZ4Yl/wEKnNnr98ZzOwwDZV5ogw==}
    dev: true

  /path-type@4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}
    dev: true

  /pathval@1.1.1:
    resolution: {integrity: sha512-Dp6zGqpTdETdR63lehJYPeIOqpiNBNtc7BpWSLrOje7UaIsE5aY92r/AunQA7rsXvet3lrJ3JnZX29UPTKXyKQ==}
    dev: true

  /performance-now@2.1.0:
    resolution: {integrity: sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow==}
    dev: true

  /picocolors@1.0.1:
    resolution: {integrity: sha512-anP1Z8qwhkbmu7MFP5iTt+wQKXgwzf7zTyGlcdzabySa9vd0Xt392U0rVmz9poOaBj0uHJKyyo9/upk0HrEQew==}
    dev: true

  /picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}
    dev: true

  /pify@2.3.0:
    resolution: {integrity: sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==}
    engines: {node: '>=0.10.0'}
    dev: true

  /pkg-dir@4.2.0:
    resolution: {integrity: sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==}
    engines: {node: '>=8'}
    dependencies:
      find-up: 4.1.0
    dev: true

  /please-upgrade-node@3.2.0:
    resolution: {integrity: sha512-gQR3WpIgNIKwBMVLkpMUeR3e1/E1y42bqDQZfql+kDeXd8COYfM8PQA4X6y7a8u9Ua9FHmsrrmirW2vHs45hWg==}
    dependencies:
      semver-compare: 1.0.0
    dev: true

  /pofile@1.1.4:
    resolution: {integrity: sha512-r6Q21sKsY1AjTVVjOuU02VYKVNQGJNQHjTIvs4dEbeuuYfxgYk/DGD2mqqq4RDaVkwdSq0VEtmQUOPe/wH8X3g==}
    dev: true

  /postcss-calc@9.0.1(postcss@8.4.41):
    resolution: {integrity: sha512-TipgjGyzP5QzEhsOZUaIkeO5mKeMFpebWzRogWG/ysonUlnHcq5aJe0jOjpfzUU8PeSaBQnrE8ehR0QA5vs8PQ==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.2.2
    dependencies:
      postcss: 8.4.41
      postcss-selector-parser: 6.1.2
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-colormin@6.1.0(postcss@8.4.41):
    resolution: {integrity: sha512-x9yX7DOxeMAR+BgGVnNSAxmAj98NX/YxEMNFP+SDCEeNLb2r3i6Hh1ksMsnW8Ub5SLCpbescQqn9YEbE9554Sw==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      browserslist: 4.23.3
      caniuse-api: 3.0.0
      colord: 2.9.3
      postcss: 8.4.41
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-convert-values@6.1.0(postcss@8.4.41):
    resolution: {integrity: sha512-zx8IwP/ts9WvUM6NkVSkiU902QZL1bwPhaVaLynPtCsOTqp+ZKbNi+s6XJg3rfqpKGA/oc7Oxk5t8pOQJcwl/w==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      browserslist: 4.23.3
      postcss: 8.4.41
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-discard-comments@6.0.2(postcss@8.4.41):
    resolution: {integrity: sha512-65w/uIqhSBBfQmYnG92FO1mWZjJ4GL5b8atm5Yw2UgrwD7HiNiSSNwJor1eCFGzUgYnN/iIknhNRVqjrrpuglw==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.41
    dev: true

  /postcss-discard-duplicates@6.0.3(postcss@8.4.41):
    resolution: {integrity: sha512-+JA0DCvc5XvFAxwx6f/e68gQu/7Z9ud584VLmcgto28eB8FqSFZwtrLwB5Kcp70eIoWP/HXqz4wpo8rD8gpsTw==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.41
    dev: true

  /postcss-discard-empty@6.0.3(postcss@8.4.41):
    resolution: {integrity: sha512-znyno9cHKQsK6PtxL5D19Fj9uwSzC2mB74cpT66fhgOadEUPyXFkbgwm5tvc3bt3NAy8ltE5MrghxovZRVnOjQ==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.41
    dev: true

  /postcss-discard-overridden@6.0.2(postcss@8.4.41):
    resolution: {integrity: sha512-j87xzI4LUggC5zND7KdjsI25APtyMuynXZSujByMaav2roV6OZX+8AaCUcZSWqckZpjAjRyFDdpqybgjFO0HJQ==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.41
    dev: true

  /postcss-import@14.1.0(postcss@8.4.41):
    resolution: {integrity: sha512-flwI+Vgm4SElObFVPpTIT7SU7R3qk2L7PyduMcokiaVKuWv9d/U+Gm/QAd8NDLuykTWTkcrjOeD2Pp1rMeBTGw==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      postcss: ^8.0.0
    dependencies:
      postcss: 8.4.41
      postcss-value-parser: 4.2.0
      read-cache: 1.0.0
      resolve: 1.22.8
    dev: true

  /postcss-js@4.0.1(postcss@8.4.41):
    resolution: {integrity: sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==}
    engines: {node: ^12 || ^14 || >= 16}
    peerDependencies:
      postcss: ^8.4.21
    dependencies:
      camelcase-css: 2.0.1
      postcss: 8.4.41
    dev: true

  /postcss-loader@4.3.0(postcss@8.4.41)(webpack@5.94.0):
    resolution: {integrity: sha512-M/dSoIiNDOo8Rk0mUqoj4kpGq91gcxCfb9PoyZVdZ76/AuhxylHDYZblNE8o+EQ9AMSASeMFEKxZf5aU6wlx1Q==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      postcss: ^7.0.0 || ^8.0.1
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      cosmiconfig: 7.1.0
      klona: 2.0.6
      loader-utils: 2.0.4
      postcss: 8.4.41
      schema-utils: 3.3.0
      semver: 7.6.3
      webpack: 5.94.0(webpack-cli@4.10.0)
    dev: true

  /postcss-merge-longhand@6.0.5(postcss@8.4.41):
    resolution: {integrity: sha512-5LOiordeTfi64QhICp07nzzuTDjNSO8g5Ksdibt44d+uvIIAE1oZdRn8y/W5ZtYgRH/lnLDlvi9F8btZcVzu3w==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.41
      postcss-value-parser: 4.2.0
      stylehacks: 6.1.1(postcss@8.4.41)
    dev: true

  /postcss-merge-rules@6.1.1(postcss@8.4.41):
    resolution: {integrity: sha512-KOdWF0gju31AQPZiD+2Ar9Qjowz1LTChSjFFbS+e2sFgc4uHOp3ZvVX4sNeTlk0w2O31ecFGgrFzhO0RSWbWwQ==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      browserslist: 4.23.3
      caniuse-api: 3.0.0
      cssnano-utils: 4.0.2(postcss@8.4.41)
      postcss: 8.4.41
      postcss-selector-parser: 6.1.2
    dev: true

  /postcss-minify-font-values@6.1.0(postcss@8.4.41):
    resolution: {integrity: sha512-gklfI/n+9rTh8nYaSJXlCo3nOKqMNkxuGpTn/Qm0gstL3ywTr9/WRKznE+oy6fvfolH6dF+QM4nCo8yPLdvGJg==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.41
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-minify-gradients@6.0.3(postcss@8.4.41):
    resolution: {integrity: sha512-4KXAHrYlzF0Rr7uc4VrfwDJ2ajrtNEpNEuLxFgwkhFZ56/7gaE4Nr49nLsQDZyUe+ds+kEhf+YAUolJiYXF8+Q==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      colord: 2.9.3
      cssnano-utils: 4.0.2(postcss@8.4.41)
      postcss: 8.4.41
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-minify-params@6.1.0(postcss@8.4.41):
    resolution: {integrity: sha512-bmSKnDtyyE8ujHQK0RQJDIKhQ20Jq1LYiez54WiaOoBtcSuflfK3Nm596LvbtlFcpipMjgClQGyGr7GAs+H1uA==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      browserslist: 4.23.3
      cssnano-utils: 4.0.2(postcss@8.4.41)
      postcss: 8.4.41
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-minify-selectors@6.0.4(postcss@8.4.41):
    resolution: {integrity: sha512-L8dZSwNLgK7pjTto9PzWRoMbnLq5vsZSTu8+j1P/2GB8qdtGQfn+K1uSvFgYvgh83cbyxT5m43ZZhUMTJDSClQ==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.41
      postcss-selector-parser: 6.1.2
    dev: true

  /postcss-mixins@9.0.4(postcss@8.4.41):
    resolution: {integrity: sha512-XVq5jwQJDRu5M1XGkdpgASqLk37OqkH4JCFDXl/Dn7janOJjCTEKL+36cnRVy7bMtoBzALfO7bV7nTIsFnUWLA==}
    engines: {node: '>=14.0'}
    peerDependencies:
      postcss: ^8.2.14
    dependencies:
      fast-glob: 3.3.2
      postcss: 8.4.41
      postcss-js: 4.0.1(postcss@8.4.41)
      postcss-simple-vars: 7.0.1(postcss@8.4.41)
      sugarss: 4.0.1(postcss@8.4.41)
    dev: true

  /postcss-modules-extract-imports@3.1.0(postcss@8.4.41):
    resolution: {integrity: sha512-k3kNe0aNFQDAZGbin48pL2VNidTF0w4/eASDsxlyspobzU3wZQLOGj7L9gfRe0Jo9/4uud09DsjFNH7winGv8Q==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      postcss: 8.4.41
    dev: true

  /postcss-modules-local-by-default@4.0.5(postcss@8.4.41):
    resolution: {integrity: sha512-6MieY7sIfTK0hYfafw1OMEG+2bg8Q1ocHCpoWLqOKj3JXlKu4G7btkmM/B7lFubYkYWmRSPLZi5chid63ZaZYw==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      icss-utils: 5.1.0(postcss@8.4.41)
      postcss: 8.4.41
      postcss-selector-parser: 6.1.2
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-modules-scope@3.2.0(postcss@8.4.41):
    resolution: {integrity: sha512-oq+g1ssrsZOsx9M96c5w8laRmvEu9C3adDSjI8oTcbfkrTE8hx/zfyobUoWIxaKPO8bt6S62kxpw5GqypEw1QQ==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      postcss: 8.4.41
      postcss-selector-parser: 6.1.2
    dev: true

  /postcss-modules-values@4.0.0(postcss@8.4.41):
    resolution: {integrity: sha512-RDxHkAiEGI78gS2ofyvCsu7iycRv7oqw5xMWn9iMoR0N/7mf9D50ecQqUo5BZ9Zh2vH4bCUR/ktCqbB9m8vJjQ==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      icss-utils: 5.1.0(postcss@8.4.41)
      postcss: 8.4.41
    dev: true

  /postcss-nesting@10.2.0(postcss@8.4.41):
    resolution: {integrity: sha512-EwMkYchxiDiKUhlJGzWsD9b2zvq/r2SSubcRrgP+jujMXFzqvANLt16lJANC+5uZ6hjI7lpRmI6O8JIl+8l1KA==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      '@csstools/selector-specificity': 2.2.0(postcss-selector-parser@6.1.2)
      postcss: 8.4.41
      postcss-selector-parser: 6.1.2
    dev: true

  /postcss-normalize-charset@6.0.2(postcss@8.4.41):
    resolution: {integrity: sha512-a8N9czmdnrjPHa3DeFlwqst5eaL5W8jYu3EBbTTkI5FHkfMhFZh1EGbku6jhHhIzTA6tquI2P42NtZ59M/H/kQ==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.41
    dev: true

  /postcss-normalize-display-values@6.0.2(postcss@8.4.41):
    resolution: {integrity: sha512-8H04Mxsb82ON/aAkPeq8kcBbAtI5Q2a64X/mnRRfPXBq7XeogoQvReqxEfc0B4WPq1KimjezNC8flUtC3Qz6jg==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.41
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-normalize-positions@6.0.2(postcss@8.4.41):
    resolution: {integrity: sha512-/JFzI441OAB9O7VnLA+RtSNZvQ0NCFZDOtp6QPFo1iIyawyXg0YI3CYM9HBy1WvwCRHnPep/BvI1+dGPKoXx/Q==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.41
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-normalize-repeat-style@6.0.2(postcss@8.4.41):
    resolution: {integrity: sha512-YdCgsfHkJ2jEXwR4RR3Tm/iOxSfdRt7jplS6XRh9Js9PyCR/aka/FCb6TuHT2U8gQubbm/mPmF6L7FY9d79VwQ==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.41
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-normalize-string@6.0.2(postcss@8.4.41):
    resolution: {integrity: sha512-vQZIivlxlfqqMp4L9PZsFE4YUkWniziKjQWUtsxUiVsSSPelQydwS8Wwcuw0+83ZjPWNTl02oxlIvXsmmG+CiQ==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.41
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-normalize-timing-functions@6.0.2(postcss@8.4.41):
    resolution: {integrity: sha512-a+YrtMox4TBtId/AEwbA03VcJgtyW4dGBizPl7e88cTFULYsprgHWTbfyjSLyHeBcK/Q9JhXkt2ZXiwaVHoMzA==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.41
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-normalize-unicode@6.1.0(postcss@8.4.41):
    resolution: {integrity: sha512-QVC5TQHsVj33otj8/JD869Ndr5Xcc/+fwRh4HAsFsAeygQQXm+0PySrKbr/8tkDKzW+EVT3QkqZMfFrGiossDg==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      browserslist: 4.23.3
      postcss: 8.4.41
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-normalize-url@6.0.2(postcss@8.4.41):
    resolution: {integrity: sha512-kVNcWhCeKAzZ8B4pv/DnrU1wNh458zBNp8dh4y5hhxih5RZQ12QWMuQrDgPRw3LRl8mN9vOVfHl7uhvHYMoXsQ==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.41
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-normalize-whitespace@6.0.2(postcss@8.4.41):
    resolution: {integrity: sha512-sXZ2Nj1icbJOKmdjXVT9pnyHQKiSAyuNQHSgRCUgThn2388Y9cGVDR+E9J9iAYbSbLHI+UUwLVl1Wzco/zgv0Q==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.41
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-ordered-values@6.0.2(postcss@8.4.41):
    resolution: {integrity: sha512-VRZSOB+JU32RsEAQrO94QPkClGPKJEL/Z9PCBImXMhIeK5KAYo6slP/hBYlLgrCjFxyqvn5VC81tycFEDBLG1Q==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      cssnano-utils: 4.0.2(postcss@8.4.41)
      postcss: 8.4.41
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-reduce-initial@6.1.0(postcss@8.4.41):
    resolution: {integrity: sha512-RarLgBK/CrL1qZags04oKbVbrrVK2wcxhvta3GCxrZO4zveibqbRPmm2VI8sSgCXwoUHEliRSbOfpR0b/VIoiw==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      browserslist: 4.23.3
      caniuse-api: 3.0.0
      postcss: 8.4.41
    dev: true

  /postcss-reduce-transforms@6.0.2(postcss@8.4.41):
    resolution: {integrity: sha512-sB+Ya++3Xj1WaT9+5LOOdirAxP7dJZms3GRcYheSPi1PiTMigsxHAdkrbItHxwYHr4kt1zL7mmcHstgMYT+aiA==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.41
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-selector-parser@6.1.2:
    resolution: {integrity: sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==}
    engines: {node: '>=4'}
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2
    dev: true

  /postcss-simple-vars@7.0.1(postcss@8.4.41):
    resolution: {integrity: sha512-5GLLXaS8qmzHMOjVxqkk1TZPf1jMqesiI7qLhnlyERalG0sMbHIbJqrcnrpmZdKCLglHnRHoEBB61RtGTsj++A==}
    engines: {node: '>=14.0'}
    peerDependencies:
      postcss: ^8.2.1
    dependencies:
      postcss: 8.4.41
    dev: true

  /postcss-svgo@6.0.3(postcss@8.4.41):
    resolution: {integrity: sha512-dlrahRmxP22bX6iKEjOM+c8/1p+81asjKT+V5lrgOH944ryx/OHpclnIbGsKVd3uWOXFLYJwCVf0eEkJGvO96g==}
    engines: {node: ^14 || ^16 || >= 18}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.41
      postcss-value-parser: 4.2.0
      svgo: 3.3.2
    dev: true

  /postcss-unique-selectors@6.0.4(postcss@8.4.41):
    resolution: {integrity: sha512-K38OCaIrO8+PzpArzkLKB42dSARtC2tmG6PvD4b1o1Q2E9Os8jzfWFfSy/rixsHwohtsDdFtAWGjFVFUdwYaMg==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.41
      postcss-selector-parser: 6.1.2
    dev: true

  /postcss-value-parser@4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}
    dev: true

  /postcss@8.4.41:
    resolution: {integrity: sha512-TesUflQ0WKZqAvg52PWL6kHgLKP6xB6heTOdoYM0Wt2UHyxNa4K25EZZMgKns3BH1RLVbZCREPpLY0rhnNoHVQ==}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.7
      picocolors: 1.0.1
      source-map-js: 1.2.0
    dev: true

  /prelude-ls@1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==}
    engines: {node: '>= 0.8.0'}
    dev: true

  /progress@2.0.3:
    resolution: {integrity: sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==}
    engines: {node: '>=0.4.0'}
    dev: true

  /promise-inflight@1.0.1:
    resolution: {integrity: sha512-6zWPyEOFaQBJYcGMHBKTKJ3u6TBsnMFOIZSa6ce1e/ZrrsOlnHRHbabMjLiBYKp+n44X9eUI6VUPaukCXHuG4g==}
    peerDependencies:
      bluebird: '*'
    peerDependenciesMeta:
      bluebird:
        optional: true
    dev: true

  /proto-list@1.2.4:
    resolution: {integrity: sha512-vtK/94akxsTMhe0/cbfpR+syPuszcuwhqVjJq26CuNDgFGj682oRBXOP5MJpv2r7JtE8MsiepGIqvvOTBwn2vA==}
    dev: true

  /psl@1.9.0:
    resolution: {integrity: sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag==}
    dev: true

  /pump@3.0.0:
    resolution: {integrity: sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==}
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0
    dev: true

  /punycode@1.4.1:
    resolution: {integrity: sha512-jmYNElW7yvO7TV33CjSmvSiE2yco3bV2czu/OzDKdMNVZQWfxCblURLhf+47syQRBntjfLdd/H0egrzIG+oaFQ==}
    dev: true

  /punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}
    dev: true

  /qjobs@1.2.0:
    resolution: {integrity: sha512-8YOJEHtxpySA3fFDyCRxA+UUV+fA+rTWnuWvylOK/NCjhY+b4ocCtmu8TtsWb+mYeU+GCHf/S66KZF/AsteKHg==}
    engines: {node: '>=0.9'}
    dev: true

  /qs@6.11.0:
    resolution: {integrity: sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==}
    engines: {node: '>=0.6'}
    dependencies:
      side-channel: 1.0.6
    dev: true

  /qs@6.5.3:
    resolution: {integrity: sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA==}
    engines: {node: '>=0.6'}
    dev: true

  /querystringify@2.2.0:
    resolution: {integrity: sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==}
    dev: true

  /queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}
    dev: true

  /quick-lru@4.0.1:
    resolution: {integrity: sha512-ARhCpm70fzdcvNQfPoy49IaanKkTlRWF2JMzqhcJbhSFRZv7nPTvZJdcY7301IPmvW+/p0RgIWnQDLJxifsQ7g==}
    engines: {node: '>=8'}
    dev: true

  /ramda@0.27.2:
    resolution: {integrity: sha512-SbiLPU40JuJniHexQSAgad32hfwd+DRUdwF2PlVuI5RZD0/vahUco7R8vD86J/tcEKKF9vZrUVwgtmGCqlCKyA==}
    dev: true

  /randombytes@2.1.0:
    resolution: {integrity: sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==}
    dependencies:
      safe-buffer: 5.2.1
    dev: true

  /range-parser@1.2.1:
    resolution: {integrity: sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==}
    engines: {node: '>= 0.6'}
    dev: true

  /raw-body@2.5.2:
    resolution: {integrity: sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==}
    engines: {node: '>= 0.8'}
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      unpipe: 1.0.0
    dev: true

  /raw-loader@4.0.2(webpack@5.94.0):
    resolution: {integrity: sha512-ZnScIV3ag9A4wPX/ZayxL/jZH+euYb6FcUinPcgiQW0+UBtEv0O6Q3lGd3cqJ+GHH+rksEv3Pj99oxJ3u3VIKA==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      loader-utils: 2.0.4
      schema-utils: 3.3.0
      webpack: 5.94.0(webpack-cli@4.10.0)
    dev: true

  /read-cache@1.0.0:
    resolution: {integrity: sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==}
    dependencies:
      pify: 2.3.0
    dev: true

  /read-pkg-up@7.0.1:
    resolution: {integrity: sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg==}
    engines: {node: '>=8'}
    dependencies:
      find-up: 4.1.0
      read-pkg: 5.2.0
      type-fest: 0.8.1
    dev: true

  /read-pkg@5.2.0:
    resolution: {integrity: sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg==}
    engines: {node: '>=8'}
    dependencies:
      '@types/normalize-package-data': 2.4.4
      normalize-package-data: 2.5.0
      parse-json: 5.2.0
      type-fest: 0.6.0
    dev: true

  /readable-stream@3.6.2:
    resolution: {integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==}
    engines: {node: '>= 6'}
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2
    dev: true

  /readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}
    dependencies:
      picomatch: 2.3.1
    dev: true

  /rechoir@0.6.2:
    resolution: {integrity: sha512-HFM8rkZ+i3zrV+4LQjwQ0W+ez98pApMGM3HUrN04j3CqzPOzl9nmP15Y8YXNm8QHGv/eacOVEjqhmWpkRV0NAw==}
    engines: {node: '>= 0.10'}
    dependencies:
      resolve: 1.22.8
    dev: true

  /rechoir@0.7.1:
    resolution: {integrity: sha512-/njmZ8s1wVeR6pjTZ+0nCnv8SpZNRMT2D1RLOJQESlYFDBvwpTA4KWJpZ+sBJ4+vhjILRcK7JIFdGCdxEAAitg==}
    engines: {node: '>= 0.10'}
    dependencies:
      resolve: 1.22.8
    dev: true

  /redent@3.0.0:
    resolution: {integrity: sha512-6tDA8g98We0zd0GvVeMT9arEOnTw9qM03L9cJXaCjrip1OO764RDBLBfrB4cwzNGDj5OA5ioymC9GkizgWJDUg==}
    engines: {node: '>=8'}
    dependencies:
      indent-string: 4.0.0
      strip-indent: 3.0.0
    dev: true

  /regenerator-runtime@0.14.1:
    resolution: {integrity: sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==}
    dev: true

  /regexpp@3.2.0:
    resolution: {integrity: sha512-pq2bWo9mVD43nbts2wGv17XLiNLya+GklZ8kaDLV2Z08gDCsGpnKn9BFMepvWuHCbyVvY7J5o5+BVvoQbmlJLg==}
    engines: {node: '>=8'}
    dev: true

  /request@2.88.2:
    resolution: {integrity: sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw==}
    engines: {node: '>= 6'}
    deprecated: request has been deprecated, see https://github.com/request/request/issues/3142
    dependencies:
      aws-sign2: 0.7.0
      aws4: 1.13.1
      caseless: 0.12.0
      combined-stream: 1.0.8
      extend: 3.0.2
      forever-agent: 0.6.1
      form-data: 2.3.3
      har-validator: 5.1.5
      http-signature: 1.2.0
      is-typedarray: 1.0.0
      isstream: 0.1.2
      json-stringify-safe: 5.0.1
      mime-types: 2.1.35
      oauth-sign: 0.9.0
      performance-now: 2.1.0
      qs: 6.5.3
      safe-buffer: 5.2.1
      tough-cookie: 2.5.0
      tunnel-agent: 0.6.0
      uuid: 3.4.0
    dev: true

  /require-directory@2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==}
    engines: {node: '>=0.10.0'}
    dev: true

  /require-from-string@2.0.2:
    resolution: {integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==}
    engines: {node: '>=0.10.0'}
    dev: true

  /requires-port@1.0.0:
    resolution: {integrity: sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==}
    dev: true

  /resolve-cwd@3.0.0:
    resolution: {integrity: sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==}
    engines: {node: '>=8'}
    dependencies:
      resolve-from: 5.0.0
    dev: true

  /resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}
    dev: true

  /resolve-from@5.0.0:
    resolution: {integrity: sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==}
    engines: {node: '>=8'}
    dev: true

  /resolve-pkg-maps@1.0.0:
    resolution: {integrity: sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==}
    dev: true

  /resolve.exports@2.0.2:
    resolution: {integrity: sha512-X2UW6Nw3n/aMgDVy+0rSqgHlv39WZAlZrXCdnbyEiKm17DSqHX4MmQMaST3FbeWR5FTuRcUwYAziZajji0Y7mg==}
    engines: {node: '>=10'}
    dev: true

  /resolve@1.22.8:
    resolution: {integrity: sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==}
    hasBin: true
    dependencies:
      is-core-module: 2.15.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0
    dev: true

  /restore-cursor@3.1.0:
    resolution: {integrity: sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==}
    engines: {node: '>=8'}
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7
    dev: true

  /restore-cursor@4.0.0:
    resolution: {integrity: sha512-I9fPXU9geO9bHOt9pHHOhOkYerIMsmVaWB0rA2AI9ERh/+x/i7MV5HKBNrg+ljO5eoPVgCcnFuRjJ9uH6I/3eg==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7
    dev: true

  /reusify@1.0.4:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}
    dev: true

  /rfdc@1.4.1:
    resolution: {integrity: sha512-q1b3N5QkRUWUl7iyylaaj3kOpIT0N2i9MqIEQXP73GVsN9cw3fdx8X63cEmWhJGi2PPCF23Ijp7ktmd39rawIA==}
    dev: true

  /rimraf@3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true
    dependencies:
      glob: 7.2.3
    dev: true

  /run-async@2.4.1:
    resolution: {integrity: sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ==}
    engines: {node: '>=0.12.0'}
    dev: true

  /run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}
    dependencies:
      queue-microtask: 1.2.3
    dev: true

  /rxjs@6.6.7:
    resolution: {integrity: sha512-hTdwr+7yYNIT5n4AMYp85KA6yw2Va0FLa3Rguvbpa4W3I5xynaBZo41cM3XM+4Q6fRMj3sBYIR1VAmZMXYJvRQ==}
    engines: {npm: '>=2.0.0'}
    dependencies:
      tslib: 1.14.1
    dev: true

  /rxjs@7.8.1:
    resolution: {integrity: sha512-AA3TVj+0A2iuIoQkWEK/tqFjBq2j+6PO6Y0zJcvzLAFhEFIO3HL0vls9hWLncZbAAbK0mar7oZ4V079I/qPMxg==}
    dependencies:
      tslib: 2.7.0
    dev: true

  /safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}
    dev: true

  /safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}
    dev: true

  /saxes@5.0.1:
    resolution: {integrity: sha512-5LBh1Tls8c9xgGjw3QrMwETmTMVk0oFgvrFSvWx62llR2hcEInrKNZ2GZCCuuy2lvWrdl5jhbpeqc5hRYKFOcw==}
    engines: {node: '>=10'}
    dependencies:
      xmlchars: 2.2.0
    dev: true

  /schema-utils@2.7.1:
    resolution: {integrity: sha512-SHiNtMOUGWBQJwzISiVYKu82GiV4QYGePp3odlY1tuKO7gPtphAT5R/py0fA6xtbgLL/RvtJZnU9b8s0F1q0Xg==}
    engines: {node: '>= 8.9.0'}
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)
    dev: true

  /schema-utils@3.3.0:
    resolution: {integrity: sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg==}
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)
    dev: true

  /schema-utils@4.2.0:
    resolution: {integrity: sha512-L0jRsrPpjdckP3oPug3/VxNKt2trR8TcabrM6FOAAlvC/9Phcmm+cuAgTlxBqdBR1WJx7Naj9WHw+aOmheSVbw==}
    engines: {node: '>= 12.13.0'}
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 8.17.1
      ajv-formats: 2.1.1(ajv@8.17.1)
      ajv-keywords: 5.1.0(ajv@8.17.1)
    dev: true

  /semver-compare@1.0.0:
    resolution: {integrity: sha512-YM3/ITh2MJ5MtzaM429anh+x2jiLVjqILF4m4oyQB18W7Ggea7BfqdH/wGMK7dDiMghv/6WG7znWMwUDzJiXow==}
    dev: true

  /semver@7.6.3:
    resolution: {integrity: sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==}
    engines: {node: '>=10'}
    hasBin: true
    dev: true

  /serialize-javascript@4.0.0:
    resolution: {integrity: sha512-GaNA54380uFefWghODBWEGisLZFj00nS5ACs6yHa9nLqlLpVLO8ChDGeKRjZnV4Nh4n0Qi7nhYZD/9fCPzEqkw==}
    dependencies:
      randombytes: 2.1.0
    dev: true

  /serialize-javascript@5.0.1:
    resolution: {integrity: sha512-SaaNal9imEO737H2c05Og0/8LUXG7EnsZyMa8MzkmuHoELfT6txuj0cMqRj6zfPKnmQ1yasR4PCJc8x+M4JSPA==}
    dependencies:
      randombytes: 2.1.0
    dev: true

  /serialize-javascript@6.0.0:
    resolution: {integrity: sha512-Qr3TosvguFt8ePWqsvRfrKyQXIiW+nGbYpy8XK24NQHE83caxWt+mIymTT19DGFbNWNLfEwsrkSmN64lVWB9ag==}
    dependencies:
      randombytes: 2.1.0
    dev: true

  /serialize-javascript@6.0.2:
    resolution: {integrity: sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g==}
    dependencies:
      randombytes: 2.1.0
    dev: true

  /set-function-length@1.2.2:
    resolution: {integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      gopd: 1.0.1
      has-property-descriptors: 1.0.2
    dev: true

  /setprototypeof@1.2.0:
    resolution: {integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==}
    dev: true

  /shallow-clone@3.0.1:
    resolution: {integrity: sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA==}
    engines: {node: '>=8'}
    dependencies:
      kind-of: 6.0.3
    dev: true

  /shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: 3.0.0
    dev: true

  /shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}
    dev: true

  /shelljs@0.8.5:
    resolution: {integrity: sha512-TiwcRcrkhHvbrZbnRcFYMLl30Dfov3HKqzp5tO5b4pt6G/SezKcYhmDg15zXVBswHmctSAQKznqNW2LO5tTDow==}
    engines: {node: '>=4'}
    hasBin: true
    dependencies:
      glob: 7.2.3
      interpret: 1.4.0
      rechoir: 0.6.2
    dev: true

  /side-channel@1.0.6:
    resolution: {integrity: sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      object-inspect: 1.13.2
    dev: true

  /signal-exit@3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}
    dev: true

  /signal-exit@4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==}
    engines: {node: '>=14'}
    dev: true

  /sinon@13.0.2:
    resolution: {integrity: sha512-KvOrztAVqzSJWMDoxM4vM+GPys1df2VBoXm+YciyB/OLMamfS3VXh3oGh5WtrAGSzrgczNWFFY22oKb7Fi5eeA==}
    deprecated: 16.1.1
    dependencies:
      '@sinonjs/commons': 1.8.6
      '@sinonjs/fake-timers': 9.1.2
      '@sinonjs/samsam': 6.1.3
      diff: 5.2.0
      nise: 5.1.9
      supports-color: 7.2.0
    dev: true

  /slack-notify@2.0.7:
    resolution: {integrity: sha512-DZ4J3RVszHUaJf5zXtAocxEhZRAvwWoswB6a/8sAG/QMWkuZdvk3e8d2YQQlPJNYNSbfak+rCtn3zrZ5UmMnYg==}
    engines: {node: '>=13.2.x'}
    dev: true

  /slash@3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==}
    engines: {node: '>=8'}
    dev: true

  /slice-ansi@3.0.0:
    resolution: {integrity: sha512-pSyv7bSTC7ig9Dcgbw9AuRNUb5k5V6oDudjZoMBSr13qpLBG7tB+zgCkARjq7xIUgdz5P1Qe8u+rSGdouOOIyQ==}
    engines: {node: '>=8'}
    dependencies:
      ansi-styles: 4.3.0
      astral-regex: 2.0.0
      is-fullwidth-code-point: 3.0.0
    dev: true

  /slice-ansi@4.0.0:
    resolution: {integrity: sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      astral-regex: 2.0.0
      is-fullwidth-code-point: 3.0.0
    dev: true

  /slice-ansi@5.0.0:
    resolution: {integrity: sha512-FC+lgizVPfie0kkhqUScwRu1O/lF6NOgJmlCgK+/LYxDCTk8sGelYaHDhFcDN+Sn3Cv+3VSa4Byeo+IMCzpMgQ==}
    engines: {node: '>=12'}
    dependencies:
      ansi-styles: 6.2.1
      is-fullwidth-code-point: 4.0.0
    dev: true

  /socket.io-adapter@2.5.5:
    resolution: {integrity: sha512-eLDQas5dzPgOWCk9GuuJC2lBqItuhKI4uxGgo9aIV7MYbk2h9Q6uULEh8WBzThoI7l+qU9Ast9fVUmkqPP9wYg==}
    dependencies:
      debug: 4.3.6
      ws: 8.17.1
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate
    dev: true

  /socket.io-parser@4.2.4:
    resolution: {integrity: sha512-/GbIKmo8ioc+NIWIhwdecY0ge+qVBSMdgxGygevmdHj24bsfgtCmcUUcQ5ZzcylGFHsN3k4HB4Cgkl96KVnuew==}
    engines: {node: '>=10.0.0'}
    dependencies:
      '@socket.io/component-emitter': 3.1.2
      debug: 4.3.6
    transitivePeerDependencies:
      - supports-color
    dev: true

  /socket.io@4.7.5:
    resolution: {integrity: sha512-DmeAkF6cwM9jSfmp6Dr/5/mfMwb5Z5qRrSXLpo3Fq5SqyU8CMF15jIN4ZhfSwu35ksM1qmHZDQ/DK5XTccSTvA==}
    engines: {node: '>=10.2.0'}
    dependencies:
      accepts: 1.3.8
      base64id: 2.0.0
      cors: 2.8.5
      debug: 4.3.6
      engine.io: 6.5.5
      socket.io-adapter: 2.5.5
      socket.io-parser: 4.2.4
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate
    dev: true

  /source-list-map@2.0.1:
    resolution: {integrity: sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw==}
    dev: true

  /source-map-js@1.2.0:
    resolution: {integrity: sha512-itJW8lvSA0TXEphiRoawsCksnlf8SyvmFzIhltqAHluXd88pkCd+cXJVHTDwdCr0IzwptSm035IHQktUu1QUMg==}
    engines: {node: '>=0.10.0'}
    dev: true

  /source-map-support@0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==}
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1
    dev: true

  /source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}
    dev: true

  /source-map@0.7.4:
    resolution: {integrity: sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==}
    engines: {node: '>= 8'}
    dev: true

  /spdx-correct@3.2.0:
    resolution: {integrity: sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA==}
    dependencies:
      spdx-expression-parse: 3.0.1
      spdx-license-ids: 3.0.20
    dev: true

  /spdx-exceptions@2.5.0:
    resolution: {integrity: sha512-PiU42r+xO4UbUS1buo3LPJkjlO7430Xn5SVAhdpzzsPHsjbYVflnnFdATgabnLude+Cqu25p6N+g2lw/PFsa4w==}
    dev: true

  /spdx-expression-parse@3.0.1:
    resolution: {integrity: sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==}
    dependencies:
      spdx-exceptions: 2.5.0
      spdx-license-ids: 3.0.20
    dev: true

  /spdx-license-ids@3.0.20:
    resolution: {integrity: sha512-jg25NiDV/1fLtSgEgyvVyDunvaNHbuwF9lfNV17gSmPFAlYzdfNBlLtLzXTevwkPj7DhGbmN9VnmJIgLnhvaBw==}
    dev: true

  /split2@3.2.2:
    resolution: {integrity: sha512-9NThjpgZnifTkJpzTZ7Eue85S49QwpNhZTq6GRJwObb6jnLFNGB7Qm73V5HewTROPyxD0C29xqmaI68bQtV+hg==}
    dependencies:
      readable-stream: 3.6.2
    dev: true

  /split@1.0.1:
    resolution: {integrity: sha512-mTyOoPbrivtXnwnIxZRFYRrPNtEFKlpB2fvjSnCQUiAA6qAZzqwna5envK4uk6OIeP17CsdF3rSBGYVBsU0Tkg==}
    dependencies:
      through: 2.3.8
    dev: true

  /sprintf-js@1.0.3:
    resolution: {integrity: sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==}
    dev: true

  /sshpk@1.18.0:
    resolution: {integrity: sha512-2p2KJZTSqQ/I3+HX42EpYOa2l3f8Erv8MWKsy2I9uf4wA7yFIkXRffYdsx86y6z4vHtV8u7g+pPlr8/4ouAxsQ==}
    engines: {node: '>=0.10.0'}
    hasBin: true
    dependencies:
      asn1: 0.2.6
      assert-plus: 1.0.0
      bcrypt-pbkdf: 1.0.2
      dashdash: 1.14.1
      ecc-jsbn: 0.1.2
      getpass: 0.1.7
      jsbn: 0.1.1
      safer-buffer: 2.1.2
      tweetnacl: 0.14.5
    dev: true

  /ssri@8.0.1:
    resolution: {integrity: sha512-97qShzy1AiyxvPNIkLWoGua7xoQzzPjQ0HAH4B0rWKo7SZ6USuPcrUiAFrws0UH8RrbWmgq3LMTObhPIHbbBeQ==}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.6
    dev: true

  /statuses@1.5.0:
    resolution: {integrity: sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA==}
    engines: {node: '>= 0.6'}
    dev: true

  /statuses@2.0.1:
    resolution: {integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==}
    engines: {node: '>= 0.8'}
    dev: true

  /streamroller@3.1.5:
    resolution: {integrity: sha512-KFxaM7XT+irxvdqSP1LGLgNWbYN7ay5owZ3r/8t77p+EtSUAfUgtl7be3xtqtOmGUl9K9YPO2ca8133RlTjvKw==}
    engines: {node: '>=8.0'}
    dependencies:
      date-format: 4.0.14
      debug: 4.3.6
      fs-extra: 8.1.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /string-argv@0.3.1:
    resolution: {integrity: sha512-a1uQGz7IyVy9YwhqjZIZu1c8JO8dNIe20xBmSS6qu9kv++k3JGzCVmprbNN5Kn+BgzD5E7YYwg1CcjuJMRNsvg==}
    engines: {node: '>=0.6.19'}
    dev: true

  /string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1
    dev: true

  /string-width@5.1.2:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==}
    engines: {node: '>=12'}
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0
    dev: true

  /string_decoder@1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==}
    dependencies:
      safe-buffer: 5.2.1
    dev: true

  /stringify-object@3.3.0:
    resolution: {integrity: sha512-rHqiFh1elqCQ9WPLIC8I0Q/g/wj5J1eMkyoiD6eoQApWHP0FtlK7rqnhmabL5VUY9JQCcqwwvlOaSuutekgyrw==}
    engines: {node: '>=4'}
    dependencies:
      get-own-enumerable-property-symbols: 3.0.2
      is-obj: 1.0.1
      is-regexp: 1.0.0
    dev: true

  /strip-ansi-cjs@1.0.0:
    resolution: {integrity: sha512-leEPI93yJhNssNWEH3NUOvOQaSv5NQfi+78qdqs3p5NHBJ6+tVNlZn8YB8+dsP5Xk8bUEE/F04etBMu6MAmQjg==}
    dev: true

  /strip-ansi@4.0.0:
    resolution: {integrity: sha512-4XaJ2zQdCzROZDivEVIDPkcQn8LMFSa8kj8Gxb/Lnwzv9A8VctNZ+lfivC/sV3ivW8ElJTERXZoPBRrZKkNKow==}
    engines: {node: '>=4'}
    dependencies:
      ansi-regex: 3.0.1
    dev: true

  /strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}
    dependencies:
      ansi-regex: 5.0.1
    dev: true

  /strip-ansi@7.1.0:
    resolution: {integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==}
    engines: {node: '>=12'}
    dependencies:
      ansi-regex: 6.0.1
    dev: true

  /strip-final-newline@2.0.0:
    resolution: {integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==}
    engines: {node: '>=6'}
    dev: true

  /strip-indent@3.0.0:
    resolution: {integrity: sha512-laJTa3Jb+VQpaC6DseHhF7dXVqHTfJPCRDaEbid/drOhgitgYku/letMUqOXFoWV0zIIUbjpdH2t+tYj4bQMRQ==}
    engines: {node: '>=8'}
    dependencies:
      min-indent: 1.0.1
    dev: true

  /strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}
    dev: true

  /style-loader@2.0.0(webpack@5.94.0):
    resolution: {integrity: sha512-Z0gYUJmzZ6ZdRUqpg1r8GsaFKypE+3xAzuFeMuoHgjc9KZv3wMyCRjQIWEbhoFSq7+7yoHXySDJyyWQaPajeiQ==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      loader-utils: 2.0.4
      schema-utils: 3.3.0
      webpack: 5.94.0(webpack-cli@4.10.0)
    dev: true

  /stylehacks@6.1.1(postcss@8.4.41):
    resolution: {integrity: sha512-gSTTEQ670cJNoaeIp9KX6lZmm8LJ3jPB5yJmX8Zq/wQxOsAFXV3qjWzHas3YYk1qesuVIyYWWUpZ0vSE/dTSGg==}
    engines: {node: ^14 || ^16 || >=18.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      browserslist: 4.23.3
      postcss: 8.4.41
      postcss-selector-parser: 6.1.2
    dev: true

  /sugarss@4.0.1(postcss@8.4.41):
    resolution: {integrity: sha512-WCjS5NfuVJjkQzK10s8WOBY+hhDxxNt/N6ZaGwxFZ+wN3/lKKFSaaKUNecULcTTvE4urLcKaZFQD8vO0mOZujw==}
    engines: {node: '>=12.0'}
    peerDependencies:
      postcss: ^8.3.3
    dependencies:
      postcss: 8.4.41
    dev: true

  /supports-color@5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}
    dependencies:
      has-flag: 3.0.0
    dev: true

  /supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}
    dependencies:
      has-flag: 4.0.0
    dev: true

  /supports-color@8.1.1:
    resolution: {integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==}
    engines: {node: '>=10'}
    dependencies:
      has-flag: 4.0.0
    dev: true

  /supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}
    dev: true

  /svgo@3.3.2:
    resolution: {integrity: sha512-OoohrmuUlBs8B8o6MB2Aevn+pRIH9zDALSR+6hhqVfa6fRwG/Qw9VUMSMW9VNg2CFc/MTIfabtdOVl9ODIJjpw==}
    engines: {node: '>=14.0.0'}
    hasBin: true
    dependencies:
      '@trysound/sax': 0.2.0
      commander: 7.2.0
      css-select: 5.1.0
      css-tree: 2.3.1
      css-what: 6.1.0
      csso: 5.0.5
      picocolors: 1.0.1
    dev: true

  /symbol-tree@3.2.4:
    resolution: {integrity: sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw==}
    dev: true

  /table@6.8.2:
    resolution: {integrity: sha512-w2sfv80nrAh2VCbqR5AK27wswXhqcck2AhfnNW76beQXskGZ1V12GwS//yYVa3d3fcvAip2OUnbDAjW2k3v9fA==}
    engines: {node: '>=10.0.0'}
    dependencies:
      ajv: 8.17.1
      lodash.truncate: 4.4.2
      slice-ansi: 4.0.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: true

  /tapable@2.2.1:
    resolution: {integrity: sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==}
    engines: {node: '>=6'}
    dev: true

  /tar@6.2.1:
    resolution: {integrity: sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==}
    engines: {node: '>=10'}
    dependencies:
      chownr: 2.0.0
      fs-minipass: 2.1.0
      minipass: 5.0.0
      minizlib: 2.1.2
      mkdirp: 1.0.4
      yallist: 4.0.0
    dev: true

  /terser-webpack-plugin@3.1.0(webpack@5.94.0):
    resolution: {integrity: sha512-cjdZte66fYkZ65rQ2oJfrdCAkkhJA7YLYk5eGOcGCSGlq0ieZupRdjedSQXYknMPo2IveQL+tPdrxUkERENCFA==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      cacache: 15.3.0
      find-cache-dir: 3.3.2
      jest-worker: 26.6.2
      p-limit: 3.1.0
      schema-utils: 2.7.1
      serialize-javascript: 4.0.0
      source-map: 0.6.1
      terser: 4.8.1
      webpack: 5.94.0(webpack-cli@4.10.0)
      webpack-sources: 1.4.3
    transitivePeerDependencies:
      - bluebird
    dev: true

  /terser-webpack-plugin@4.2.3(webpack@5.94.0):
    resolution: {integrity: sha512-jTgXh40RnvOrLQNgIkwEKnQ8rmHjHK4u+6UBEi+W+FPmvb+uo+chJXntKe7/3lW5mNysgSWD60KyesnhW8D6MQ==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      cacache: 15.3.0
      find-cache-dir: 3.3.2
      jest-worker: 26.6.2
      p-limit: 3.1.0
      schema-utils: 3.3.0
      serialize-javascript: 5.0.1
      source-map: 0.6.1
      terser: 5.31.6
      webpack: 5.94.0(webpack-cli@4.10.0)
      webpack-sources: 1.4.3
    transitivePeerDependencies:
      - bluebird
    dev: true

  /terser-webpack-plugin@5.3.10(webpack@5.94.0):
    resolution: {integrity: sha512-BKFPWlPDndPs+NGGCr1U59t0XScL5317Y0UReNrHaw9/FwhPENlq6bfgs+4yPfyP51vqC1bQ4rp1EfXW5ZSH9w==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      '@swc/core': '*'
      esbuild: '*'
      uglify-js: '*'
      webpack: ^5.1.0
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      esbuild:
        optional: true
      uglify-js:
        optional: true
    dependencies:
      '@jridgewell/trace-mapping': 0.3.25
      jest-worker: 27.5.1
      schema-utils: 3.3.0
      serialize-javascript: 6.0.2
      terser: 5.31.6
      webpack: 5.94.0(webpack-cli@4.10.0)
    dev: true

  /terser@4.8.1:
    resolution: {integrity: sha512-4GnLC0x667eJG0ewJTa6z/yXrbLGv80D9Ru6HIpCQmO+Q4PfEtBFi0ObSckqwL6VyQv/7ENJieXHo2ANmdQwgw==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      acorn: 8.12.1
      commander: 2.20.3
      source-map: 0.6.1
      source-map-support: 0.5.21
    dev: true

  /terser@5.31.6:
    resolution: {integrity: sha512-PQ4DAriWzKj+qgehQ7LK5bQqCFNMmlhjR2PFFLuqGCpuCAauxemVBWwWOxo3UIwWQx8+Pr61Df++r76wDmkQBg==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      '@jridgewell/source-map': 0.3.6
      acorn: 8.12.1
      commander: 2.20.3
      source-map-support: 0.5.21
    dev: true

  /test-exclude@6.0.0:
    resolution: {integrity: sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w==}
    engines: {node: '>=8'}
    dependencies:
      '@istanbuljs/schema': 0.1.3
      glob: 7.2.3
      minimatch: 3.1.2
    dev: true

  /text-extensions@1.9.0:
    resolution: {integrity: sha512-wiBrwC1EhBelW12Zy26JeOUkQ5mRu+5o8rpsJk5+2t+Y5vE7e842qtZDQ2g1NpX/29HdyFeJ4nSIhI47ENSxlQ==}
    engines: {node: '>=0.10'}
    dev: true

  /text-table@0.2.0:
    resolution: {integrity: sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==}
    dev: true

  /through2@3.0.2:
    resolution: {integrity: sha512-enaDQ4MUyP2W6ZyT6EsMzqBPZaM/avg8iuo+l2d3QCs0J+6RaqkHV/2/lOwDTueBHeJ/2LG9lrLW3d5rWPucuQ==}
    dependencies:
      inherits: 2.0.4
      readable-stream: 3.6.2
    dev: true

  /through@2.3.8:
    resolution: {integrity: sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==}
    dev: true

  /tmp@0.0.33:
    resolution: {integrity: sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==}
    engines: {node: '>=0.6.0'}
    dependencies:
      os-tmpdir: 1.0.2
    dev: true

  /tmp@0.2.3:
    resolution: {integrity: sha512-nZD7m9iCPC5g0pYmcaxogYKggSfLsdxl8of3Q/oIbqCqLLIO9IAF0GWjX1z9NZRHPiXv8Wex4yDCaZsgEw0Y8w==}
    engines: {node: '>=14.14'}
    dev: true

  /to-fast-properties@2.0.0:
    resolution: {integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==}
    engines: {node: '>=4'}
    dev: true

  /to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}
    dependencies:
      is-number: 7.0.0
    dev: true

  /toidentifier@1.0.1:
    resolution: {integrity: sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==}
    engines: {node: '>=0.6'}
    dev: true

  /tough-cookie@2.5.0:
    resolution: {integrity: sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==}
    engines: {node: '>=0.8'}
    dependencies:
      psl: 1.9.0
      punycode: 2.3.1
    dev: true

  /tough-cookie@4.1.4:
    resolution: {integrity: sha512-Loo5UUvLD9ScZ6jh8beX1T6sO1w2/MpCRpEP7V280GKMVUQ0Jzar2U3UJPsrdbziLEMMhu3Ujnq//rhiFuIeag==}
    engines: {node: '>=6'}
    dependencies:
      psl: 1.9.0
      punycode: 2.3.1
      universalify: 0.2.0
      url-parse: 1.5.10
    dev: true

  /tr46@0.0.3:
    resolution: {integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==}
    dev: true

  /tr46@2.1.0:
    resolution: {integrity: sha512-15Ih7phfcdP5YxqiB+iDtLoaTz4Nd35+IiAv0kQ5FNKHzXgdWqPoTIqEDDJmXceQt4JZk6lVPT8lnDlPpGDppw==}
    engines: {node: '>=8'}
    dependencies:
      punycode: 2.3.1
    dev: true

  /trim-newlines@3.0.1:
    resolution: {integrity: sha512-c1PTsA3tYrIsLGkJkzHF+w9F2EyxfXGo4UyJc4pFL++FMjnq0HJS69T3M7d//gKrFKwy429bouPescbjecU+Zw==}
    engines: {node: '>=8'}
    dev: true

  /ts-loader@9.5.1(typescript@4.9.5)(webpack@5.94.0):
    resolution: {integrity: sha512-rNH3sK9kGZcH9dYzC7CewQm4NtxJTjSEVRJ2DyBZR7f8/wcta+iV44UPCXc5+nzDzivKtlzV6c9P4e+oFhDLYg==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      typescript: '*'
      webpack: ^5.0.0
    dependencies:
      chalk: 4.1.2
      enhanced-resolve: 5.17.1
      micromatch: 4.0.8
      semver: 7.6.3
      source-map: 0.7.4
      typescript: 4.9.5
      webpack: 5.94.0(webpack-cli@4.10.0)
    dev: true

  /tslib@1.14.1:
    resolution: {integrity: sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==}
    dev: true

  /tslib@2.7.0:
    resolution: {integrity: sha512-gLXCKdN1/j47AiHiOkJN69hJmcbGTHI0ImLmbYLHykhgeN0jVGola9yVjFgzCUklsZQMW55o+dW7IXv3RCXDzA==}
    dev: true

  /tsutils@3.21.0(typescript@4.9.5):
    resolution: {integrity: sha512-mHKK3iUXL+3UF6xL5k0PEhKRUBKPBCv/+RkEOpjRWxxx27KKRBmmA60A9pgOUvMi8GKhRMPEmjBRPzs2W7O1OA==}
    engines: {node: '>= 6'}
    peerDependencies:
      typescript: '>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta'
    dependencies:
      tslib: 1.14.1
      typescript: 4.9.5
    dev: true

  /tunnel-agent@0.6.0:
    resolution: {integrity: sha512-McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w==}
    dependencies:
      safe-buffer: 5.2.1
    dev: true

  /turndown-plugin-gfm@1.0.2:
    resolution: {integrity: sha512-vwz9tfvF7XN/jE0dGoBei3FXWuvll78ohzCZQuOb+ZjWrs3a0XhQVomJEb2Qh4VHTPNRO4GPZh0V7VRbiWwkRg==}
    dev: true

  /turndown@6.0.0:
    resolution: {integrity: sha512-UVJBhSyRHCpNKtQ00mNWlYUM/i+tcipkb++F0PrOpt0L7EhNd0AX9mWEpL2dRFBu7LWXMp4HgAMA4OeKKnN7og==}
    dependencies:
      jsdom: 16.7.0
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /tweetnacl@0.14.5:
    resolution: {integrity: sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA==}
    dev: true

  /type-check@0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.2.1
    dev: true

  /type-detect@4.0.8:
    resolution: {integrity: sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==}
    engines: {node: '>=4'}
    dev: true

  /type-detect@4.1.0:
    resolution: {integrity: sha512-Acylog8/luQ8L7il+geoSxhEkazvkslg7PSNKOX59mbB9cOveP5aq9h74Y7YU8yDpJwetzQQrfIwtf4Wp4LKcw==}
    engines: {node: '>=4'}
    dev: true

  /type-fest@0.18.1:
    resolution: {integrity: sha512-OIAYXk8+ISY+qTOwkHtKqzAuxchoMiD9Udx+FSGQDuiRR+PJKJHc2NJAXlbhkGwTt/4/nKZxELY1w3ReWOL8mw==}
    engines: {node: '>=10'}
    dev: true

  /type-fest@0.20.2:
    resolution: {integrity: sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==}
    engines: {node: '>=10'}
    dev: true

  /type-fest@0.21.3:
    resolution: {integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==}
    engines: {node: '>=10'}
    dev: true

  /type-fest@0.6.0:
    resolution: {integrity: sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg==}
    engines: {node: '>=8'}
    dev: true

  /type-fest@0.8.1:
    resolution: {integrity: sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA==}
    engines: {node: '>=8'}
    dev: true

  /type-fest@1.4.0:
    resolution: {integrity: sha512-yGSza74xk0UG8k+pLh5oeoYirvIiWo5t0/o3zHHAO2tRDiZcxWP7fywNlXhqb6/r6sWvwi+RsyQMWhVLe4BVuA==}
    engines: {node: '>=10'}
    dev: true

  /type-is@1.6.18:
    resolution: {integrity: sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==}
    engines: {node: '>= 0.6'}
    dependencies:
      media-typer: 0.3.0
      mime-types: 2.1.35
    dev: true

  /typedarray@0.0.6:
    resolution: {integrity: sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA==}
    dev: true

  /typescript@4.9.5:
    resolution: {integrity: sha512-1FXk9E2Hm+QzZQ7z+McJiHL4NW1F2EzMu9Nq9i3zAaGqibafqYwCVU6WyWAuyQRRzOlxou8xZSyXLEN8oKj24g==}
    engines: {node: '>=4.2.0'}
    hasBin: true
    dev: true

  /ua-parser-js@0.7.38:
    resolution: {integrity: sha512-fYmIy7fKTSFAhG3fuPlubeGaMoAd6r0rSnfEsO5nEY55i26KSLt9EH7PLQiiqPUhNqYIJvSkTy1oArIcXAbPbA==}
    dev: true

  /uglify-js@3.19.2:
    resolution: {integrity: sha512-S8KA6DDI47nQXJSi2ctQ629YzwOVs+bQML6DAtvy0wgNdpi+0ySpQK0g2pxBq2xfF2z3YCscu7NNA8nXT9PlIQ==}
    engines: {node: '>=0.8.0'}
    hasBin: true
    requiresBuild: true
    dev: true
    optional: true

  /undici-types@6.19.8:
    resolution: {integrity: sha512-ve2KP6f/JnbPBFyobGHuerC9g1FYGn/F8n1LWTwNxCEzd6IfqTwUQcNXgEtmmQ6DlRrC1hrSrBnCZPokRrDHjw==}
    dev: true

  /unique-filename@1.1.1:
    resolution: {integrity: sha512-Vmp0jIp2ln35UTXuryvjzkjGdRyf9b2lTXuSYUiPmzRcl3FDtYqAwOnTJkAngD9SWhnoJzDbTKwaOrZ+STtxNQ==}
    dependencies:
      unique-slug: 2.0.2
    dev: true

  /unique-slug@2.0.2:
    resolution: {integrity: sha512-zoWr9ObaxALD3DOPfjPSqxt4fnZiWblxHIgeWqW8x7UqDzEtHEQLzji2cuJYQFCU6KmoJikOYAZlrTHHebjx2w==}
    dependencies:
      imurmurhash: 0.1.4
    dev: true

  /universal-user-agent@6.0.1:
    resolution: {integrity: sha512-yCzhz6FN2wU1NiiQRogkTQszlQSlpWaw8SvVegAc+bDxbzHgh1vX8uIe8OYyMH6DwH+sdTJsgMl36+mSMdRJIQ==}
    dev: true

  /universalify@0.1.2:
    resolution: {integrity: sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==}
    engines: {node: '>= 4.0.0'}
    dev: true

  /universalify@0.2.0:
    resolution: {integrity: sha512-CJ1QgKmNg3CwvAv/kOFmtnEN05f0D/cn9QntgNOQlQF9dgvVTHj3t+8JPdjqawCHk7V/KA+fbUqzZ9XWhcqPUg==}
    engines: {node: '>= 4.0.0'}
    dev: true

  /universalify@2.0.1:
    resolution: {integrity: sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==}
    engines: {node: '>= 10.0.0'}
    dev: true

  /unpipe@1.0.0:
    resolution: {integrity: sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==}
    engines: {node: '>= 0.8'}
    dev: true

  /upath@2.0.1:
    resolution: {integrity: sha512-1uEe95xksV1O0CYKXo8vQvN1JEbtJp7lb7C5U9HMsIp6IVwntkH/oNUzyVNQSd4S1sYk2FpSSW44FqMc8qee5w==}
    engines: {node: '>=4'}
    dev: true

  /update-browserslist-db@1.1.0(browserslist@4.23.3):
    resolution: {integrity: sha512-EdRAaAyk2cUE1wOf2DkEhzxqOQvFOoRJFNS6NeyJ01Gp2beMRpBAINjM2iDXE3KCuKhwnvHIQCJm6ThL2Z+HzQ==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'
    dependencies:
      browserslist: 4.23.3
      escalade: 3.1.2
      picocolors: 1.0.1
    dev: true

  /uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}
    dependencies:
      punycode: 2.3.1
    dev: true

  /url-parse@1.5.10:
    resolution: {integrity: sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==}
    dependencies:
      querystringify: 2.2.0
      requires-port: 1.0.0
    dev: true

  /util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}
    dev: true

  /utils-merge@1.0.1:
    resolution: {integrity: sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==}
    engines: {node: '>= 0.4.0'}
    dev: true

  /uuid@3.4.0:
    resolution: {integrity: sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==}
    deprecated: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
    hasBin: true
    dev: true

  /v8-compile-cache@2.4.0:
    resolution: {integrity: sha512-ocyWc3bAHBB/guyqJQVI5o4BZkPhznPYUG2ea80Gond/BgNWpap8TOmLSeeQG7bnh2KMISxskdADG59j7zruhw==}
    dev: true

  /validate-npm-package-license@3.0.4:
    resolution: {integrity: sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==}
    dependencies:
      spdx-correct: 3.2.0
      spdx-expression-parse: 3.0.1
    dev: true

  /validate-npm-package-name@5.0.1:
    resolution: {integrity: sha512-OljLrQ9SQdOUqTaQxqL5dEfZWrXExyyWsozYlAWFawPVNuD83igl7uJD2RTkNMbniIYgt8l81eCJGIdQF7avLQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}
    dev: true

  /vanilla-colorful@0.7.2:
    resolution: {integrity: sha512-z2YZusTFC6KnLERx1cgoIRX2CjPRP0W75N+3CC6gbvdX5Ch47rZkEMGO2Xnf+IEmi3RiFLxS18gayMA27iU7Kg==}
    dev: true

  /vary@1.1.2:
    resolution: {integrity: sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==}
    engines: {node: '>= 0.8'}
    dev: true

  /verror@1.10.0:
    resolution: {integrity: sha512-ZZKSmDAEFOijERBLkmYfJ+vmk3w+7hOLYDNkRCuRuMJGEmqYNCNLyBBFwWKVMhfwaEF3WOd0Zlw86U/WC/+nYw==}
    engines: {'0': node >=0.6.0}
    dependencies:
      assert-plus: 1.0.0
      core-util-is: 1.0.2
      extsprintf: 1.3.0
    dev: true

  /void-elements@2.0.1:
    resolution: {integrity: sha512-qZKX4RnBzH2ugr8Lxa7x+0V6XD9Sb/ouARtiasEQCHB1EVU4NXtmHsDDrx1dO4ne5fc3J6EW05BP1Dl0z0iung==}
    engines: {node: '>=0.10.0'}
    dev: true

  /vue-component-type-helpers@2.0.29:
    resolution: {integrity: sha512-58i+ZhUAUpwQ+9h5Hck0D+jr1qbYl4voRt5KffBx8qzELViQ4XdT/Tuo+mzq8u63teAG8K0lLaOiL5ofqW38rg==}
    dev: true

  /vue-eslint-parser@9.4.3(eslint@7.32.0):
    resolution: {integrity: sha512-2rYRLWlIpaiN8xbPiDyXZXRgLGOtWxERV7ND5fFAv5qo1D2N9Fu9MNajBNc6o13lZ+24DAWCkQCvj4klgmcITg==}
    engines: {node: ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: '>=6.0.0'
    dependencies:
      debug: 4.3.6
      eslint: 7.32.0
      eslint-scope: 7.2.2
      eslint-visitor-keys: 3.4.3
      espree: 9.6.1
      esquery: 1.6.0
      lodash: 4.17.21
      semver: 7.6.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /vue@3.4.38(typescript@4.9.5):
    resolution: {integrity: sha512-f0ZgN+mZ5KFgVv9wz0f4OgVKukoXtS3nwET4c2vLBGQR50aI8G0cqbFtLlX9Yiyg3LFGBitruPHt2PxwTduJEw==}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@vue/compiler-dom': 3.4.38
      '@vue/compiler-sfc': 3.4.38
      '@vue/runtime-dom': 3.4.38
      '@vue/server-renderer': 3.4.38(vue@3.4.38)
      '@vue/shared': 3.4.38
      typescript: 4.9.5
    dev: true

  /w3c-hr-time@1.0.2:
    resolution: {integrity: sha512-z8P5DvDNjKDoFIHK7q8r8lackT6l+jo/Ye3HOle7l9nICP9lf1Ci25fy9vHd0JOWewkIFzXIEig3TdKT7JQ5fQ==}
    deprecated: Use your platform's native performance.now() and performance.timeOrigin.
    dependencies:
      browser-process-hrtime: 1.0.0
    dev: true

  /w3c-xmlserializer@2.0.0:
    resolution: {integrity: sha512-4tzD0mF8iSiMiNs30BiLO3EpfGLZUT2MSX/G+o7ZywDzliWQ3OPtTZ0PTC3B3ca1UAf4cJMHB+2Bf56EriJuRA==}
    engines: {node: '>=10'}
    dependencies:
      xml-name-validator: 3.0.0
    dev: true

  /watchpack@2.4.2:
    resolution: {integrity: sha512-TnbFSbcOCcDgjZ4piURLCbJ3nJhznVh9kw6F6iokjiFPl8ONxe9A6nMDVXDiNbrSfLILs6vB07F7wLBrwPYzJw==}
    engines: {node: '>=10.13.0'}
    dependencies:
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11
    dev: true

  /webidl-conversions@3.0.1:
    resolution: {integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==}
    dev: true

  /webidl-conversions@5.0.0:
    resolution: {integrity: sha512-VlZwKPCkYKxQgeSbH5EyngOmRp7Ww7I9rQLERETtf5ofd9pGeswWiOtogpEO850jziPRarreGxn5QIiTqpb2wA==}
    engines: {node: '>=8'}
    dev: true

  /webidl-conversions@6.1.0:
    resolution: {integrity: sha512-qBIvFLGiBpLjfwmYAaHPXsn+ho5xZnGvyGvsarywGNc8VyQJUMHJ8OBKGGrPER0okBeMDaan4mNBlgBROxuI8w==}
    engines: {node: '>=10.4'}
    dev: true

  /webpack-cli@4.10.0(webpack@5.94.0):
    resolution: {integrity: sha512-NLhDfH/h4O6UOy+0LSso42xvYypClINuMNBVVzX4vX98TmTaTUxwRbXdhucbFMd2qLaCTcLq/PdYrvi8onw90w==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    peerDependencies:
      '@webpack-cli/generators': '*'
      '@webpack-cli/migrate': '*'
      webpack: 4.x.x || 5.x.x
      webpack-bundle-analyzer: '*'
      webpack-dev-server: '*'
    peerDependenciesMeta:
      '@webpack-cli/generators':
        optional: true
      '@webpack-cli/migrate':
        optional: true
      webpack-bundle-analyzer:
        optional: true
      webpack-dev-server:
        optional: true
    dependencies:
      '@discoveryjs/json-ext': 0.5.7
      '@webpack-cli/configtest': 1.2.0(webpack-cli@4.10.0)(webpack@5.94.0)
      '@webpack-cli/info': 1.5.0(webpack-cli@4.10.0)
      '@webpack-cli/serve': 1.7.0(webpack-cli@4.10.0)
      colorette: 2.0.20
      commander: 7.2.0
      cross-spawn: 7.0.3
      fastest-levenshtein: 1.0.16
      import-local: 3.2.0
      interpret: 2.2.0
      rechoir: 0.7.1
      webpack: 5.94.0(webpack-cli@4.10.0)
      webpack-merge: 5.10.0
    dev: true

  /webpack-merge@4.2.2:
    resolution: {integrity: sha512-TUE1UGoTX2Cd42j3krGYqObZbOD+xF7u28WB7tfUordytSjbWTIjK/8V0amkBfTYN4/pB/GIDlJZZ657BGG19g==}
    dependencies:
      lodash: 4.17.21
    dev: true

  /webpack-merge@5.10.0:
    resolution: {integrity: sha512-+4zXKdx7UnO+1jaN4l2lHVD+mFvnlZQP/6ljaJVb4SZiwIKeUnrT5l0gkT8z+n4hKpC+jpOv6O9R+gLtag7pSA==}
    engines: {node: '>=10.0.0'}
    dependencies:
      clone-deep: 4.0.1
      flat: 5.0.2
      wildcard: 2.0.1
    dev: true

  /webpack-sources@1.4.3:
    resolution: {integrity: sha512-lgTS3Xhv1lCOKo7SA5TjKXMjpSM4sBjNV5+q2bqesbSPs5FjGmU6jjtBSkX9b4qW87vDIsCIlUPOEhbZrMdjeQ==}
    dependencies:
      source-list-map: 2.0.1
      source-map: 0.6.1
    dev: true

  /webpack-sources@2.3.1:
    resolution: {integrity: sha512-y9EI9AO42JjEcrTJFOYmVywVZdKVUfOvDUPsJea5GIr1JOEGFVqwlY2K098fFoIjOkDzHn2AjRvM8dsBZu+gCA==}
    engines: {node: '>=10.13.0'}
    dependencies:
      source-list-map: 2.0.1
      source-map: 0.6.1
    dev: true

  /webpack-sources@3.2.3:
    resolution: {integrity: sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==}
    engines: {node: '>=10.13.0'}
    dev: true

  /webpack@5.94.0(webpack-cli@4.10.0):
    resolution: {integrity: sha512-KcsGn50VT+06JH/iunZJedYGUJS5FGjow8wb9c0v5n1Om8O1g4L6LjtfxwlXIATopoQu+vOXXa7gYisWxCoPyg==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true
    dependencies:
      '@types/estree': 1.0.5
      '@webassemblyjs/ast': 1.12.1
      '@webassemblyjs/wasm-edit': 1.12.1
      '@webassemblyjs/wasm-parser': 1.12.1
      acorn: 8.12.1
      acorn-import-attributes: 1.9.5(acorn@8.12.1)
      browserslist: 4.23.3
      chrome-trace-event: 1.0.4
      enhanced-resolve: 5.17.1
      es-module-lexer: 1.5.4
      eslint-scope: 5.1.1
      events: 3.3.0
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11
      json-parse-even-better-errors: 2.3.1
      loader-runner: 4.3.0
      mime-types: 2.1.35
      neo-async: 2.6.2
      schema-utils: 3.3.0
      tapable: 2.2.1
      terser-webpack-plugin: 5.3.10(webpack@5.94.0)
      watchpack: 2.4.2
      webpack-cli: 4.10.0(webpack@5.94.0)
      webpack-sources: 3.2.3
    transitivePeerDependencies:
      - '@swc/core'
      - esbuild
      - uglify-js
    dev: true

  /whatwg-encoding@1.0.5:
    resolution: {integrity: sha512-b5lim54JOPN9HtzvK9HFXvBma/rnfFeqsic0hSpjtDbVxR3dJKLc+KB4V6GgiGOvl7CY/KNh8rxSo9DKQrnUEw==}
    dependencies:
      iconv-lite: 0.4.24
    dev: true

  /whatwg-mimetype@2.3.0:
    resolution: {integrity: sha512-M4yMwr6mAnQz76TbJm914+gPpB/nCwvZbJU28cUD6dR004SAxDLOOSUaB1JDRqLtaOV/vi0IC5lEAGFgrjGv/g==}
    dev: true

  /whatwg-url@5.0.0:
    resolution: {integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==}
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1
    dev: true

  /whatwg-url@8.7.0:
    resolution: {integrity: sha512-gAojqb/m9Q8a5IV96E3fHJM70AzCkgt4uXYX2O7EmuyOnLrViCQlsEBmF9UQIu3/aeAIp2U17rtbpZWNntQqdg==}
    engines: {node: '>=10'}
    dependencies:
      lodash: 4.17.21
      tr46: 2.1.0
      webidl-conversions: 6.1.0
    dev: true

  /which@1.3.1:
    resolution: {integrity: sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==}
    hasBin: true
    dependencies:
      isexe: 2.0.0
    dev: true

  /which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: 2.0.0
    dev: true

  /which@3.0.1:
    resolution: {integrity: sha512-XA1b62dzQzLfaEOSQFTCOd5KFf/1VSzZo7/7TUjnya6u0vGGKzU96UQBZTAThCb2j4/xjBAyii1OhRLJEivHvg==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}
    hasBin: true
    dependencies:
      isexe: 2.0.0
    dev: true

  /wildcard@2.0.1:
    resolution: {integrity: sha512-CC1bOL87PIWSBhDcTrdeLo6eGT7mCFtrg0uIJtqJUFyK+eJnzl8A1niH56uu7KMa5XFrtiV+AQuHO3n7DsHnLQ==}
    dev: true

  /word-wrap@1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /wordwrap@1.0.0:
    resolution: {integrity: sha512-gvVzJFlPycKc5dZN4yPkP8w7Dc37BtP1yczEneOb4uq34pXZcvrtRTmWV8W+Ume+XCxKgbjM+nevkyFPMybd4Q==}
    dev: true

  /workerpool@6.2.0:
    resolution: {integrity: sha512-Rsk5qQHJ9eowMH28Jwhe8HEbmdYDX4lwoMWshiCXugjtHqMD9ZbiqSDLxcsfdqsETPzVUtX5s1Z5kStiIM6l4A==}
    dev: true

  /wrap-ansi@6.2.0:
    resolution: {integrity: sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==}
    engines: {node: '>=8'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: true

  /wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: true

  /wrap-ansi@8.1.0:
    resolution: {integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==}
    engines: {node: '>=12'}
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0
    dev: true

  /wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}
    dev: true

  /ws@7.5.10:
    resolution: {integrity: sha512-+dbF1tHwZpXcbOJdVOkzLDxZP1ailvSxM6ZweXTegylPny803bFhA+vqBYw4s31NSAk4S2Qz+AKXK9a4wkdjcQ==}
    engines: {node: '>=8.3.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true
    dev: true

  /ws@8.17.1:
    resolution: {integrity: sha512-6XQFvXTkbfUOZOKKILFG1PDK2NDQs4azKQl26T0YS5CxqWLgXajbPZ+h4gZekJyRqFU8pvnbAbbs/3TgRPy+GQ==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true
    dev: true

  /xml-name-validator@3.0.0:
    resolution: {integrity: sha512-A5CUptxDsvxKJEU3yO6DuWBSJz/qizqzJKOMIfUJHETbBw/sFaDxgd6fxm1ewUaM0jZ444Fc5vC5ROYurg/4Pw==}
    dev: true

  /xml-name-validator@4.0.0:
    resolution: {integrity: sha512-ICP2e+jsHvAj2E2lIHxa5tjXRlKDJo4IdvPvCXbXQGdzSfmSpNVyIKMvoZHjDY9DP0zV17iI85o90vRFXNccRw==}
    engines: {node: '>=12'}
    dev: true

  /xmlchars@2.2.0:
    resolution: {integrity: sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw==}
    dev: true

  /y18n@5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==}
    engines: {node: '>=10'}
    dev: true

  /yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}
    dev: true

  /yallist@4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}
    dev: true

  /yaml@1.10.2:
    resolution: {integrity: sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==}
    engines: {node: '>= 6'}
    dev: true

  /yargs-parser@20.2.4:
    resolution: {integrity: sha512-WOkpgNhPTlE73h4VFAFsOnomJVaovO8VqLDzy5saChRBFQFBoMYirowyW+Q9HB4HFF4Z7VZTiG3iSzJJA29yRA==}
    engines: {node: '>=10'}
    dev: true

  /yargs-parser@20.2.9:
    resolution: {integrity: sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==}
    engines: {node: '>=10'}
    dev: true

  /yargs-unparser@2.0.0:
    resolution: {integrity: sha512-7pRTIA9Qc1caZ0bZ6RYRGbHJthJWuakf+WmHK0rVeLkNrrGhfoabBNdue6kdINI6r4if7ocq9aD/n7xwKOdzOA==}
    engines: {node: '>=10'}
    dependencies:
      camelcase: 6.3.0
      decamelize: 4.0.0
      flat: 5.0.2
      is-plain-obj: 2.1.0
    dev: true

  /yargs@16.2.0:
    resolution: {integrity: sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw==}
    engines: {node: '>=10'}
    dependencies:
      cliui: 7.0.4
      escalade: 3.1.2
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 20.2.9
    dev: true

  /yocto-queue@0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    engines: {node: '>=10'}
    dev: true
