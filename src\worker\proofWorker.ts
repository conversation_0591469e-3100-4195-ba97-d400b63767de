/**
 * 导入所需的库和模块
 * htmlparser2: 用于HTML解析
 * domhandler: 提供DOM操作的类型和接口
 * dom-serializer: 用于DOM序列化为HTML字符串
 * url-parse: URL解析工具
 * lodash-es: 提供trim等工具函数
 * entities: 处理HTML实体编码/解码
 * highlight.js: 代码语法高亮
 * katex: LaTeX数学公式渲染
 */
import { ElementType, parseDocument } from 'htmlparser2';
import { Document, Element, type ChildNode, type Text } from 'domhandler';
import { render } from 'dom-serializer';
import { type RenderInfo } from '@/types/word';
import URLParse from 'url-parse';
import { trim } from 'lodash-es';
import { decodeHTML } from 'entities';
import hljs from 'highlight.js';
import katex from 'katex';
import { getVisibleLatex } from '@/utils/getVisibleLatex';

/**
 * Web Worker的消息处理函数
 * 接收HTML字符串数组，处理后返回渲染所需的信息
 * 
 * @param e 消息事件，包含以下数据：
 *   - htmlStrings: 需要处理的HTML字符串数组
 *   - regString: 可选，已生成的正则字符串
 *   - renderInfoIndexes: 可选，渲染信息索引数组
 *   - renderInfoListList: 可选，渲染信息列表的列表
 */
onmessage = function (
  e: MessageEvent<{
    htmlStrings: string[];
    regString?: string;
    renderInfoIndexes?: Array<{
      listIndex: number;
      index: number;
    }>;
    renderInfoListList?: RenderInfo[][];
  }>
) {
  // 记录处理开始时间，用于性能分析
  const start = Date.now();
  
  // 解构获取消息数据
  let { htmlStrings, regString, renderInfoIndexes, renderInfoListList } = e.data;
  
  // 如果没有提供regString，则需要从头构建所有数据
  if (!regString) {
    const res = build(htmlStrings);
    regString = res.regString;
    renderInfoIndexes = res.renderInfoIndexes;
    renderInfoListList = res.renderInfoListList;
  }
  
  // 将处理结果发送回主线程
  postMessage({
    regString,
    renderInfoIndexes,
    renderInfoListList
  });
};

/**
 * 构建函数 - 将HTML字符串数组转换为渲染所需的数据结构
 * 
 * @param htmlStrings HTML字符串数组
 * @returns 包含以下数据的对象：
 *   - renderInfoIndexes: 渲染信息索引数组，记录每个字符在哪个列表的哪个位置
 *   - regString: 生成的正则字符串，用于后续匹配
 *   - renderInfoListList: 渲染信息列表的列表，包含所有需要渲染的内容信息
 */
function build(htmlStrings: string[]) {
  // 初始化渲染信息索引数组，用于记录每个字符在哪个列表的哪个位置
  const renderInfoIndexes: Array<{
    listIndex: number; // 列表索引
    index: number;    // 在列表中的位置索引
  }> = [];

  // 初始化正则字符串，用于后续匹配
  let regString = '';
  // 初始化渲染信息列表的列表，每个HTML字符串对应一个渲染信息列表
  const renderInfoListList: RenderInfo[][] = [];
  
  // 遍历每个HTML字符串进行处理
  htmlStrings.forEach((htmlString, listIndex) => {
    // 使用htmlparser2解析HTML字符串为DOM树
    // decodeEntities设为false，保留原始的HTML实体编码
    const dom = parseDocument(htmlString, {
      decodeEntities: false
    });

    // 初始化当前HTML字符串的渲染信息列表
    const renderInfoList: RenderInfo[] = [];
    let index = 0; // 渲染字符串位置索引，记录当前处理到的位置
    /**
     * 深度优先搜索遍历DOM树节点
     * 根据节点类型进行不同处理：
     * - 文本节点：处理文本内容，包括HTML实体
     * - 脚本节点：处理LaTeX公式
     * - 标签节点：处理img、code等特殊标签和普通HTML标签
     * - Document节点：处理其子节点
     * 
     * @param node 当前处理的DOM节点
     */
    const dfs = (node: ChildNode) => {
      // 处理文本节点
      if (node.type == ElementType.Text) {
        const textContent = node.data;
        // 遍历文本内容的每个字符
        for (let i = 0; i < textContent.length; ++i) {
          // 处理HTML实体（如&amp;、&lt;等）
          if (textContent[i] == '&') {
            // 查找实体结束位置
            const end = textContent.indexOf(';', i);
            if (end == -1) {
              throw new Error('转义字符异常'); // 实体格式不正确时抛出异常
            }
            // 提取完整的实体字符串
            const entity = textContent.slice(i, end + 1);

            // 添加到渲染信息列表
            renderInfoList.push({
              content: entity,       // 原始实体内容
              isTag: false,          // 不是HTML标签
              relatedQuestionCount: 1, // 相关问题计数
              relatedSearchCount: 1,   // 相关搜索计数
              questionCountMap: {},    // 问题计数映射
              searchCount: 0           // 搜索计数
            });
            // 将解码后的实体添加到正则字符串
            regString += decodeHTML(entity);
            // 记录该字符在哪个列表的哪个位置
            renderInfoIndexes.push({
              listIndex,
              index
            });
            index++;

            // 跳过已处理的实体
            i = end;
          } else {
            // 处理普通字符（忽略回车符）
            if (textContent[i] != '\r') {
              renderInfoList.push({
                content: textContent[i], // 单个字符内容
                isTag: false,            // 不是HTML标签
                relatedQuestionCount: 1,  // 相关问题计数
                relatedSearchCount: 1,    // 相关搜索计数
                questionCountMap: {},     // 问题计数映射
                searchCount: 0            // 搜索计数
              });
              regString += textContent[i];
              renderInfoIndexes.push({
                listIndex,
                index
              });
              index++;
            }
          }
        }
      // 处理脚本节点（用于LaTeX数学公式）
      } else if (node.type == ElementType.Script) {
        // 判断是否为显示模式公式（独立成行的公式）
        const display = node.attribs.type.match(/mode\s*=\s*display(;|\s|\n|$)/) != null;
        // 获取LaTeX代码
        const code = (node.firstChild as Text).data;

        // 使用KaTeX渲染LaTeX公式为HTML
        const outerHTML = katex.renderToString(code, {
          throwOnError: false, // 发生错误时不抛出异常
          output: 'html'       // 输出格式为HTML
        });
        
        // 根据是否为显示模式，创建不同的HTML容器
        const str = display
          ? `<div class="equation" latexCode="${code}">${outerHTML}</div>` // 显示模式用div
          : `<span class="inline-equation" latexCode="${code}">${outerHTML}</span>`; // 行内模式用span
        
        // 获取LaTeX公式的可见文本表示
        const key = getVisibleLatex(outerHTML);
        
        // 添加到渲染信息列表
        renderInfoList.push({
          content: str,                  // 包含渲染后HTML的字符串
          isTag: false,                  // 不作为HTML标签处理
          relatedQuestionCount: code.length, // 相关问题计数，使用代码长度
          relatedSearchCount: 1,           // 相关搜索计数
          questionCountMap: {},            // 问题计数映射
          searchCount: 0                   // 搜索计数
        });
        
        // 将可见文本表示添加到正则字符串
        regString += key;
        
        // 为可见文本的每个字符添加索引引用
        for (let i = 0; i < key.length; ++i) {
          renderInfoIndexes.push({
            listIndex,
            index
          });
        }
        index++;
        // node.attribs.stringIndex = String(index - 1);
        // const display = node.attribs.type.match(/mode\s*=\s*display(;|\s|\n|$)/) != null;
        // const outerHTML = renderToString(key, {
        //   displayMode: display,
        //   throwOnError: true,
        //   output: 'html'
        // })
        // console.log(outerHTML)
        // const outerHTML = render(node);
        // if (!uncommonWordMap.has(key)) {
        //   regString += uncommonWords[uncommonWordIndex];
        //   renderInfoList.push({
        //     content: outerHTML,
        //     qids: [],
        //     search: false,
        //     isTag: false
        //   });
        //   uncommonWordMap.set(key, uncommonWords[uncommonWordIndex++]);
        //   renderInfoIndexes.push(index++);
        // } else {
        //   const str = uncommonWordMap.get(key)!;
        //   regString += str;
        //   renderInfoList.push({
        //     content: outerHTML,
        //     qids: [],
        //     search: false,
        //     isTag: false
        //   });
        //   renderInfoIndexes.push(index++);
        // }
      // 处理HTML标签节点
      } else if (node.type == ElementType.Tag) {
        // 处理图片标签
        if (node.tagName == 'img') {
          // 解码图片src属性中的HTML实体
          node.attribs.src = decodeHTML(node.attribs.src);
          const src = node.attribs.src;
          // 将节点渲染为HTML字符串
          const outerHTML = render(node);
          // 从URL路径中提取文件名作为图片的标识符
          const key = decodeURI(URLParse(src).pathname.split('/').pop() as string);

          // 添加到渲染信息列表
          renderInfoList.push({
            content: outerHTML,           // 图片的HTML代码
            isTag: false,                 // 不作为HTML标签处理
            relatedQuestionCount: key.length, // 相关问题计数，使用文件名长度
            relatedSearchCount: Infinity,    // 相关搜索计数设为无限大
            questionCountMap: {},            // 问题计数映射
            searchCount: 0                   // 搜索计数
          });
          
          // 将图片标识符添加到正则字符串
          regString += key;
          
          // 为标识符的每个字符添加索引引用
          for (let i = 0; i < key.length; ++i) {
            renderInfoIndexes.push({
              listIndex,
              index
            });
          }
          index++;
          // if (!uncommonWordMap.has(key)) {
          //   regString += uncommonWords[uncommonWordIndex];
          //   renderInfoList.push({
          //     content: outerHTML,
          //     qids: [],
          //     search: false,
          //     isTag: false
          //   });
          //   uncommonWordMap.set(key, uncommonWords[uncommonWordIndex++]);
          //   renderInfoIndexes.push(index++);
          // } else {
          //   const str = uncommonWordMap.get(key)!;
          //   regString += str;
          //   renderInfoList.push({
          //     content: outerHTML,
          //     qids: [],
          //     search: false,
          //     isTag: false
          //   });
          //   renderInfoIndexes.push(index++);
          // }
        }
        // 处理代码标签
        else if (node.tagName == 'code') {
          // 获取代码内容
          const code = (node.children[0] as Text).data;
          // 添加highlight.js的类名
          node.attribs.class = `${node.attribs.class} hljs`;

          // 使用highlight.js自动检测语言并高亮代码
          const outerHTML = decodeHTML(hljs.highlightAuto(code).value);
          
          // 构建开始标签
          let str = `<code`;
          for (const key in node.attribs) {
            str += ` ${key}="${node.attribs[key]}"`;
          }
          str += `>`;
          
          // 添加开始标签到渲染信息列表
          renderInfoList.push({
            content: str,                  // 开始标签内容
            isTag: true,                   // 作为HTML标签处理
            relatedQuestionCount: Infinity, // 相关问题计数设为无限大
            relatedSearchCount: Infinity,   // 相关搜索计数设为无限大
            questionCountMap: {},           // 问题计数映射
            searchCount: 0                  // 搜索计数
          });
          index++;
          
          // 解析高亮后的HTML内容并递归处理
          const dom = parseDocument(outerHTML, {
            decodeEntities: false
          });
          dfs(dom); // 递归处理高亮后的内容
          
          // 添加结束标签到渲染信息列表
          renderInfoList.push({
            content: `</code>`,              // 结束标签内容
            isTag: true,                    // 作为HTML标签处理
            relatedQuestionCount: Infinity,  // 相关问题计数设为无限大
            relatedSearchCount: Infinity,    // 相关搜索计数设为无限大
            questionCountMap: {},            // 问题计数映射
            searchCount: 0                   // 搜索计数
          });
          index++;
        }
        // 处理其他HTML标签
        else {
          // 构建开始标签
          let content = `<${node.tagName}`;
          for (const key in node.attribs) {
            content += ` ${key}="${node.attribs[key]}"`;
          }
          content += '>';
          
          // 添加开始标签到渲染信息列表
          renderInfoList.push({
            content,                        // 开始标签内容
            isTag: true,                    // 作为HTML标签处理
            relatedQuestionCount: Infinity,  // 相关问题计数设为无限大
            relatedSearchCount: Infinity,    // 相关搜索计数设为无限大
            questionCountMap: {},            // 问题计数映射
            searchCount: 0                   // 搜索计数
          });
          index++;
          
          // 递归处理所有子节点
          for (const child of node.children) {
            dfs(child);
          }
          
          // 对于非自闭合标签（不是br或hr），添加结束标签
          if (!['br', 'hr'].includes(node.tagName)) {
            renderInfoList.push({
              content: `</${node.tagName}>`,   // 结束标签内容
              isTag: true,                     // 作为HTML标签处理
              relatedQuestionCount: Infinity,   // 相关问题计数设为无限大
              relatedSearchCount: Infinity,     // 相关搜索计数设为无限大
              questionCountMap: {},             // 问题计数映射
              searchCount: 0                    // 搜索计数
            });
            index++;
          }
        }
      // 处理Document节点
      } else {
        // 递归处理所有子节点
        for (const child of (node as Document).children) {
          dfs(child);
        }
      }
    };
    
    // 开始从DOM根节点进行深度优先遍历
    dfs(dom);
    // 将当前HTML字符串的渲染信息列表添加到总列表中
    renderInfoListList.push(renderInfoList);
  });
  
  // 返回处理结果
  return { 
    renderInfoIndexes,  // 渲染信息索引数组
    regString,          // 生成的正则字符串
    renderInfoListList  // 渲染信息列表的列表
  };
}
