<script setup lang="ts">
import FormSwitch from '@/components/FormSwitch.vue';
import MyFlipper from '@/components/MyFlipper.vue';
import CmpButton from '@/components/CmpButton.vue';
import CheckDrawer from '@/view/task/components/CheckDrawer.vue';
import AssignDialog from '@/view/task/components/AssignDialog.vue';
import { taskCompleteType, taskCompleteTypeDict, MAX_PAGESIZE } from '@/utils/constant';
import { getTaskListApi } from '@/apis/path/task';
import type { params2GetTaskList } from '@/apis/path/task';
import { onMounted, ref } from 'vue';
import { ElMessage } from 'element-plus';

const drawerRef = ref(); // drawer
const dialogRef = ref(); // dialog
const currentPage = ref(1); // 当前页
const pageSize = MAX_PAGESIZE; // 页大小
const tableData = ref([]); // 表格数据
const total = ref(0); // 总数
const selectionTaskList = ref([]); // 选择任务列表

// 分配任务枚举
enum assignTask {
  mul = 0,
  one = 1
}
// 获取任务列表
const getTaskList = (current: number, status?: number, domain?: string, task?: string) => {
  let params: params2GetTaskList = {
    current: current ? current : currentPage.value,
    limit: pageSize
  };
  if (status !== -1 && status) params.status = status;
  if (domain) params.areaTitle = domain;
  if (task) params.taskTitle = task;

  getTaskListApi(params).then((res) => {
    if (res.success) {
      tableData.value = res.data.list;
      total.value = res.data.total;
    }
  });
};
// 处理任务列表更新
const handleRefreshList = (form?: any) => {
  if (form) {
    getTaskList(currentPage.value, form.taskStatus, form.domainTitle, form.taskTitle);
  } else {
    getTaskList(currentPage.value);
  }
};
// 计算列表index
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize + index + 1;
};
// 处理选择task
const handleSelectionChange = (list: []) => {
  selectionTaskList.value = list;
};
// 处理换页
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  getTaskList(newPage);
};
// 处理查看
const handleCheck = (task: any) => {
  drawerRef.value.showDrawer(task, -1);
};
// 处理分配任务
const handleAssign = (tasks: any, type: number) => {
  if (tasks.length === 0) {
    ElMessage.warning('请选择任务');
  } else {
    dialogRef.value.showDialog(tasks, type);
  }
};
onMounted(() => {
  getTaskList(currentPage.value);
});
</script>
<template>
  <div class="wrapper">
    <div class="header-wrapper">
      <form-switch
        :need-task-complete="true"
        :need-domain="true"
        :need-search="true"
        @task-list="handleRefreshList"
      ></form-switch>
      <div class="line"></div>
      <div class="toolbar">
        <CmpButton type="primary" @click="handleAssign(selectionTaskList, assignTask.mul)"
          >批量分配</CmpButton
        >
      </div>
    </div>
    <div class="main-wrapper">
      <div class="line"></div>
      <el-table
        class="table"
        :data="tableData"
        style="width: 100%; overflow-x: hidden"
        empty-text="暂无数据"
        :row-style="{ height: '55px', overflow: 'hidden' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="30"
          :selectable="
            (row) =>
              row.taskStatus !== taskCompleteType.completed &&
              row.taskStatus !== taskCompleteType.executed &&
              row.taskStatus !== taskCompleteType.returned
          "
        />
        <el-table-column type="index" :index="indexMethod" label="序号" width="60" align="center" />
        <el-table-column prop="klgName" label="任务名称" min-width="200">
          <template #default="scope">
            <el-tooltip
              placement="top"
              :content="scope.row.klgName"
              :raw-content="true"
              :show-after="200"
              effect="customized"
            >
              <span class="ck-content ellipsis-text-inline" v-html="scope.row.klgName"></span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="areaTitle" label="所属领域" width="180" align="center" />
        <el-table-column prop="createTime" label="提交时间" width="180" align="center" />
        <el-table-column prop="creatorName" label="提交人" width="100" align="center">
          <template #default="scope">
            <el-tooltip
              placement="top"
              :content="scope.row.creatorName"
              :raw-content="true"
              :show-after="200"
              effect="customized"
            >
              <span class="ellipsis-text-inline" v-html="scope.row.creatorName"></span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="任务状态" width="100" align="center">
          <template #default="scope">
            {{
              Object.keys(taskCompleteTypeDict).find(
                (key) => taskCompleteTypeDict[key] === scope.row.taskStatus
              )
            }}
          </template>
        </el-table-column>
        <el-table-column prop="handlerName" label="负责人" width="100" align="center">
          <template #default="scope">
            <el-tooltip
              placement="top"
              :content="scope.row.handlerName"
              :raw-content="true"
              :show-after="200"
              effect="customized"
            >
              <span class="ck-content ellipsis-text-inline" v-html="scope.row.handlerName"></span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center">
          <template #default="scope">
            <span class="operation">
              <el-button class="op-btn" type="primary" @click="handleCheck(scope.row)" text>
                查看
              </el-button>
              <el-button
                style="font-family: var(--text-font-family); font-weight: 400"
                type="primary"
                :disabled="
                  scope.row.taskStatus === taskCompleteType.completed ||
                  scope.row.taskStatus === taskCompleteType.executed ||
                  scope.row.taskStatus === taskCompleteType.returned
                "
                @click="handleAssign(scope.row, assignTask.one)"
                text
              >
                分配
              </el-button>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <my-flipper
        @change-page="handleChangePage"
        :current="currentPage"
        :page-size="pageSize"
        :total="total"
      ></my-flipper>
    </div>
  </div>
  <!-- other -->
  <check-drawer ref="drawerRef"></check-drawer>
  <assign-dialog ref="dialogRef" @refresh="handleRefreshList"></assign-dialog>
</template>
<style scoped>
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
.wrapper {
  width: 1200px;
  min-height: 750px;
  background-color: white;
  font-family: var(--text-family);

  .header-wrapper {
    width: 100%;
    .toolbar {
      margin: 7px 0;
      padding: 0 20px;
    }
  }
  .main-wrapper {
    padding: 0 10px;

    .table {
      --el-color-primary: var(--color-primary);
      :deep(.cell) {
        padding: 0 6px;
        line-height: 55px;
        max-height: 55px;
      }
      :deep(p) {
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .operation {
        padding: 0 10px;
        display: flex;

        .op-btn {
          cursor: pointer;
          color: var(--color-primary);
          font-family: var(--text-family);
          font-weight: 400;
          &:hover {
            font-weight: bold;
          }
        }
      }
    }
  }
}
:deep(.el-button--primary.is-text.is-disabled) {
  color: var(--color-boxborder);
}
</style>
