// 任务状态
export enum taskCompleteType {
  defualt = -1,
  pending = 1,
  assigned = 2,
  executed = 3,
  completed = 4,
  returned = 5,
  executing = 6
}
export const taskCompleteTypeDict: { [key: string]: number } = {
  全部任务状态: taskCompleteType.defualt,
  待分配: taskCompleteType.pending,
  已分配: taskCompleteType.assigned,
  已执行: taskCompleteType.executed,
  执行中: taskCompleteType.executing,
  已完成: taskCompleteType.completed,
  已退回: taskCompleteType.returned
};

export enum KlgType {
  All = 0,
  Factual = 1, // 事实
  Conceptual = 2, // 概念性知识
  Principles = 3, //原理性知识
  Procedural = 4, // 程序性知识
  Strategic = 5 // 策略性知识
}

export const KlgTypeDict: { [key: string]: number } = {
  事实性知识: KlgType.Factual,
  概念性知识: KlgType.Conceptual,
  原理性知识: KlgType.Principles,
  程序性知识: KlgType.Procedural,
  策略性知识: KlgType.Strategic
};
export const KlgTypeDictPlus: { [key: string]: number } = {
  全部知识: KlgType.All,
  事实性知识: KlgType.Factual,
  概念性知识: KlgType.Conceptual,
  原理性知识: KlgType.Principles,
  程序性知识: KlgType.Procedural,
  策略性知识: KlgType.Strategic
};

export enum refStatus {
  all = 2,
  noPublish = 0,
  publish = 1
}

export const refStatusDict: { [key: string]: number } = {
  全部: refStatus.all,
  未发布: refStatus.noPublish,
  已发布: refStatus.publish
};
export enum klgAuditType {
  all = -1,
  draft = 0,
  pending = 1,
  reviewing = 2,
  returned = 3,
  published = 4,
  withdrawn = 5,
  deleted = 6
}

export const klgAuditTypeDict: { [key: string]: number } = {
  全部状态: klgAuditType.all,
  草稿: klgAuditType.draft,
  待审核: klgAuditType.pending,
  审核中: klgAuditType.reviewing,
  已退回: klgAuditType.returned,
  已发布: klgAuditType.published,
  已撤回: klgAuditType.withdrawn,
  已删除待恢复: klgAuditType.deleted
};
export const hasPreDict: { [key: string]: number } = {
  全部: 2,
  '是(前驱知识)': 0,
  '否(前驱知识)': 1
};
export const sortOrderDict: { [key: string]: number } = {
  顺序: 1,
  倒序: 0
};
export const sortByDict: { [key: string]: number } = {
  创建时间: 0,
  修改时间: 1
};
export const searchRange: { [key: string]: number } = {
  当前领域: 1,
  递归查询: 0
};
export const klgProofCondSortDict: { [key: string]: number } = {
  内部条件: 0,
  外部条件: 1,
  输入条件: 2
};
export const auditTypeDict: { [key: string]: number } = {
  退回: 0,
  通过: 1
};
export const proofCondTypeDict: { [key: string]: number } = {
  输入条件: 2,
  内部条件: 0,
  外部条件: 1
};
export enum WorkerType {
  draft = 1,
  video = 2,
  exercise = 3,
  proof = 4
}
export enum QuestionType {
  what = 1,
  why = 2
}
export const QuestionTypeDict: { [key: string]: number } = {
  是什么: QuestionType.what,
  为什么: QuestionType.why
};
export enum TreeType {
  all = 0,
  edit = 1,
  show = 2,
  audit = 3
}
export enum PreKlgStatus {
  able = 0,
  unable = 1
}
export enum PreKlgFlag {
  manual = 0,
  auto = 1,
  outer = 2
}
export enum klgQuestionStatus {
  toConfirmed = 0, // 待确认
  confirmed = 1, // 已确认
  deleted = 2 // 已删除待恢复
}
export enum klgQuestionAble {
  disable = 0, // 失效
  able = 1 // 生效
}
export const ApprovalAuthorityList = ['无审核权限', '初级权限', '高级权限'];
export const ReferenceSortList = ['全部', '著作资料', '网络资料', '其他资料'];
export const MAX_PAGESIZE = 10; // 页大小
