// cosHelper.ts
import COS from "cos-js-sdk-v5"
import { getTemp<PERSON>ey<PERSON><PERSON> } from "@/apis/path/cos"

interface AuthOptions {
  Bucket: string
  Region: string
}

export interface PutObjectOptions {
  Bucket: string
  Region: string
  Key: string
  Body: Blob
}

interface GetObjectUrlOptions extends AuthOptions {
  Key: string
}

class CosHelper {
  private cos: COS
  // 构造器
  constructor() {
    this.cos = new COS({
      getAuthorization: (options, callback) => {
        // 获取临时key
        getTempKeyApi()
          .then((res) => {
            const data = res.data
            const credentials = {
              TmpSecretId: data.TmpSecretId,
              TmpSecretKey: data.TmpSecretKey,
              SecurityToken: data.SecurityToken,
              ExpiredTime: data.ExpiredTime,
              StartTime: data.StartTime,
            }
            callback(credentials) // 传递 credentials 对象
          })
          .catch((error) => {
            console.error("Failed to get temp key:", error)
            callback(null)
          })
      },
    })
  }
  // 从cos拿
  public async getObjectUrl({
    Bucket,
    Region,
    Key,
  }: GetObjectUrlOptions): Promise<string | undefined> {
    return new Promise((resolve, reject) => {
      this.cos.getObjectUrl({ Bucket, Region, Key }, function(err, data) {
        if (err) {
          reject(new Error(`Failed to get object URL: ${err}`))
        } else {
          const downloadUrl = data.Url + (data.Url.indexOf('?') > -1 ? '&' : '?') + 'response-content-disposition=attachment'
          resolve(downloadUrl);
        }
      })
    })
  }
  // 丢进cos
  public async putObject({
    Bucket,
    Region,
    Key,
    Body,
  }: PutObjectOptions): Promise<any> {
    try {
      const params = { Bucket, Region, Key, Body }
      const result = await this.cos.putObject(params)
      return result
    } catch (error) {
      throw new Error(`Failed to upload object: ${error}`)
    }
  }
  // 在cos删除
  public async deleteObject({
    Bucket,
    Region,
    Key,
  }: AuthOptions & { Key: string }): Promise<any> {
    try {
      const params = { Bucket, Region, Key }
      const result = await this.cos.deleteObject(params)
      return result
    } catch (error) {
      throw new Error(`Failed to delete object: ${error}`)
    }
  }
}

export default CosHelper
