<script setup lang="ts">
import editKlgStep1 from '@/view/other/editKlg/editKlgStep1.vue';
import editKlgStep2 from '@/view/other/editKlg/editKlgStep2.vue';
import editKlgStep3 from '@/view/other/editKlg/editKlgStep3.vue';
import { KlgTypeDict, KlgType } from '@/utils/constant';
import { AreaListItem, RefListItem, ProofListItem } from '@/utils/type';
import { getKlgDetailApi, getKlgRefListApi, getProofBlockListApi } from '@/apis/path/klg';
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { findKeyByValue } from '@/utils/func';

const blankAreaList: AreaListItem[] = [];
const blankRefList: RefListItem[] = [];
const blankProofList: ProofListItem[] = [];
const blankKlgDetail = {
  id: 0,
  klgCode: '',
  sortId: 1,
  sortTitle: '',
  title: '',
  sysTitles: '',
  cnt: '',
  tagIds: '',
  status: 0,
  statusTime: null,
  statusWho: null,
  statusWhoid: null,
  isOk: 1,
  who: '',
  whoName: '',
  whoid: 0,
  notice: '',
  createTime: '',
  modifiedTime: ''
};
const blankProofBlock: ProofListItem = {
  klgProofBlockId: null,
  klgProofBlockOrderNum: 0,
  conclusion: '',
  klgProofCondList: [
    {
      klgProofCondId: null,
      sort: 2,
      refId: '',
      cnt: '',
      klgProofCondOrderNum: 0
    }
  ]
};
const route = useRoute();
const stepActive = ref(0);
const showStep3 = ref(false);

const klgDetail = ref(blankKlgDetail);
const klgRefList = ref(blankRefList);
const klgProofList = ref(blankProofList);
const klgAreaList = ref(blankAreaList);

// 获取klg详情
const getKlgDetail = () => {
  getKlgDetailApi(klgDetail.value.klgCode).then((res) => {
    if (res.success) {
      klgDetail.value = res.data.detail;
      if (!res.data.detail.sysTitles) klgDetail.value.sysTitles = '';
      klgDetail.value.sortTitle = findKeyByValue(klgDetail.value.sortId, KlgTypeDict) as string;
      if (res.data.areaList) {
        klgAreaList.value = res.data.areaList.map((item) => {
          return {
            areaCode: item.areaCode,
            label: item.title
          };
        });
      }
      if (KlgTypeDict[klgDetail.value.sortTitle] === KlgType.Principles) {
        showStep3.value = true;
      }
    }
  });
};
// 获取klg文献列表
const getKlgRefList = () => {
  getKlgRefListApi(klgDetail.value.klgCode).then((res) => {
    if (res.success) {
      klgRefList.value = res.data.klgToRefVoList;
    }
  });
};
// 获取klg论证块列表
const getKlgProofList = () => {
  getProofBlockListApi(klgDetail.value.klgCode).then((res) => {
    if (res.success) {
      if (res.data.klgProofBlocks.length !== 0) {
        klgProofList.value = res.data.klgProofBlocks;
        klgProofList.value.forEach((item) => {});
      } else {
        klgProofList.value = [blankProofBlock];
      }
      console.log('klgProofList', klgProofList.value);
    }
  });
};
// 处理展示step3
const handleShowStep3 = (flag: boolean) => {
  showStep3.value = flag;
};
// 处理上/下一步
const handleStep = (step: number) => {
  // 注意step为实际步骤-1, el-step从0开始
  stepActive.value = step;
};

// 处理刷新
const handleRefresh = (data: any) => {
  klgDetail.value.klgCode = data.klgCode;
  if (data.step === 1) {
    getKlgDetail();
  } else if (data.step === 2) {
    getKlgDetail();
    getKlgRefList();
  }
};

watch(
  () => route.query.klgCode,
  () => {
    const step = sessionStorage.getItem('step');
    if (step) stepActive.value = parseInt(step);
    else stepActive.value = 0;
    if (route.query.klgCode) {
      klgDetail.value.klgCode = route.query.klgCode.toString();
      getKlgDetail();
      getKlgRefList();
      getKlgProofList();
    } else {
    }
  },
  { deep: true, immediate: true }
);
watch(
  () => stepActive.value,
  (oldVal, newVal) => {
    if (oldVal !== newVal) {
      sessionStorage.setItem('step', stepActive.value.toString());
    }
  },
  { deep: true, immediate: true }
);
</script>
<template>
  <div class="wrapper">
    <editKlgStep1
      v-if="stepActive === 0"
      :show-step3="showStep3"
      :detail="klgDetail"
      @step="handleStep"
      @show-step3="handleShowStep3"
      @refresh="handleRefresh"
    ></editKlgStep1>
    <editKlgStep2
      :show-step3="showStep3"
      :detail="klgDetail"
      :areaList="klgAreaList"
      :refList="klgRefList"
      @step="handleStep"
      @refresh="handleRefresh"
      v-else-if="stepActive === 1"
    ></editKlgStep2>
    <editKlgStep3
      :show-step3="showStep3"
      :detail="klgDetail"
      :areaList="klgAreaList"
      :refList="klgRefList"
      :proofList="klgProofList"
      @step="handleStep"
      @refresh="handleRefresh"
      v-else-if="stepActive === 2"
    ></editKlgStep3>
  </div>
</template>
<style scoped>
.wrapper {
  --el-text-color-placeholder: var(--color-invalid);
}
:deep(img) {
  max-width: 100%;
  height: auto;
}
:deep(p) {
  margin: 0;
}
</style>
<style>
.primary {
  --el-color-primary: var(--color-primary);
}
</style>
