import { http } from "@/apis"
import type { APIResponse } from "@/utils/type"

interface page {
  current: number
  limit: number
}
export interface params2GetAuditList extends page {
  areaCode: string
  keyword: string
}
export interface params2GetMyAuditList extends page {
  type: number | null
  keyword: string
}
export interface params2GetAuditRecordList extends page {
  klgCode: string
}
export interface params2Audit {
  klgCode: string
  audit_opinion: string
  auditResult: number
}
export interface params2AddMyPool {}
// 获取审核池列表
export function getAuditKlgListApi(
  params: params2GetAuditList
): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/process/instance/getAll`,
    data: params,
  })
}
// 获取我的审核池列表
export function getMyAuditKlgListApi(
  params: params2GetMyAuditList
): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/process/instance/getOne`,
    data: params,
  })
}
// 添加到我的审核
export function addMyAuditPoolApi(list: string[]): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/process/instance/save`,
    data: list,
  })
}
// 换人审核
export function changeAuditKlgApi(list: string[]): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/process/instance/exProcess`,
    data: list,
  })
}
// 获取审核信息
export function getAuditKlgInfoApi(id: string): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/process/instance/getOneAction/${id}`,
  })
}
// 获取审核记录
export function getAuditRecordListApi(params: params2GetAuditRecordList): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/process/record/detail`,
    data: params
  })
}
// 审核
export function auditKlgApi(params: params2Audit): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/process/instance/process`,
    data: params
  })
}

