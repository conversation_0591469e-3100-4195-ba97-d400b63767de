<script setup lang="ts">
import FormSwitch from '@/components/FormSwitch.vue';
import CmpButton from '@/components/CmpButton.vue';
import MyFlipper from '@/components/MyFlipper.vue';
import AuditDialog from '@/view/audit/components/AuditDialog.vue';

import { onMounted, onUnmounted, ref } from 'vue';
import { KlgTypeDict, MAX_PAGESIZE } from '@/utils/constant';
import { getAuditKlgListApi } from '@/apis/path/audit';
import type { params2GetAuditList } from '@/apis/path/audit';
import { findKeyByValue } from '@/utils/func';
import { ElMessage } from 'element-plus';
import { emitter } from '@/utils/emitter';
import { Event } from '@/types/event';

const currentPage = ref(1);
const pageSize = MAX_PAGESIZE;
const total = ref(0);
const dialogRef = ref();
const canShow = ref();

const selectionKlgList = ref<any[]>([]);
const tableData = ref();
const curDomain = ref({
  areaCode: '',
  title: '',
  keyword: ''
});

// 处理刷新树
const handleRefreshTree = () => {
  emitter.emit(Event.TREE_REFRESH, true);
};
// 计算列表index
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize + index + 1;
};
// 处理表格选择
const handleSelectionChange = (list: []) => {
  selectionKlgList.value = list;
};
// 处理换页
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  getAuditKlgList();
};
// 获取待审核列表
const getAuditKlgList = () => {
  if (canShow.value) {
    const params: params2GetAuditList = {
      current: currentPage.value,
      limit: pageSize,
      areaCode: curDomain.value.areaCode,
      keyword: curDomain.value.keyword
    };
    getAuditKlgListApi(params).then((res) => {
      if (res.success) {
        total.value = res.data.total;
        tableData.value = res.data.list;
      }
    });
  } else {
  }
};
// 处理搜索
const handleSearch = (data: any) => {
  curDomain.value.keyword = data.keyword;
  getAuditKlgList();
};
// 处理dialog
const handleDialog = () => {
  if (selectionKlgList.value.length !== 0) {
    dialogRef.value.showDialog(0, selectionKlgList.value);
  } else {
    ElMessage.warning('请至少选择一个知识！');
  }
};
// 处理提交
const handleSubmitAudit = (flag: boolean) => {
  if (flag) {
    getAuditKlgList();
  }
};

// 处理响应
const handleEmitCheckNode = (data: any) => {
  curDomain.value.areaCode = data.areaCode;
  curDomain.value.title = data.label;
  canShow.value = data.isEditKlg;
  getAuditKlgList();
};
onMounted(() => {
  handleRefreshTree();
  emitter.on(Event.CHECK_TREE_NODE, handleEmitCheckNode);
});
onUnmounted(() => {
  emitter.off(Event.CHECK_TREE_NODE, handleEmitCheckNode);
});
</script>
<template>
  <div class="wrapper">
    <div class="header">
      <span class="header-title">{{ curDomain.title }}</span>
      <FormSwitch :need-audit-klg="true" @audit="handleSearch"></FormSwitch>
    </div>
    <div class="line"></div>
    <div v-if="!canShow" class="background-image-container">
      <img src="/static/audit-glass.png" draggable="false" style="user-select: none" />
      <span class="nopermission-tip">暂无查看权限</span>
    </div>
    <div class="main-container" v-else>
      <div class="toolbar">
        <CmpButton type="primary" @click="handleDialog">我来审核</CmpButton>
      </div>
      <div class="line"></div>
      <el-table
        class="table"
        :data="tableData"
        style="width: 100%; overflow-x: hidden"
        empty-text="暂无数据"
        :row-style="{ height: '50px', overflow: 'hidden' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="30" />
        <el-table-column type="index" :index="indexMethod" label="序号" width="60" align="center" />
        <el-table-column label="知识名称" min-width="200" height="55">
          <template #default="scope">
            <el-tooltip
              placement="top"
              :content="(scope.row.title)"
              :raw-content="true"
              :show-after="200"
              effect="customized"
            >
              <span
                class="ck-content ellipsis-text-inline"
                v-html="(scope.row.title)"
              ></span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="知识类型" width="150" align="center">
          <template #default="scope">
            {{ findKeyByValue(scope.row.sortId, KlgTypeDict) }}
          </template>
        </el-table-column>
        <el-table-column label="作者" min-width="70" align="center">
          <template #default="scope">
            <el-tooltip
              placement="top"
              :content="scope.row.whoName"
              :raw-content="true"
              :show-after="200"
              effect="customized"
            >
              <span class="ck-content ellipsis-text-inline" v-html="scope.row.whoName"></span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="提交日期" width="200" align="center" />
        <el-table-column prop="currActionName" label="审核环节" width="100" align="center" />
      </el-table>
      <my-flipper
        @change-page="handleChangePage"
        :current="currentPage"
        :page-size="pageSize"
        :total="total"
      ></my-flipper>
    </div>
  </div>
  <!-- other -->
  <AuditDialog ref="dialogRef" @submit="handleSubmitAudit"></AuditDialog>
</template>
<style scoped>
::v-deep(p) {
  margin: 0px;
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}

.wrapper {
  width: 62.5vw;
  min-height: 750px;
  background-color: white;
  .header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .header-title {
      display: flex;
      align-items: center;
      margin-left: 20px;
    }
  }
  .background-image-container {
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
    .nopermission-tip {
      position: absolute;
      top: 300px;
      border: 1px solid var(--color-boxborder);
      background-color: white;
      padding: 10px;
      border-radius: 3px;
      font-size: 18px;
      font-weight: 800;
      color: var(--color-black);
    }
  }
  .main-container {
    padding: 0 20px;
    .toolbar {
      margin: 10px 0;
    }
  }
}
</style>
